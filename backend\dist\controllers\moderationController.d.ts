import { Request, Response, NextFunction } from 'express';
interface AuthRequest extends Request {
    user?: {
        id: string;
        name: string;
        email: string;
        role?: string;
    };
}
export declare const getModerationQueue: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getModerationStats: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const reviewModerationItem: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const triggerCleanup: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getCleanupStats: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export {};
//# sourceMappingURL=moderationController.d.ts.map