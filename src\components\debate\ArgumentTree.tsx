import React, { useState } from 'react';
import { Argument, VoteSide, Evidence } from '../../types';
import { ParticipantIcon } from '../ui/ParticipantIcon';
import { VoteIcon, LinkIcon, ChevronDownIcon, ChevronRightIcon, PlusIcon, ChatIcon } from '../ui/icons';
import { ValidationSummary, QualityGuidelines, useContentValidation } from '../ui/ValidationFeedback';


interface ArgumentTreeProps {
  argument: Argument;
  onVote: (argumentId: string, voteType: 'up' | 'down') => void;
  onAddSubArgument: (parentId: string, text: string, isChallenge: boolean) => void;
  onAddEvidence: (argumentId: string, evidence: Partial<Evidence>) => void;
  onVoteOnEvidence?: (argumentId: string, evidenceId: string, vote: 'accurate' | 'inaccurate') => void;
  onArgumentClick?: (argument: Argument) => void;
  onChatClick?: (argument: Argument) => void;
  currentUserId?: string;
  depth?: number;
  maxDepth?: number;
}

interface EvidenceItemProps {
  evidence: Evidence;
  onVoteOnEvidence?: (evidenceId: string, vote: 'accurate' | 'inaccurate') => void;
  currentUserId?: string;
}

const EvidenceItem: React.FC<EvidenceItemProps> = ({ evidence, onVoteOnEvidence, currentUserId }) => {
  const getEvidenceIcon = (type: Evidence['type']) => {
    switch (type) {
      case 'statistic': return '📊';
      case 'study': return '🔬';
      case 'expert_opinion': return '👨‍🎓';
      case 'case_study': return '📋';
      case 'historical_fact': return '📚';
      default: return '📄';
    }
  };

  const getVerificationColor = (status: Evidence['verificationStatus']) => {
    switch (status) {
      case 'verified': return 'text-green-400';
      case 'pending': return 'text-yellow-400';
      case 'disputed': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  // Check if current user has already voted
  const userVote = currentUserId && evidence.votedBy
    ? evidence.votedBy.find(vote => vote.userId === currentUserId)
    : null;
  const hasVoted = !!userVote;
  const canVote = onVoteOnEvidence && currentUserId;

  return (
    <div className="bg-brand-bg/30 rounded-lg p-3 border-l-2 border-brand-primary/30">
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2">
          <span className="text-lg">{getEvidenceIcon(evidence.type)}</span>
          <span className="text-sm font-medium text-brand-text capitalize">
            {evidence.type.replace('_', ' ')}
          </span>
        </div>
        <span className={`text-xs font-medium ${getVerificationColor(evidence.verificationStatus)}`}>
          {evidence.verificationStatus}
        </span>
      </div>
      <p className="text-sm text-brand-text-light mb-2">{evidence.content}</p>
      <div className="flex items-center justify-between mb-2">
        <a
          href={evidence.source.url}
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center gap-1 text-brand-primary/80 hover:text-brand-primary text-xs transition-colors"
        >
          <LinkIcon className="w-3 h-3" />
          <span>{evidence.source.title}</span>
        </a>
        <div className="flex items-center gap-2 text-xs text-brand-text-secondary">
          <ParticipantIcon participant={evidence.addedBy} size="xs" />
          <span>{evidence.addedBy.name}</span>
        </div>
      </div>

      {/* Community Verification Voting */}
      <div className="flex items-center justify-between pt-2 border-t border-brand-primary/10">
        <div className="flex items-center gap-3">
          {onVoteOnEvidence && currentUserId && (
            <div className="flex items-center gap-1">
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onVoteOnEvidence(evidence.id, 'accurate');
                }}
                disabled={!canVote}
                className={`flex items-center gap-1 px-2 py-1 rounded text-xs transition-colors ${
                  userVote?.vote === 'accurate'
                    ? 'bg-green-500/30 text-green-300 border border-green-500/50'
                    : canVote
                      ? 'bg-green-500/10 text-green-400 hover:bg-green-500/20'
                      : 'bg-gray-500/10 text-gray-500 cursor-not-allowed'
                }`}
                title={userVote?.vote === 'accurate' ? 'You voted accurate (click to change)' : 'Vote as accurate'}
                type="button"
              >
                <span>✓</span>
                <span>{evidence.accurateVotes || 0}</span>
              </button>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onVoteOnEvidence(evidence.id, 'inaccurate');
                }}
                disabled={!canVote}
                className={`flex items-center gap-1 px-2 py-1 rounded text-xs transition-colors ${
                  userVote?.vote === 'inaccurate'
                    ? 'bg-red-500/30 text-red-300 border border-red-500/50'
                    : canVote
                      ? 'bg-red-500/10 text-red-400 hover:bg-red-500/20'
                      : 'bg-gray-500/10 text-gray-500 cursor-not-allowed'
                }`}
                title={userVote?.vote === 'inaccurate' ? 'You voted inaccurate (click to change)' : 'Vote as inaccurate'}
                type="button"
              >
                <span>✗</span>
                <span>{evidence.inaccurateVotes || 0}</span>
              </button>
            </div>
          )}
          {evidence.totalVotes > 0 && (
            <div className="text-xs text-brand-text-secondary">
              <span className="font-medium">{Math.round(evidence.verificationScore || 0)}%</span> accurate
              <span className="ml-1">({evidence.totalVotes} vote{evidence.totalVotes !== 1 ? 's' : ''})</span>
            </div>
          )}
          {hasVoted && (
            <div className="text-xs text-brand-text-secondary">
              You voted: <span className={userVote?.vote === 'accurate' ? 'text-green-400' : 'text-red-400'}>
                {userVote?.vote}
              </span> <span className="text-gray-500">(click to change)</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export const ArgumentTree: React.FC<ArgumentTreeProps> = ({
  argument,
  onVote,
  onAddSubArgument,
  onAddEvidence,
  onVoteOnEvidence,
  onArgumentClick,
  onChatClick,
  currentUserId,
  depth = 0,
  maxDepth = 3
}) => {
  const [isExpanded, setIsExpanded] = useState(depth < 2); // Auto-expand first 2 levels
  const [showAddSubArgument, setShowAddSubArgument] = useState(false);
  const [showAddEvidence, setShowAddEvidence] = useState(false);
  const [showStrengthBreakdown, setShowStrengthBreakdown] = useState(false);
  const [showQualityGuidelines, setShowQualityGuidelines] = useState(false);
  const [isEvidenceExpanded, setIsEvidenceExpanded] = useState(false);
  const [newSubArgumentText, setNewSubArgumentText] = useState('');
  const [isChallenge, setIsChallenge] = useState(false);
  const [newEvidence, setNewEvidence] = useState({
    type: 'other' as Evidence['type'],
    content: '',
    sourceTitle: '',
    sourceUrl: ''
  });

  const { validateArgument, validateEvidence } = useContentValidation();

  const isFor = argument.side === VoteSide.FOR;
  const borderColor = isFor ? 'border-green-500/50' : 'border-red-500/50';
  const challengeColor = argument.isChallenge ? 'border-orange-500/50' : borderColor;
  
  // Calculate indentation based on depth
  const indentClass = depth > 0 ? `ml-${Math.min(depth * 4, 12)}` : '';
  
  const hasSubArguments = argument.subArguments && argument.subArguments.length > 0;
  const hasEvidence = argument.evidence && argument.evidence.length > 0;

  const handleAddSubArgument = () => {
    if (newSubArgumentText.trim()) {
      onAddSubArgument(argument.id, newSubArgumentText.trim(), isChallenge);
      setNewSubArgumentText('');
      setIsChallenge(false);
      setShowAddSubArgument(false);
    }
  };

  const handleAddEvidence = () => {
    if (newEvidence.content.trim() && newEvidence.sourceTitle.trim() && newEvidence.sourceUrl.trim()) {
      const evidence: Partial<Evidence> = {
        type: newEvidence.type,
        content: newEvidence.content.trim(),
        source: {
          id: `ref-${Date.now()}`,
          title: newEvidence.sourceTitle.trim(),
          url: newEvidence.sourceUrl.trim(),
          type: 'other'
        }
      };

      onAddEvidence(argument.id, evidence);
      setNewEvidence({
        type: 'other',
        content: '',
        sourceTitle: '',
        sourceUrl: ''
      });
      setShowAddEvidence(false);
    }
  };

  const getStrengthColor = (score: number) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    if (score >= 40) return 'text-orange-400';
    return 'text-red-400';
  };

  const getStrengthBreakdown = () => {
    const netVotes = Math.max(0, argument.upvotes - argument.downvotes);
    const totalVotes = argument.upvotes + argument.downvotes;
    const voteRatio = totalVotes > 0 ? argument.upvotes / totalVotes : 0.5;
    const voteScore = Math.log(netVotes + 1) * 2 + (voteRatio - 0.5) * 10;

    let evidenceScore = 0;
    if (argument.evidence && argument.evidence.length > 0) {
      const weightedEvidenceSum = argument.evidence.reduce((sum, ev) => {
        let verificationMultiplier = 1;
        switch (ev.verificationStatus) {
          case 'verified': verificationMultiplier = 1.2; break;
          case 'pending': verificationMultiplier = 1.0; break;
          case 'disputed': verificationMultiplier = 0.5; break;
          case 'unverified': verificationMultiplier = 0.8; break;
        }
        // Base evidence value of 5 (equivalent to old default strength)
        return sum + (5 * verificationMultiplier);
      }, 0);
      evidenceScore = weightedEvidenceSum / argument.evidence.length;
    }

    const referenceBonus = argument.references && argument.references.length > 0
      ? Math.min(5, argument.references.length * 1.5)
      : 0;

    let subArgumentScore = 0;
    if (argument.subArguments && argument.subArguments.length > 0) {
      const supporting = argument.subArguments.filter(sub => !sub.isChallenge);
      const challenging = argument.subArguments.filter(sub => sub.isChallenge);

      const supportingAvg = supporting.length > 0
        ? supporting.reduce((sum, sub) => sum + sub.strengthScore, 0) / supporting.length
        : 0;
      const challengingAvg = challenging.length > 0
        ? challenging.reduce((sum, sub) => sum + sub.strengthScore, 0) / challenging.length
        : 0;

      const supportingBoost = supportingAvg * Math.min(1, supporting.length * 0.3) * 0.6;
      const challengingReduction = challengingAvg * Math.min(1, challenging.length * 0.25) * 0.4;

      subArgumentScore = supportingBoost - challengingReduction;
    }

    return {
      baseStrength: 30,
      voteContribution: Math.round(voteScore * 0.3),
      evidenceContribution: Math.round(evidenceScore * 4),
      subArgumentContribution: Math.round(subArgumentScore),
      referenceBonus: Math.round(referenceBonus),
      total: argument.strengthScore
    };
  };

  return (
    <div className={`${indentClass} mb-4`}>
      <div className={`bg-brand-surface rounded-lg p-4 border-l-4 ${challengeColor} shadow-md relative`}>
        {/* Argument Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3">
            <ParticipantIcon participant={argument.author} size="sm" />
            <span className="font-semibold text-brand-text">{argument.author.name}</span>
            {argument.isChallenge && (
              <span className="text-xs bg-orange-500/20 text-orange-400 px-2 py-1 rounded-full">
                Challenge
              </span>
            )}
          </div>
          <div className="flex items-center gap-4">
            <span className={`font-bold text-sm ${isFor ? 'text-green-400' : 'text-red-400'}`}>
              {isFor ? 'FOR' : 'AGAINST'}
            </span>
            <div className="flex items-center gap-2">
              {(() => {
                // Check if current user has voted on this argument
                const userVote = currentUserId && argument.userVotes
                  ? argument.userVotes.find(vote => vote.userId === currentUserId)
                  : null;

                return (
                  <>
                    <button
                      onClick={() => onVote(argument.id, 'up')}
                      className={`transition-colors w-6 h-6 flex items-center justify-center rounded ${
                        userVote?.vote === 'up'
                          ? 'text-green-300 bg-green-500/20'
                          : 'text-green-400 hover:text-green-300'
                      }`}
                      title={userVote?.vote === 'up' ? 'You voted up (click to remove)' : 'Vote up'}
                    >
                      ▲
                    </button>
                    <span className="font-mono text-sm text-brand-text">{argument.upvotes - argument.downvotes}</span>
                    <button
                      onClick={() => onVote(argument.id, 'down')}
                      className={`transition-colors w-6 h-6 flex items-center justify-center rounded ${
                        userVote?.vote === 'down'
                          ? 'text-red-300 bg-red-500/20'
                          : 'text-red-400 hover:text-red-300'
                      }`}
                      title={userVote?.vote === 'down' ? 'You voted down (click to remove)' : 'Vote down'}
                    >
                      ▼
                    </button>
                  </>
                );
              })()}
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs text-brand-text-secondary">Strength:</span>
              <button
                onClick={() => setShowStrengthBreakdown(!showStrengthBreakdown)}
                className={`font-bold text-sm ${getStrengthColor(argument.strengthScore)} hover:opacity-80 transition-opacity`}
                title="Click to see strength breakdown"
              >
                {Math.round(argument.strengthScore)}%
              </button>
            </div>
            {onChatClick && (
              <button
                onClick={() => onChatClick(argument)}
                className="text-brand-text-secondary hover:text-brand-primary transition-colors p-1 rounded"
                title="Open discussion thread"
              >
                <ChatIcon className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        {/* Argument Text */}
        <p
          className={`text-brand-text-light mb-4 ${onArgumentClick ? 'cursor-pointer hover:text-brand-text transition-colors' : ''}`}
          onClick={() => onArgumentClick?.(argument)}
        >
          {argument.text}
        </p>

        {/* Strength Breakdown */}
        {showStrengthBreakdown && (
          <div className="bg-brand-bg/30 rounded-lg p-3 mb-3 text-sm">
            <h4 className="font-semibold text-brand-text mb-2">Strength Score Breakdown</h4>
            {(() => {
              const breakdown = getStrengthBreakdown();
              return (
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span className="text-brand-text-secondary">Base Strength:</span>
                    <span className="text-brand-text">{breakdown.baseStrength}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-brand-text-secondary">Votes ({argument.upvotes}↑ {argument.downvotes}↓):</span>
                    <span className="text-brand-text">+{breakdown.voteContribution}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-brand-text-secondary">Evidence ({argument.evidence?.length || 0}):</span>
                    <span className="text-brand-text">+{breakdown.evidenceContribution}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-brand-text-secondary">Sub-Arguments ({argument.subArguments?.length || 0}):</span>
                    <span className={breakdown.subArgumentContribution >= 0 ? "text-green-400" : "text-red-400"}>
                      {breakdown.subArgumentContribution >= 0 ? '+' : ''}{breakdown.subArgumentContribution}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-brand-text-secondary">References ({argument.references?.length || 0}):</span>
                    <span className="text-brand-text">+{breakdown.referenceBonus}</span>
                  </div>
                  <div className="border-t border-brand-surface-light pt-1 mt-2">
                    <div className="flex justify-between font-semibold">
                      <span className="text-brand-text">Total Strength:</span>
                      <span className={getStrengthColor(breakdown.total)}>{breakdown.total}%</span>
                    </div>
                  </div>
                </div>
              );
            })()}
          </div>
        )}

        {/* References */}
        {argument.references.length > 0 && (
          <div className="mb-4">
            <h4 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2">References</h4>
            <ul className="space-y-1">
              {argument.references.map(ref => (
                <li key={ref.id}>
                  <a href={ref.url} target="_blank" rel="noopener noreferrer" 
                     className="flex items-center gap-2 text-brand-primary/80 hover:text-brand-primary text-sm transition-colors">
                    <LinkIcon className="w-4 h-4" />
                    <span>{ref.title}</span>
                    {ref.type !== 'other' && (
                      <span className="text-xs text-brand-text-secondary">({ref.type})</span>
                    )}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Evidence - Collapsible */}
        {hasEvidence && (
          <div className="mb-4">
            <button
              onClick={() => setIsEvidenceExpanded(!isEvidenceExpanded)}
              className="flex items-center gap-2 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2 hover:text-gray-300 transition-colors"
            >
              {isEvidenceExpanded ? (
                <ChevronDownIcon className="w-3 h-3" />
              ) : (
                <ChevronRightIcon className="w-3 h-3" />
              )}
              Evidence ({argument.evidence.length})
            </button>
            {isEvidenceExpanded ? (
              <div className="space-y-2">
                {argument.evidence.map(evidence => (
                  <EvidenceItem
                    key={evidence.id}
                    evidence={evidence}
                    onVoteOnEvidence={onVoteOnEvidence ? (evidenceId, vote) => onVoteOnEvidence(argument.id, evidenceId, vote) : undefined}
                    currentUserId={currentUserId}
                  />
                ))}
              </div>
            ) : (
              <div className="text-xs text-brand-text-secondary pl-5">
                {argument.evidence.slice(0, 2).map((evidence, index) => (
                  <div key={evidence.id} className="flex items-start gap-2 mb-2">
                    <span className="flex-shrink-0 mt-0.5">{evidence.type === 'statistic' ? '📊' : evidence.type === 'study' ? '🔬' : evidence.type === 'expert_opinion' ? '👨‍🎓' : evidence.type === 'case_study' ? '📋' : evidence.type === 'historical_fact' ? '📚' : '📄'}</span>
                    <span className="flex-1 truncate whitespace-nowrap overflow-hidden">{evidence.content}</span>
                  </div>
                ))}
                {argument.evidence.length > 2 && (
                  <div className="text-brand-text-secondary/70 italic">
                    +{argument.evidence.length - 2} more...
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center gap-2 mb-4">
          <button
            onClick={() => setShowAddSubArgument(!showAddSubArgument)}
            className="flex items-center gap-1 text-brand-primary hover:text-brand-primary-light text-sm transition-colors"
            disabled={depth >= maxDepth}
          >
            <PlusIcon className="w-4 h-4" />
            Add Sub-Argument
          </button>
          <button
            onClick={() => setShowAddEvidence(!showAddEvidence)}
            className="flex items-center gap-1 text-brand-secondary hover:text-brand-secondary-light text-sm transition-colors"
          >
            <PlusIcon className="w-4 h-4" />
            Add Evidence
          </button>
          {hasSubArguments && (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="flex items-center gap-1 text-brand-text-secondary hover:text-white text-sm transition-colors"
            >
              {isExpanded ? <ChevronDownIcon className="w-4 h-4" /> : <ChevronRightIcon className="w-4 h-4" />}
              {argument.subArguments.length} Sub-Arguments
            </button>
          )}
        </div>

        {/* Add Sub-Argument Form */}
        {showAddSubArgument && (
          <div className="bg-brand-bg/50 rounded-lg p-3 mb-4">
            {(() => {
              const validation = validateArgument(newSubArgumentText);
              return (
                <>
                  <ValidationSummary
                    errors={validation.errors}
                    warnings={validation.warnings}
                  />
                  <textarea
                    value={newSubArgumentText}
                    onChange={(e) => setNewSubArgumentText(e.target.value)}
                    placeholder="Enter your sub-argument..."
                    className="w-full bg-brand-surface border border-brand-surface-light rounded-lg p-3 text-brand-text resize-none"
                    rows={3}
                  />
                  <div className="flex items-center justify-between mt-2">
                    <div className="flex items-center gap-4">
                      <label className="flex items-center gap-2 text-sm text-brand-text">
                        <input
                          type="checkbox"
                          checked={isChallenge}
                          onChange={(e) => setIsChallenge(e.target.checked)}
                          className="rounded"
                        />
                        This challenges the parent argument
                      </label>
                      <button
                        onClick={() => setShowQualityGuidelines(!showQualityGuidelines)}
                        className="text-xs text-brand-text-secondary hover:text-brand-text transition-colors"
                      >
                        📋 Guidelines
                      </button>
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={() => setShowAddSubArgument(false)}
                        className="px-3 py-1 text-sm text-brand-text-secondary hover:text-white transition-colors"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={handleAddSubArgument}
                        disabled={!newSubArgumentText.trim() || validation.errors.length > 0}
                        className="px-3 py-1 bg-brand-primary hover:bg-brand-primary-dark text-white text-sm rounded transition-colors disabled:opacity-50"
                      >
                        Add
                      </button>
                    </div>
                  </div>
                  {showQualityGuidelines && (
                    <QualityGuidelines className="mt-3" />
                  )}
                </>
              );
            })()}
          </div>
        )}

        {/* Add Evidence Form */}
        {showAddEvidence && (
          <div className="bg-brand-bg/50 rounded-lg p-3 mb-4">
            {(() => {
              const validation = validateEvidence(newEvidence.content, newEvidence.sourceUrl, newEvidence.sourceTitle);
              return (
                <>
                  <ValidationSummary
                    errors={validation.errors}
                    warnings={validation.warnings}
                  />
                  <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-brand-text mb-1">Evidence Type</label>
                <select
                  value={newEvidence.type}
                  onChange={(e) => setNewEvidence(prev => ({ ...prev, type: e.target.value as Evidence['type'] }))}
                  className="w-full bg-brand-surface border border-brand-surface-light rounded-lg p-2 text-brand-text"
                >
                  <option value="statistic">Statistic</option>
                  <option value="study">Study</option>
                  <option value="expert_opinion">Expert Opinion</option>
                  <option value="case_study">Case Study</option>
                  <option value="historical_fact">Historical Fact</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-brand-text mb-1">Evidence Content</label>
                <textarea
                  value={newEvidence.content}
                  onChange={(e) => setNewEvidence(prev => ({ ...prev, content: e.target.value }))}
                  placeholder="Describe the evidence..."
                  className="w-full bg-brand-surface border border-brand-surface-light rounded-lg p-3 text-brand-text resize-none"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-brand-text mb-1">Source Title</label>
                  <input
                    type="text"
                    value={newEvidence.sourceTitle}
                    onChange={(e) => setNewEvidence(prev => ({ ...prev, sourceTitle: e.target.value }))}
                    placeholder="Source title..."
                    className="w-full bg-brand-surface border border-brand-surface-light rounded-lg p-2 text-brand-text"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-brand-text mb-1">Source URL</label>
                  <input
                    type="url"
                    value={newEvidence.sourceUrl}
                    onChange={(e) => setNewEvidence(prev => ({ ...prev, sourceUrl: e.target.value }))}
                    placeholder="https://..."
                    className="w-full bg-brand-surface border border-brand-surface-light rounded-lg p-2 text-brand-text"
                  />
                </div>
              </div>


                  </div>

                  <div className="flex justify-between items-center mt-4">
                    <button
                      onClick={() => setShowQualityGuidelines(!showQualityGuidelines)}
                      className="text-xs text-brand-text-secondary hover:text-brand-text transition-colors"
                    >
                      📋 Guidelines
                    </button>
                    <div className="flex gap-2">
                      <button
                        onClick={() => setShowAddEvidence(false)}
                        className="px-3 py-1 text-sm text-brand-text-secondary hover:text-white transition-colors"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={handleAddEvidence}
                        disabled={!newEvidence.content.trim() || !newEvidence.sourceTitle.trim() || !newEvidence.sourceUrl.trim() || validation.errors.length > 0}
                        className="px-3 py-1 bg-brand-secondary hover:bg-brand-secondary-dark text-white text-sm rounded transition-colors disabled:opacity-50"
                      >
                        Add Evidence
                      </button>
                    </div>
                  </div>
                  {showQualityGuidelines && (
                    <QualityGuidelines className="mt-3" />
                  )}
                </>
              );
            })()}
          </div>
        )}
      </div>

      {/* Sub-Arguments - Only show direct children, no nesting */}
      {isExpanded && hasSubArguments && depth === 0 && (
        <div className="mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-stretch">
            {/* Supporting Sub-Arguments (FOR) */}
            <div className="flex flex-col h-full">
              <h5 className="text-sm font-medium text-success mb-2 flex items-center gap-2">
                <span className="w-2 h-2 bg-success rounded-full"></span>
                Supporting ({argument.subArguments.filter(sub => sub.side === VoteSide.FOR).length})
              </h5>
              <div className="flex-1 flex flex-col gap-2">
                {argument.subArguments
                  .filter(sub => sub.side === VoteSide.FOR)
                  .sort((a, b) => ((b.upvotes || 0) - (b.downvotes || 0)) - ((a.upvotes || 0) - (a.downvotes || 0)))
                  .map(subArg => (
                    <div
                      key={subArg.id}
                      className="border border-brand-border rounded-lg p-3 hover:border-success/50 transition-colors bg-brand-bg hover:bg-success/5 flex flex-col h-full"
                    >
                      <div className="flex items-start justify-between mb-1">
                        <div className="flex items-center gap-2">
                          <ParticipantIcon participant={subArg.author} size="sm" />
                          <span className="text-sm font-medium text-brand-text">
                            {subArg.author.name}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            {(() => {
                              // Check if current user has voted on this sub-argument
                              const userVote = currentUserId && subArg.userVotes
                                ? subArg.userVotes.find(vote => vote.userId === currentUserId)
                                : null;

                              return (
                                <>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      onVote(subArg.id, 'up');
                                    }}
                                    className={`transition-colors text-sm w-5 h-5 flex items-center justify-center rounded ${
                                      userVote?.vote === 'up'
                                        ? 'text-green-300 bg-green-500/20'
                                        : 'text-green-400 hover:text-green-300'
                                    }`}
                                    title={userVote?.vote === 'up' ? 'You voted up (click to remove)' : 'Vote up'}
                                  >
                                    ▲
                                  </button>
                                  <span className="text-sm font-medium text-brand-text">
                                    {(subArg.upvotes || 0) - (subArg.downvotes || 0)}
                                  </span>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      onVote(subArg.id, 'down');
                                    }}
                                    className={`transition-colors text-sm w-5 h-5 flex items-center justify-center rounded ${
                                      userVote?.vote === 'down'
                                        ? 'text-red-300 bg-red-500/20'
                                        : 'text-red-400 hover:text-red-300'
                                    }`}
                                    title={userVote?.vote === 'down' ? 'You voted down (click to remove)' : 'Vote down'}
                                  >
                                    ▼
                                  </button>
                                </>
                              );
                            })()}
                          </div>
                          <button
                            onClick={() => onArgumentClick?.(subArg)}
                            className="text-xs text-brand-text-secondary hover:text-brand-text transition-colors px-2 py-1 rounded"
                          >
                            View
                          </button>
                        </div>
                      </div>
                      <p className="text-sm text-brand-text-light flex-1">{subArg.text}</p>
                      {subArg.subArguments && subArg.subArguments.length > 0 && (
                        <div className="text-xs text-brand-text-secondary">
                          {subArg.subArguments.length} sub-argument{subArg.subArguments.length !== 1 ? 's' : ''}
                        </div>
                      )}
                    </div>
                  ))}
                {argument.subArguments.filter(sub => sub.side === VoteSide.FOR).length === 0 && (
                  <div className="text-center py-4 text-brand-text-light border border-dashed border-brand-border rounded-lg text-sm flex items-center justify-center h-full">
                    No supporting arguments yet
                  </div>
                )}
              </div>
            </div>

            {/* Challenging Sub-Arguments (AGAINST) */}
            <div className="flex flex-col h-full">
              <h5 className="text-sm font-medium text-danger mb-2 flex items-center gap-2">
                <span className="w-2 h-2 bg-danger rounded-full"></span>
                Challenging ({argument.subArguments.filter(sub => sub.side === VoteSide.AGAINST).length})
              </h5>
              <div className="flex-1 flex flex-col gap-2">
                {argument.subArguments
                  .filter(sub => sub.side === VoteSide.AGAINST)
                  .sort((a, b) => ((b.upvotes || 0) - (b.downvotes || 0)) - ((a.upvotes || 0) - (a.downvotes || 0)))
                  .map(subArg => (
                    <div
                      key={subArg.id}
                      className="border border-brand-border rounded-lg p-3 hover:border-danger/50 transition-colors bg-brand-bg hover:bg-danger/5 flex flex-col h-full"
                    >
                      <div className="flex items-start justify-between mb-1">
                        <div className="flex items-center gap-2">
                          <ParticipantIcon participant={subArg.author} size="sm" />
                          <span className="text-sm font-medium text-brand-text">
                            {subArg.author.name}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            {(() => {
                              // Check if current user has voted on this sub-argument
                              const userVote = currentUserId && subArg.userVotes
                                ? subArg.userVotes.find(vote => vote.userId === currentUserId)
                                : null;

                              return (
                                <>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      onVote(subArg.id, 'up');
                                    }}
                                    className={`transition-colors text-sm w-5 h-5 flex items-center justify-center rounded ${
                                      userVote?.vote === 'up'
                                        ? 'text-green-300 bg-green-500/20'
                                        : 'text-green-400 hover:text-green-300'
                                    }`}
                                    title={userVote?.vote === 'up' ? 'You voted up (click to remove)' : 'Vote up'}
                                  >
                                    ▲
                                  </button>
                                  <span className="text-sm font-medium text-brand-text">
                                    {(subArg.upvotes || 0) - (subArg.downvotes || 0)}
                                  </span>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      onVote(subArg.id, 'down');
                                    }}
                                    className={`transition-colors text-sm w-5 h-5 flex items-center justify-center rounded ${
                                      userVote?.vote === 'down'
                                        ? 'text-red-300 bg-red-500/20'
                                        : 'text-red-400 hover:text-red-300'
                                    }`}
                                    title={userVote?.vote === 'down' ? 'You voted down (click to remove)' : 'Vote down'}
                                  >
                                    ▼
                                  </button>
                                </>
                              );
                            })()}
                          </div>
                          <button
                            onClick={() => onArgumentClick?.(subArg)}
                            className="text-xs text-brand-text-secondary hover:text-brand-text transition-colors px-2 py-1 rounded"
                          >
                            View
                          </button>
                        </div>
                      </div>
                      <p className="text-sm text-brand-text-light flex-1">{subArg.text}</p>
                      {subArg.subArguments && subArg.subArguments.length > 0 && (
                        <div className="text-xs text-brand-text-secondary">
                          {subArg.subArguments.length} sub-argument{subArg.subArguments.length !== 1 ? 's' : ''}
                        </div>
                      )}
                    </div>
                  ))}
                {argument.subArguments.filter(sub => sub.side === VoteSide.AGAINST).length === 0 && (
                  <div className="text-center py-4 text-brand-text-light border border-dashed border-brand-border rounded-lg text-sm flex items-center justify-center h-full">
                    No challenging arguments yet
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
