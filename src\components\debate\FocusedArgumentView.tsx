import React, { useState } from 'react';
import { Argument, VoteSide, Evidence } from '../../types';
import { ParticipantIcon } from '../ui/ParticipantIcon';
import { LinkIcon, PlusIcon, ChatIcon } from '../ui/icons';
import { AddArgumentForm } from './AddArgumentForm';
import { useAuth } from '../../contexts/AuthContext';
import { ReportButton } from '../ui/ReportButton';

interface FocusedArgumentViewProps {
  argument: Argument;
  onVote: (argumentId: string, voteType: 'up' | 'down') => void;
  onAddSubArgument: (parentId: string, text: string, isChallenge: boolean) => void;
  onAddEvidence: (argumentId: string, evidence: Partial<Evidence>) => void;
  onVoteOnEvidence?: (argumentId: string, evidenceId: string, vote: 'accurate' | 'inaccurate') => void;
  onArgumentClick: (argument: Argument) => void;
  canAddArguments: boolean;
  onRefresh: () => void;
  currentUserId?: string;
  onChatClick: (argument: Argument) => void;
}

interface ReferenceListProps {
  references: Array<{ id: string; title: string; url: string; type: string }>;
}

const ReferenceList: React.FC<ReferenceListProps> = ({ references }) => (
  <div>
    <h4 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2">References</h4>
    <ul className="space-y-1">
      {references.map(ref => (
        <li key={ref.id}>
          <a href={ref.url} target="_blank" rel="noopener noreferrer"
             className="flex items-center gap-2 text-brand-primary/80 hover:text-brand-primary text-sm transition-colors">
            <LinkIcon className="w-4 h-4" />
            <span>{ref.title}</span>
          </a>
        </li>
      ))}
    </ul>
  </div>
);

interface EvidenceItemProps {
  evidence: Evidence;
  onVoteOnEvidence?: (evidenceId: string, vote: 'accurate' | 'inaccurate') => void;
  currentUserId?: string;
}

const EvidenceItem: React.FC<EvidenceItemProps> = ({ evidence, onVoteOnEvidence, currentUserId }) => {
  const getEvidenceIcon = (type: Evidence['type']) => {
    switch (type) {
      case 'statistic': return '📊';
      case 'study': return '🔬';
      case 'expert_opinion': return '👨‍🎓';
      case 'case_study': return '📋';
      case 'historical_fact': return '📚';
      default: return '📄';
    }
  };

  const getVerificationColor = (status: Evidence['verificationStatus']) => {
    switch (status) {
      case 'verified': return 'text-green-400';
      case 'pending': return 'text-yellow-400';
      case 'disputed': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  // Check if current user has already voted
  const userVote = currentUserId && evidence.votedBy
    ? evidence.votedBy.find(vote => vote.userId === currentUserId)
    : null;
  const hasVoted = !!userVote;
  const canVote = onVoteOnEvidence && currentUserId;

  return (
    <div className="bg-brand-bg/30 rounded-lg p-3 border-l-2 border-brand-primary/30">
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2">
          <span className="text-lg">{getEvidenceIcon(evidence.type)}</span>
          <span className="text-sm font-medium text-brand-text capitalize">
            {evidence.type.replace('_', ' ')}
          </span>
        </div>
        <span className={`text-xs font-medium ${getVerificationColor(evidence.verificationStatus)}`}>
          {evidence.verificationStatus}
        </span>
      </div>
      <p className="text-sm text-brand-text-light mb-2">{evidence.content}</p>
      <div className="flex items-center justify-between mb-2">
        <a
          href={evidence.source.url}
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center gap-1 text-brand-primary/80 hover:text-brand-primary text-xs transition-colors"
        >
          <LinkIcon className="w-3 h-3" />
          <span>{evidence.source.title}</span>
        </a>
        <div className="flex items-center gap-2 text-xs text-brand-text-secondary">
          <ParticipantIcon participant={evidence.addedBy} size="sm" />
          <span>{evidence.addedBy.name}</span>
          <ReportButton
            contentId={evidence.id}
            contentType="evidence"
            className="ml-2"
          />
        </div>
      </div>

      {/* Community Verification Voting */}
      <div className="flex items-center justify-between pt-2 border-t border-brand-primary/10">
        <div className="flex items-center gap-3">
          {onVoteOnEvidence && currentUserId && (
            <div className="flex items-center gap-1">
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onVoteOnEvidence(evidence.id, 'accurate');
                }}
                disabled={!canVote}
                className={`flex items-center gap-1 px-2 py-1 rounded text-xs transition-colors ${
                  userVote?.vote === 'accurate'
                    ? 'bg-green-500/30 text-green-300 border border-green-500/50'
                    : canVote
                      ? 'bg-green-500/10 text-green-400 hover:bg-green-500/20'
                      : 'bg-gray-500/10 text-gray-500 cursor-not-allowed'
                }`}
                title={userVote?.vote === 'accurate' ? 'You voted accurate (click to change)' : 'Vote as accurate'}
                type="button"
              >
                <span>✓</span>
                <span>{evidence.accurateVotes || 0}</span>
              </button>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onVoteOnEvidence(evidence.id, 'inaccurate');
                }}
                disabled={!canVote}
                className={`flex items-center gap-1 px-2 py-1 rounded text-xs transition-colors ${
                  userVote?.vote === 'inaccurate'
                    ? 'bg-red-500/30 text-red-300 border border-red-500/50'
                    : canVote
                      ? 'bg-red-500/10 text-red-400 hover:bg-red-500/20'
                      : 'bg-gray-500/10 text-gray-500 cursor-not-allowed'
                }`}
                title={userVote?.vote === 'inaccurate' ? 'You voted inaccurate (click to change)' : 'Vote as inaccurate'}
                type="button"
              >
                <span>✗</span>
                <span>{evidence.inaccurateVotes || 0}</span>
              </button>
            </div>
          )}
          {evidence.totalVotes > 0 && (
            <div className="text-xs text-brand-text-secondary">
              <span className="font-medium">{Math.round(evidence.verificationScore || 0)}%</span> accurate
              <span className="ml-1">({evidence.totalVotes} vote{evidence.totalVotes !== 1 ? 's' : ''})</span>
            </div>
          )}
          {hasVoted && (
            <div className="text-xs text-brand-text-secondary">
              You voted: <span className={userVote?.vote === 'accurate' ? 'text-green-400' : 'text-red-400'}>
                {userVote?.vote}
              </span> <span className="text-gray-500">(click to change)</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export const FocusedArgumentView: React.FC<FocusedArgumentViewProps> = ({
  argument,
  onVote,
  onAddSubArgument,
  onAddEvidence,
  onVoteOnEvidence,
  onArgumentClick,
  canAddArguments,
  onRefresh,
  currentUserId,
  onChatClick
}) => {
  const { user } = useAuth();
  const [showAddSubArgument, setShowAddSubArgument] = useState(false);
  const [showAddEvidence, setShowAddEvidence] = useState(false);
  const [isSubmittingArgument, setIsSubmittingArgument] = useState(false);
  const [newEvidence, setNewEvidence] = useState({
    type: 'other' as Evidence['type'],
    content: '',
    sourceTitle: '',
    sourceUrl: ''
  });

  const isFor = argument.side === VoteSide.FOR;
  const sideColor = isFor ? 'border-success' : 'border-danger';
  const sideTextColor = isFor ? 'text-success' : 'text-danger';

  const handleAddSubArgument = async (text: string, side: VoteSide, references: any[]) => {
    setIsSubmittingArgument(true);
    try {
      // Convert side to isChallenge boolean (AGAINST means challenging the parent argument)
      const isChallenge = side === VoteSide.AGAINST;
      await onAddSubArgument(argument.id, text, isChallenge);
      setShowAddSubArgument(false);
      // Refresh to show the new sub-argument immediately
      await onRefresh();
      // Show success feedback
      console.log('Sub-argument added successfully!');
    } catch (error) {
      console.error('Error adding sub-argument:', error);
      alert('Failed to add sub-argument. Please try again.');
    } finally {
      setIsSubmittingArgument(false);
    }
  };

  const handleAddEvidence = async () => {
    if (newEvidence.content.trim() && newEvidence.sourceTitle.trim() && newEvidence.sourceUrl.trim()) {
      try {
        const evidence: Partial<Evidence> = {
          type: newEvidence.type,
          content: newEvidence.content.trim(),
          source: {
            id: `ref-${Date.now()}`,
            title: newEvidence.sourceTitle.trim(),
            url: newEvidence.sourceUrl.trim(),
            type: 'other'
          }
        };

        await onAddEvidence(argument.id, evidence);
        setNewEvidence({
          type: 'other',
          content: '',
          sourceTitle: '',
          sourceUrl: ''
        });
        setShowAddEvidence(false);
        await onRefresh(); // Refresh to show the new evidence
      } catch (error) {
        console.error('Failed to add evidence:', error);
        alert('Failed to add evidence. Please try again.');
      }
    }
  };

  return (
    <div className="bg-brand-surface rounded-xl shadow-lg p-6 mb-8">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <ParticipantIcon participant={argument.author} />
          <div>
            <span className="font-semibold text-brand-text">{argument.author.name}</span>
            <div className="flex items-center gap-2 mt-1">
              <span className={`font-bold text-sm ${sideTextColor}`}>
                {isFor ? 'FOR' : 'AGAINST'}
              </span>
              {argument.isChallenge && (
                <span className="text-xs bg-orange-500/20 text-orange-400 px-2 py-1 rounded-full">
                  Challenge
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => onVote(argument.id, 'up')}
            className="text-green-400 hover:text-green-300 transition-colors text-lg"
          >
            ▲
          </button>
          <span className="font-mono text-lg text-brand-text font-bold">
            {argument.upvotes - argument.downvotes}
          </span>
          <button
            onClick={() => onVote(argument.id, 'down')}
            className="text-red-400 hover:text-red-300 transition-colors text-lg"
          >
            ▼
          </button>
          <button
            onClick={() => onChatClick(argument)}
            className="ml-2 p-2 text-brand-text-secondary hover:text-brand-primary transition-colors rounded-lg hover:bg-brand-primary/10"
            title="Open chat"
          >
            <ChatIcon className="w-4 h-4" />
            {argument.comments && argument.comments.length > 0 && (
              <span className="ml-1 text-xs bg-brand-primary/20 text-brand-primary px-1.5 py-0.5 rounded-full">
                {argument.comments.length}
              </span>
            )}
          </button>
        </div>
      </div>

      <div className={`bg-brand-bg/50 rounded-lg p-6 border-l-4 ${sideColor}`}>
        <p className="text-lg text-brand-text mb-4 italic">"{argument.text}"</p>
        <div className="flex justify-between items-end">
          {argument.references.length > 0 && <ReferenceList references={argument.references} />}
          <div className="text-sm text-brand-text-secondary">
            Strength Score: <span className="text-brand-text font-semibold">{argument.strengthScore}%</span>
          </div>
        </div>
      </div>

      {/* Evidence Display */}
      {console.log('FocusedArgumentView - Argument ID:', argument.id)}
      {console.log('FocusedArgumentView - Argument evidence:', argument.evidence)}
      {argument.evidence && argument.evidence.length > 0 ? (
        <div className="mt-6">
          <h4 className="text-lg font-semibold text-brand-text mb-4">Evidence ({argument.evidence.length})</h4>
          <div className="space-y-3">
            {argument.evidence.map(evidence => (
              <EvidenceItem
                key={evidence.id}
                evidence={evidence}
                onVoteOnEvidence={onVoteOnEvidence ? (evidenceId, vote) => onVoteOnEvidence(argument.id, evidenceId, vote) : undefined}
                currentUserId={currentUserId}
              />
            ))}
          </div>
        </div>
      ) : (
        <div className="mt-6">
          <h4 className="text-lg font-semibold text-brand-text mb-4">Evidence (0)</h4>
          <p className="text-brand-text-light italic">No evidence has been added to this argument yet.</p>
        </div>
      )}

      {/* Add Evidence Section */}
      {canAddArguments && (
        <div className="mt-6">
          {!showAddEvidence ? (
            <button
              onClick={() => setShowAddEvidence(true)}
              className="flex items-center gap-2 px-4 py-2 bg-brand-secondary/20 hover:bg-brand-secondary/30 text-brand-secondary border border-brand-secondary/50 rounded-lg transition-colors"
            >
              <PlusIcon className="w-4 h-4" />
              Add Evidence
            </button>
          ) : (
            <div className="bg-brand-bg/50 rounded-lg p-4">
              <h4 className="text-lg font-semibold text-brand-text mb-4">Add Evidence</h4>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-brand-text mb-1">Evidence Type</label>
                  <select
                    value={newEvidence.type}
                    onChange={(e) => setNewEvidence(prev => ({ ...prev, type: e.target.value as Evidence['type'] }))}
                    className="w-full bg-brand-surface border border-brand-surface-light rounded-lg p-2 text-brand-text"
                  >
                    <option value="statistic">Statistic</option>
                    <option value="study">Study</option>
                    <option value="expert_opinion">Expert Opinion</option>
                    <option value="case_study">Case Study</option>
                    <option value="historical_fact">Historical Fact</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-brand-text mb-1">Evidence Description</label>
                  <textarea
                    value={newEvidence.content}
                    onChange={(e) => setNewEvidence(prev => ({ ...prev, content: e.target.value }))}
                    placeholder="Describe the evidence..."
                    className="w-full bg-brand-surface border border-brand-surface-light rounded-lg p-3 text-brand-text resize-none"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-brand-text mb-1">Source Title</label>
                    <input
                      type="text"
                      value={newEvidence.sourceTitle}
                      onChange={(e) => setNewEvidence(prev => ({ ...prev, sourceTitle: e.target.value }))}
                      placeholder="Source title..."
                      className="w-full bg-brand-surface border border-brand-surface-light rounded-lg p-2 text-brand-text"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-brand-text mb-1">Source URL</label>
                    <input
                      type="url"
                      value={newEvidence.sourceUrl}
                      onChange={(e) => setNewEvidence(prev => ({ ...prev, sourceUrl: e.target.value }))}
                      placeholder="https://..."
                      className="w-full bg-brand-surface border border-brand-surface-light rounded-lg p-2 text-brand-text"
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-2 mt-4">
                  <button
                    onClick={() => setShowAddEvidence(false)}
                    className="px-3 py-1 text-sm text-brand-text-secondary hover:text-white transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleAddEvidence}
                    disabled={!newEvidence.content.trim() || !newEvidence.sourceTitle.trim() || !newEvidence.sourceUrl.trim()}
                    className="px-3 py-1 bg-brand-secondary hover:bg-brand-secondary-dark text-white text-sm rounded transition-colors disabled:opacity-50"
                  >
                    Add Evidence
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Add Sub-Argument Section */}
      {canAddArguments && (
        <div className="mt-6">
          {!showAddSubArgument ? (
            <button
              onClick={() => setShowAddSubArgument(true)}
              className="flex items-center gap-2 px-4 py-2 bg-brand-primary/20 hover:bg-brand-primary/30 text-brand-primary border border-brand-primary/50 rounded-lg transition-colors"
            >
              <PlusIcon className="w-4 h-4" />
              Add Sub-Argument
            </button>
          ) : (
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-brand-text">Add Supporting or Challenging Argument</h4>
              <AddArgumentForm
                side={argument.side} // Default to same side, user can change
                onSubmit={handleAddSubArgument}
                onCancel={() => setShowAddSubArgument(false)}
                isSubmitting={isSubmittingArgument}
                allowSideSelection={true} // Allow user to choose FOR or AGAINST
              />
            </div>
          )}
        </div>
      )}

      {/* Sub-Arguments */}
      {argument.subArguments && argument.subArguments.length > 0 && (
        <div className="mt-8">
          <h3 className="text-xl font-bold text-brand-text mb-4">
            Sub-Arguments ({argument.subArguments.length})
          </h3>

          {/* Split layout: FOR on left, AGAINST on right */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-stretch">
            {/* Supporting Arguments (FOR) */}
            <div className="flex flex-col h-full">
              <h4 className="text-lg font-medium text-success mb-3 flex items-center gap-2">
                <span className="w-3 h-3 bg-success rounded-full"></span>
                Supporting Arguments
              </h4>
              <div className="flex-1 flex flex-col gap-3">
                {argument.subArguments
                  .filter(subArg => subArg.side === VoteSide.FOR)
                  .sort((a, b) => ((b.upvotes || 0) - (b.downvotes || 0)) - ((a.upvotes || 0) - (a.downvotes || 0)))
                  .map((subArg) => (
                    <div
                      key={subArg.id}
                      className="border border-brand-border rounded-lg p-3 hover:border-success/50 transition-colors cursor-pointer bg-brand-bg hover:bg-success/5 flex flex-col h-full"
                      onClick={() => onArgumentClick(subArg)}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <ParticipantIcon participant={subArg.author} size="sm" />
                          <span className="text-sm font-medium text-brand-text">
                            {subArg.author.name}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                onVote(subArg.id, 'up');
                              }}
                              className="text-green-400 hover:text-green-300 transition-colors text-sm"
                            >
                              ▲
                            </button>
                            <span className="text-sm font-medium text-brand-text">
                              {(subArg.upvotes || 0) - (subArg.downvotes || 0)}
                            </span>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                onVote(subArg.id, 'down');
                              }}
                              className="text-red-400 hover:text-red-300 transition-colors text-sm"
                            >
                              ▼
                            </button>
                          </div>
                          <button
                            onClick={() => onArgumentClick(subArg)}
                            className="text-xs text-brand-text-secondary hover:text-brand-text transition-colors px-2 py-1 rounded"
                          >
                            View
                          </button>
                        </div>
                      </div>
                      <p className="text-sm text-brand-text-light flex-1 mt-2">{subArg.text}</p>
                    </div>
                  ))}
                {argument.subArguments.filter(subArg => subArg.side === VoteSide.FOR).length === 0 && (
                  <div className="text-center py-6 text-brand-text-light border border-dashed border-brand-border rounded-lg flex items-center justify-center h-full">
                    No supporting arguments yet
                  </div>
                )}
              </div>
            </div>

            {/* Challenging Arguments (AGAINST) */}
            <div className="flex flex-col h-full">
              <h4 className="text-lg font-medium text-danger mb-3 flex items-center gap-2">
                <span className="w-3 h-3 bg-danger rounded-full"></span>
                Challenging Arguments
              </h4>
              <div className="flex-1 flex flex-col gap-3">
                {argument.subArguments
                  .filter(subArg => subArg.side === VoteSide.AGAINST)
                  .sort((a, b) => ((b.upvotes || 0) - (b.downvotes || 0)) - ((a.upvotes || 0) - (a.downvotes || 0)))
                  .map((subArg) => (
                    <div
                      key={subArg.id}
                      className="border border-brand-border rounded-lg p-3 hover:border-danger/50 transition-colors cursor-pointer bg-brand-bg hover:bg-danger/5 flex flex-col h-full"
                      onClick={() => onArgumentClick(subArg)}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <ParticipantIcon participant={subArg.author} size="sm" />
                          <span className="text-sm font-medium text-brand-text">
                            {subArg.author.name}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                onVote(subArg.id, 'up');
                              }}
                              className="text-green-400 hover:text-green-300 transition-colors text-sm"
                            >
                              ▲
                            </button>
                            <span className="text-sm font-medium text-brand-text">
                              {(subArg.upvotes || 0) - (subArg.downvotes || 0)}
                            </span>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                onVote(subArg.id, 'down');
                              }}
                              className="text-red-400 hover:text-red-300 transition-colors text-sm"
                            >
                              ▼
                            </button>
                          </div>
                          <button
                            onClick={() => onArgumentClick(subArg)}
                            className="text-xs text-brand-text-secondary hover:text-brand-text transition-colors px-2 py-1 rounded"
                          >
                            View
                          </button>
                        </div>
                      </div>
                      <p className="text-sm text-brand-text-light flex-1 mt-2">{subArg.text}</p>
                    </div>
                  ))}
                {argument.subArguments.filter(subArg => subArg.side === VoteSide.AGAINST).length === 0 && (
                  <div className="text-center py-6 text-brand-text-light border border-dashed border-brand-border rounded-lg flex items-center justify-center h-full">
                    No challenging arguments yet
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Report Button - Positioned away from main actions */}
      <div className="mt-8 pt-4 border-t border-brand-border/30 flex justify-end">
        <ReportButton
          contentId={argument.id}
          contentType="argument"
          className="text-xs text-brand-text-secondary/60 hover:text-brand-text-secondary"
        />
      </div>
    </div>
  );
};
