import { Response, NextFunction } from 'express';
import { AuthRequest } from '../middleware/auth';
export declare const recalculateAllDebateVoteTotals: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getDebates: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getDebateById: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const createDebate: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const updateDebate: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const deleteDebate: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const joinDebate: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const voteOnDebate: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const addArgument: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const voteOnArgument: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const addSubArgument: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const addEvidence: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const voteOnEvidence: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const addComment: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const voteOnComment: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const reportComment: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=debateController.d.ts.map