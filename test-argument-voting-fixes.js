const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

// Test users
const testUsers = [
  { username: 'testuser1', email: '<EMAIL>', password: 'password123' },
  { username: 'testuser2', email: '<EMAIL>', password: 'password123' }
];

let tokens = {};
let debateId = '';
let argumentId = '';
let subArgumentId = '';

async function registerAndLogin(user) {
  try {
    // Try to register (might fail if user exists)
    await axios.post(`${BASE_URL}/auth/register`, user);
  } catch (error) {
    // User might already exist, that's okay
  }

  // Login
  const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
    username: user.username,
    password: user.password
  });

  return loginResponse.data.token;
}

async function createTestDebate(token) {
  const response = await axios.post(`${BASE_URL}/debates`, {
    title: 'Test Debate for Voting Fixes',
    description: 'Testing argument voting fixes',
    type: 'open'
  }, {
    headers: { Authorization: `Bearer ${token}` }
  });

  return response.data.data.id;
}

async function addArgument(token, debateId) {
  const response = await axios.post(`${BASE_URL}/debates/${debateId}/arguments`, {
    text: 'This is a test argument for voting',
    side: 'FOR'
  }, {
    headers: { Authorization: `Bearer ${token}` }
  });

  return response.data.data.id;
}

async function addSubArgument(token, debateId, parentId) {
  const response = await axios.post(`${BASE_URL}/debates/${debateId}/arguments/${parentId}/sub-arguments`, {
    text: 'This is a test sub-argument',
    isChallenge: false
  }, {
    headers: { Authorization: `Bearer ${token}` }
  });

  return response.data.data.id;
}

async function voteOnArgument(token, debateId, argumentId, vote) {
  const response = await axios.post(`${BASE_URL}/debates/${debateId}/arguments/${argumentId}/vote`, {
    vote
  }, {
    headers: { Authorization: `Bearer ${token}` }
  });

  return response.data;
}

async function getDebate(token, debateId) {
  const response = await axios.get(`${BASE_URL}/debates/${debateId}`, {
    headers: { Authorization: `Bearer ${token}` }
  });

  return response.data.data;
}

async function runTests() {
  console.log('🧪 Testing Argument Voting Fixes...\n');

  try {
    // Setup: Register and login test users
    console.log('1. Setting up test users...');
    tokens.user1 = await registerAndLogin(testUsers[0]);
    tokens.user2 = await registerAndLogin(testUsers[1]);
    console.log('✅ Test users ready\n');

    // Create test debate
    console.log('2. Creating test debate...');
    debateId = await createTestDebate(tokens.user1);
    console.log(`✅ Created debate: ${debateId}\n`);

    // Add main argument
    console.log('3. Adding main argument...');
    argumentId = await addArgument(tokens.user1, debateId);
    console.log(`✅ Created argument: ${argumentId}\n`);

    // Add sub-argument
    console.log('4. Adding sub-argument...');
    subArgumentId = await addSubArgument(tokens.user2, debateId, argumentId);
    console.log(`✅ Created sub-argument: ${subArgumentId}\n`);

    // Test 1: User can vote once on argument
    console.log('5. Testing single vote per user on main argument...');
    await voteOnArgument(tokens.user1, debateId, argumentId, 'up');
    let debate = await getDebate(tokens.user1, debateId);
    let argument = debate.arguments.find(arg => arg.id === argumentId);
    console.log(`   Main argument votes: ${argument.upvotes} up, ${argument.downvotes} down`);
    console.log(`   User votes tracked: ${argument.userVotes?.length || 0}`);

    // Try to vote again (should toggle off)
    await voteOnArgument(tokens.user1, debateId, argumentId, 'up');
    debate = await getDebate(tokens.user1, debateId);
    argument = debate.arguments.find(arg => arg.id === argumentId);
    console.log(`   After second vote: ${argument.upvotes} up, ${argument.downvotes} down`);
    console.log(`   User votes tracked: ${argument.userVotes?.length || 0}`);
    console.log('✅ Single vote per user working\n');

    // Test 2: Vote change (up to down)
    console.log('6. Testing vote change...');
    await voteOnArgument(tokens.user1, debateId, argumentId, 'up');
    await voteOnArgument(tokens.user1, debateId, argumentId, 'down');
    debate = await getDebate(tokens.user1, debateId);
    argument = debate.arguments.find(arg => arg.id === argumentId);
    console.log(`   After vote change: ${argument.upvotes} up, ${argument.downvotes} down`);
    console.log(`   User votes tracked: ${argument.userVotes?.length || 0}`);
    console.log('✅ Vote change working\n');

    // Test 3: Sub-argument voting doesn't affect parent
    console.log('7. Testing sub-argument voting isolation...');
    const findSubArgument = (args, id) => {
      for (const arg of args) {
        if (arg.id === id) return arg;
        for (const sub of arg.subArguments || []) {
          if (sub.id === id) return sub;
          const found = findSubArgument([sub], id);
          if (found) return found;
        }
      }
      return null;
    };

    // Vote on sub-argument
    await voteOnArgument(tokens.user2, debateId, subArgumentId, 'up');
    debate = await getDebate(tokens.user1, debateId);
    argument = debate.arguments.find(arg => arg.id === argumentId);
    const subArgument = findSubArgument(debate.arguments, subArgumentId);
    
    console.log(`   Parent argument votes: ${argument.upvotes} up, ${argument.downvotes} down`);
    console.log(`   Sub-argument votes: ${subArgument.upvotes} up, ${subArgument.downvotes} down`);
    console.log(`   Debate totals: FOR=${debate.votesFor}, AGAINST=${debate.votesAgainst}`);
    console.log('✅ Sub-argument voting isolation working\n');

    // Test 4: Multiple users can vote
    console.log('8. Testing multiple users voting...');
    await voteOnArgument(tokens.user2, debateId, argumentId, 'up');
    debate = await getDebate(tokens.user1, debateId);
    argument = debate.arguments.find(arg => arg.id === argumentId);
    console.log(`   After user2 vote: ${argument.upvotes} up, ${argument.downvotes} down`);
    console.log(`   User votes tracked: ${argument.userVotes?.length || 0}`);
    console.log('✅ Multiple users voting working\n');

    console.log('🎉 All tests passed! Argument voting fixes are working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

runTests();
