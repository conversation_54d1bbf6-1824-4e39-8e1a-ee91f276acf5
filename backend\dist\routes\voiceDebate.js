"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const voiceDebateController_1 = require("../controllers/voiceDebateController");
const auth_1 = require("../middleware/auth");
const validate_1 = require("../middleware/validate");
const router = express_1.default.Router();
router.get('/:id/voice', [
    auth_1.auth,
    (0, express_validator_1.param)('id').isMongoId(),
    validate_1.validate
], voiceDebateController_1.getVoiceDebateState);
router.post('/:id/voice/initialize', [
    auth_1.auth,
    (0, express_validator_1.param)('id').isMongoId(),
    (0, express_validator_1.body)('settings').optional().isObject(),
    (0, express_validator_1.body)('settings.defaultSpeakingTime').optional().isInt({ min: 30, max: 600 }),
    (0, express_validator_1.body)('settings.maxSpeakersPerSide').optional().isInt({ min: 1, max: 20 }),
    (0, express_validator_1.body)('settings.allowSpectatorVoice').optional().isBoolean(),
    (0, express_validator_1.body)('settings.autoRotateSpeakers').optional().isBoolean(),
    (0, express_validator_1.body)('settings.mutualExclusionEnabled').optional().isBoolean(),
    validate_1.validate
], voiceDebateController_1.initializeVoiceDebate);
router.post('/:id/voice/join', [
    auth_1.auth,
    (0, express_validator_1.param)('id').isMongoId(),
    (0, express_validator_1.body)('side').isIn(['FOR', 'AGAINST']),
    (0, express_validator_1.body)('timeAllocation').optional().isInt({ min: 30, max: 600 }),
    validate_1.validate
], voiceDebateController_1.joinVoiceQueue);
router.post('/:id/voice/leave', [
    auth_1.auth,
    (0, express_validator_1.param)('id').isMongoId(),
    validate_1.validate
], voiceDebateController_1.leaveVoiceQueue);
router.post('/:id/voice/next-speaker', [
    auth_1.auth,
    (0, express_validator_1.param)('id').isMongoId(),
    validate_1.validate
], voiceDebateController_1.nextSpeaker);
router.post('/:id/voice/give-time', [
    auth_1.auth,
    (0, express_validator_1.param)('id').isMongoId(),
    (0, express_validator_1.body)('speakerId').isString().notEmpty(),
    validate_1.validate
], voiceDebateController_1.giveTime);
router.get('/:id/voice/time-blocks', [
    auth_1.auth,
    (0, express_validator_1.param)('id').isMongoId(),
    validate_1.validate
], voiceDebateController_1.getTimeBlocks);
router.post('/:id/voice/grant-time-blocks', [
    auth_1.auth,
    (0, express_validator_1.param)('id').isMongoId(),
    (0, express_validator_1.body)('userId').isString().notEmpty(),
    (0, express_validator_1.body)('timeBlocks').isInt({ min: 1, max: 100 }),
    validate_1.validate
], voiceDebateController_1.grantTimeBlocks);
router.post('/:id/voice/allocate-time', [
    auth_1.auth,
    (0, express_validator_1.param)('id').isMongoId(),
    (0, express_validator_1.body)('speakerId').isString().notEmpty(),
    (0, express_validator_1.body)('additionalTime').isInt({ min: 1, max: 300 }),
    validate_1.validate
], voiceDebateController_1.allocateTime);
router.put('/:id/voice/settings', [
    auth_1.auth,
    (0, express_validator_1.param)('id').isMongoId(),
    (0, express_validator_1.body)('settings').isObject(),
    (0, express_validator_1.body)('settings.defaultSpeakingTime').optional().isInt({ min: 30, max: 600 }),
    (0, express_validator_1.body)('settings.maxSpeakersPerSide').optional().isInt({ min: 1, max: 20 }),
    (0, express_validator_1.body)('settings.allowSpectatorVoice').optional().isBoolean(),
    (0, express_validator_1.body)('settings.autoRotateSpeakers').optional().isBoolean(),
    (0, express_validator_1.body)('settings.mutualExclusionEnabled').optional().isBoolean(),
    validate_1.validate
], voiceDebateController_1.updateVoiceSettings);
router.post('/:id/voice/mutual-exclusion', [
    auth_1.auth,
    (0, express_validator_1.param)('id').isMongoId(),
    validate_1.validate
], voiceDebateController_1.reportMutualExclusion);
router.post('/:id/voice/mutual-exclusion/release', [
    auth_1.auth,
    (0, express_validator_1.param)('id').isMongoId(),
    validate_1.validate
], voiceDebateController_1.releaseMutualExclusion);
exports.default = router;
//# sourceMappingURL=voiceDebate.js.map