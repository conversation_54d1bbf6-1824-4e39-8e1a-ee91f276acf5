import React from 'react';
import { AdminStats } from '../../types';
import { getDisplayName } from '../../utils/userDisplay';

interface AdminStatsOverviewProps {
  stats: AdminStats;
  onRefresh: () => void;
}

export const AdminStatsOverview: React.FC<AdminStatsOverviewProps> = ({ stats, onRefresh }) => {
  const { overview, userRoles, recentActivity } = stats;

  const statCards = [
    {
      title: 'Total Users',
      value: overview.totalUsers,
      icon: '👥',
      color: 'bg-blue-500/20 border-blue-500/30',
      textColor: 'text-blue-400'
    },
    {
      title: 'Total Debates',
      value: overview.totalDebates,
      icon: '💬',
      color: 'bg-green-500/20 border-green-500/30',
      textColor: 'text-green-400'
    },
    {
      title: 'Active Debates',
      value: overview.activeDebates,
      icon: '🔥',
      color: 'bg-orange-500/20 border-orange-500/30',
      textColor: 'text-orange-400'
    },
    {
      title: 'Pending Moderation',
      value: overview.pendingModerationItems,
      icon: '⚠️',
      color: 'bg-red-500/20 border-red-500/30',
      textColor: 'text-red-400'
    },
    {
      title: 'New Users Today',
      value: overview.usersToday,
      icon: '📈',
      color: 'bg-purple-500/20 border-purple-500/30',
      textColor: 'text-purple-400'
    },
    {
      title: 'New Debates Today',
      value: overview.debatesToday,
      icon: '🆕',
      color: 'bg-cyan-500/20 border-cyan-500/30',
      textColor: 'text-cyan-400'
    }
  ];

  return (
    <div className="space-y-8">
      {/* Header with refresh button */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">System Overview</h2>
        <button
          onClick={onRefresh}
          className="bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
        >
          <span>🔄</span>
          <span>Refresh</span>
        </button>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((card, index) => (
          <div
            key={index}
            className={`${card.color} border rounded-lg p-6`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-brand-text-secondary text-sm font-medium">{card.title}</p>
                <p className={`text-3xl font-bold ${card.textColor}`}>{card.value.toLocaleString()}</p>
              </div>
              <div className="text-3xl">{card.icon}</div>
            </div>
          </div>
        ))}
      </div>

      {/* User Roles Distribution */}
      <div className="bg-brand-surface rounded-lg p-6">
        <h3 className="text-xl font-bold text-white mb-4">User Roles Distribution</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-brand-surface-light rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-brand-text-secondary">Users</span>
              <span className="text-2xl font-bold text-blue-400">{userRoles.user || 0}</span>
            </div>
          </div>
          <div className="bg-brand-surface-light rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-brand-text-secondary">Moderators</span>
              <span className="text-2xl font-bold text-green-400">{userRoles.moderator || 0}</span>
            </div>
          </div>
          <div className="bg-brand-surface-light rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-brand-text-secondary">Admins</span>
              <span className="text-2xl font-bold text-purple-400">{userRoles.admin || 0}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Users */}
        <div className="bg-brand-surface rounded-lg p-6">
          <h3 className="text-xl font-bold text-white mb-4">Recent Users</h3>
          <div className="space-y-3">
            {recentActivity.users.length > 0 ? (
              recentActivity.users.map((user) => (
                <div key={user.id} className="flex items-center justify-between bg-brand-surface-light rounded-lg p-3">
                  <div>
                    <p className="text-white font-medium">{getDisplayName(user)}</p>
                    <p className="text-brand-text-secondary text-sm">@{user.username}</p>
                  </div>
                  <div className="text-right">
                    <span className={`inline-block px-2 py-1 rounded text-xs font-medium ${
                      user.role === 'admin' ? 'bg-purple-500/20 text-purple-400' :
                      user.role === 'moderator' ? 'bg-green-500/20 text-green-400' :
                      'bg-blue-500/20 text-blue-400'
                    }`}>
                      {user.role}
                    </span>
                    <p className="text-brand-text-secondary text-xs mt-1">
                      {new Date(user.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-brand-text-secondary text-center py-4">No recent users</p>
            )}
          </div>
        </div>

        {/* Recent Debates */}
        <div className="bg-brand-surface rounded-lg p-6">
          <h3 className="text-xl font-bold text-white mb-4">Recent Debates</h3>
          <div className="space-y-3">
            {recentActivity.debates.length > 0 ? (
              recentActivity.debates.map((debate) => (
                <div key={debate.id} className="bg-brand-surface-light rounded-lg p-3">
                  <p className="text-white font-medium mb-1">{debate.title}</p>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-brand-text-secondary">
                      by {getDisplayName(debate.proponent)}
                    </span>
                    <div className="flex items-center space-x-2">
                      <span className={`inline-block px-2 py-1 rounded text-xs font-medium ${
                        debate.status === 'active' ? 'bg-green-500/20 text-green-400' :
                        debate.status === 'completed' ? 'bg-blue-500/20 text-blue-400' :
                        'bg-gray-500/20 text-gray-400'
                      }`}>
                        {debate.status}
                      </span>
                      <span className="text-brand-text-secondary">
                        {new Date(debate.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-brand-text-secondary text-center py-4">No recent debates</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
