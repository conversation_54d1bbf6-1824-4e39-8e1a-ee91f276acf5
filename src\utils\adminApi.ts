import { AdminStats, User, UsersPaginationResponse, ModerationItem } from '../types';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('token');
  return {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` })
  };
};

// Helper function to handle API responses
const handleResponse = async (response: Response) => {
  if (!response.ok) {
    const error = await response.json().catch(() => ({ message: 'Network error' }));
    throw new Error(error.message || 'API request failed');
  }
  return response.json();
};

export const adminAPI = {
  // Get admin dashboard statistics
  getStats: async (): Promise<{ success: boolean; data: AdminStats }> => {
    const response = await fetch(`${API_BASE_URL}/admin/stats`, {
      method: 'GET',
      headers: getAuthHeaders()
    });
    return handleResponse(response);
  },

  // Get users with pagination and filtering
  getUsers: async (params: {
    page?: number;
    limit?: number;
    role?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: string;
  } = {}): Promise<{ success: boolean; data: UsersPaginationResponse }> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await fetch(`${API_BASE_URL}/admin/users?${queryParams}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });
    return handleResponse(response);
  },

  // Get user details
  getUserDetails: async (userId: string): Promise<{ 
    success: boolean; 
    data: { 
      user: User; 
      debates: any[]; 
      moderationHistory: ModerationItem[] 
    } 
  }> => {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });
    return handleResponse(response);
  },

  // Update user role
  updateUserRole: async (userId: string, role: 'user' | 'moderator' | 'admin'): Promise<{ 
    success: boolean; 
    data: User; 
    message: string 
  }> => {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/role`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify({ role })
    });
    return handleResponse(response);
  },

  // Suspend/unsuspend user
  suspendUser: async (userId: string, suspended: boolean, reason?: string): Promise<{ 
    success: boolean; 
    data: User; 
    message: string 
  }> => {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/suspend`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify({ suspended, reason })
    });
    return handleResponse(response);
  }
};

export const moderationAPI = {
  // Get moderation queue
  getQueue: async (params: {
    status?: string;
    page?: number;
    limit?: number;
  } = {}): Promise<{ 
    success: boolean; 
    data: { 
      items: ModerationItem[]; 
      pagination: { page: number; limit: number; total: number; pages: number } 
    } 
  }> => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await fetch(`${API_BASE_URL}/moderation/queue?${queryParams}`, {
      method: 'GET',
      headers: getAuthHeaders()
    });
    return handleResponse(response);
  },

  // Get moderation statistics
  getStats: async (): Promise<{ 
    success: boolean; 
    data: { 
      moderation: { 
        pending: number; 
        reviewed: number; 
        resolved: number; 
        dismissed: number; 
        total: number 
      }; 
      cleanup: any 
    } 
  }> => {
    const response = await fetch(`${API_BASE_URL}/moderation/stats`, {
      method: 'GET',
      headers: getAuthHeaders()
    });
    return handleResponse(response);
  },

  // Review moderation item
  reviewItem: async (itemId: string, data: {
    status: 'reviewed' | 'resolved' | 'dismissed';
    moderatorNotes?: string;
    actionTaken?: 'none' | 'warning' | 'comment_removed' | 'user_suspended';
  }): Promise<{ 
    success: boolean; 
    data: ModerationItem; 
    message: string 
  }> => {
    const response = await fetch(`${API_BASE_URL}/moderation/${itemId}/review`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(data)
    });
    return handleResponse(response);
  },

  // Trigger cleanup
  triggerCleanup: async (): Promise<{ success: boolean; message: string }> => {
    const response = await fetch(`${API_BASE_URL}/moderation/cleanup`, {
      method: 'POST',
      headers: getAuthHeaders()
    });
    return handleResponse(response);
  },

  // Get cleanup stats
  getCleanupStats: async (): Promise<{ success: boolean; data: any }> => {
    const response = await fetch(`${API_BASE_URL}/moderation/cleanup/stats`, {
      method: 'GET',
      headers: getAuthHeaders()
    });
    return handleResponse(response);
  }
};
