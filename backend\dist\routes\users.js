"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const userController_1 = require("../controllers/userController");
const validate_1 = require("../middleware/validate");
const router = express_1.default.Router();
router.get('/', [
    (0, express_validator_1.query)('page').optional().isInt({ min: 1 }),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }),
    (0, express_validator_1.query)('search').optional().trim().isLength({ min: 1 }),
    validate_1.validate
], userController_1.getUsers);
router.get('/:id', [
    (0, express_validator_1.param)('id').isMongoId(),
    validate_1.validate
], userController_1.getUserById);
router.get('/:id/debates', [
    (0, express_validator_1.param)('id').isMongoId(),
    (0, express_validator_1.query)('type').optional().isIn(['participated', 'created']),
    (0, express_validator_1.query)('page').optional().isInt({ min: 1 }),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }),
    validate_1.validate
], userController_1.getUserDebates);
exports.default = router;
//# sourceMappingURL=users.js.map