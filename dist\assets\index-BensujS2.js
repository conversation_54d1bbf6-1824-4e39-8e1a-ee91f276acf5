(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))o(d);new MutationObserver(d=>{for(const h of d)if(h.type==="childList")for(const m of h.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&o(m)}).observe(document,{childList:!0,subtree:!0});function c(d){const h={};return d.integrity&&(h.integrity=d.integrity),d.referrerPolicy&&(h.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?h.credentials="include":d.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function o(d){if(d.ep)return;d.ep=!0;const h=c(d);fetch(d.href,h)}})();function wm(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}var po={exports:{}},al={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zh;function kb(){if(Zh)return al;Zh=1;var a=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function c(o,d,h){var m=null;if(h!==void 0&&(m=""+h),d.key!==void 0&&(m=""+d.key),"key"in d){h={};for(var b in d)b!=="key"&&(h[b]=d[b])}else h=d;return d=h.ref,{$$typeof:a,type:o,key:m,ref:d!==void 0?d:null,props:h}}return al.Fragment=r,al.jsx=c,al.jsxs=c,al}var Kh;function qb(){return Kh||(Kh=1,po.exports=kb()),po.exports}var l=qb(),xo={exports:{}},pe={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jh;function Lb(){if(Jh)return pe;Jh=1;var a=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),c=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),m=Symbol.for("react.context"),b=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),x=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),S=Symbol.iterator;function U(j){return j===null||typeof j!="object"?null:(j=S&&j[S]||j["@@iterator"],typeof j=="function"?j:null)}var B={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},O=Object.assign,A={};function T(j,Y,ee){this.props=j,this.context=Y,this.refs=A,this.updater=ee||B}T.prototype.isReactComponent={},T.prototype.setState=function(j,Y){if(typeof j!="object"&&typeof j!="function"&&j!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,j,Y,"setState")},T.prototype.forceUpdate=function(j){this.updater.enqueueForceUpdate(this,j,"forceUpdate")};function X(){}X.prototype=T.prototype;function L(j,Y,ee){this.props=j,this.context=Y,this.refs=A,this.updater=ee||B}var F=L.prototype=new X;F.constructor=L,O(F,T.prototype),F.isPureReactComponent=!0;var ae=Array.isArray,P={H:null,A:null,T:null,S:null,V:null},K=Object.prototype.hasOwnProperty;function le(j,Y,ee,W,R,Z){return ee=Z.ref,{$$typeof:a,type:j,key:Y,ref:ee!==void 0?ee:null,props:Z}}function Q(j,Y){return le(j.type,Y,void 0,void 0,void 0,j.props)}function $(j){return typeof j=="object"&&j!==null&&j.$$typeof===a}function J(j){var Y={"=":"=0",":":"=2"};return"$"+j.replace(/[=:]/g,function(ee){return Y[ee]})}var xe=/\/+/g;function de(j,Y){return typeof j=="object"&&j!==null&&j.key!=null?J(""+j.key):Y.toString(36)}function Ce(){}function Ge(j){switch(j.status){case"fulfilled":return j.value;case"rejected":throw j.reason;default:switch(typeof j.status=="string"?j.then(Ce,Ce):(j.status="pending",j.then(function(Y){j.status==="pending"&&(j.status="fulfilled",j.value=Y)},function(Y){j.status==="pending"&&(j.status="rejected",j.reason=Y)})),j.status){case"fulfilled":return j.value;case"rejected":throw j.reason}}throw j}function qe(j,Y,ee,W,R){var Z=typeof j;(Z==="undefined"||Z==="boolean")&&(j=null);var I=!1;if(j===null)I=!0;else switch(Z){case"bigint":case"string":case"number":I=!0;break;case"object":switch(j.$$typeof){case a:case r:I=!0;break;case y:return I=j._init,qe(I(j._payload),Y,ee,W,R)}}if(I)return R=R(j),I=W===""?"."+de(j,0):W,ae(R)?(ee="",I!=null&&(ee=I.replace(xe,"$&/")+"/"),qe(R,Y,ee,"",function(ne){return ne})):R!=null&&($(R)&&(R=Q(R,ee+(R.key==null||j&&j.key===R.key?"":(""+R.key).replace(xe,"$&/")+"/")+I)),Y.push(R)),1;I=0;var Pe=W===""?".":W+":";if(ae(j))for(var De=0;De<j.length;De++)W=j[De],Z=Pe+de(W,De),I+=qe(W,Y,ee,Z,R);else if(De=U(j),typeof De=="function")for(j=De.call(j),De=0;!(W=j.next()).done;)W=W.value,Z=Pe+de(W,De++),I+=qe(W,Y,ee,Z,R);else if(Z==="object"){if(typeof j.then=="function")return qe(Ge(j),Y,ee,W,R);throw Y=String(j),Error("Objects are not valid as a React child (found: "+(Y==="[object Object]"?"object with keys {"+Object.keys(j).join(", ")+"}":Y)+"). If you meant to render a collection of children, use an array instead.")}return I}function N(j,Y,ee){if(j==null)return j;var W=[],R=0;return qe(j,W,"","",function(Z){return Y.call(ee,Z,R++)}),W}function G(j){if(j._status===-1){var Y=j._result;Y=Y(),Y.then(function(ee){(j._status===0||j._status===-1)&&(j._status=1,j._result=ee)},function(ee){(j._status===0||j._status===-1)&&(j._status=2,j._result=ee)}),j._status===-1&&(j._status=0,j._result=Y)}if(j._status===1)return j._result.default;throw j._result}var te=typeof reportError=="function"?reportError:function(j){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var Y=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof j=="object"&&j!==null&&typeof j.message=="string"?String(j.message):String(j),error:j});if(!window.dispatchEvent(Y))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",j);return}console.error(j)};function he(){}return pe.Children={map:N,forEach:function(j,Y,ee){N(j,function(){Y.apply(this,arguments)},ee)},count:function(j){var Y=0;return N(j,function(){Y++}),Y},toArray:function(j){return N(j,function(Y){return Y})||[]},only:function(j){if(!$(j))throw Error("React.Children.only expected to receive a single React element child.");return j}},pe.Component=T,pe.Fragment=c,pe.Profiler=d,pe.PureComponent=L,pe.StrictMode=o,pe.Suspense=g,pe.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=P,pe.__COMPILER_RUNTIME={__proto__:null,c:function(j){return P.H.useMemoCache(j)}},pe.cache=function(j){return function(){return j.apply(null,arguments)}},pe.cloneElement=function(j,Y,ee){if(j==null)throw Error("The argument must be a React element, but you passed "+j+".");var W=O({},j.props),R=j.key,Z=void 0;if(Y!=null)for(I in Y.ref!==void 0&&(Z=void 0),Y.key!==void 0&&(R=""+Y.key),Y)!K.call(Y,I)||I==="key"||I==="__self"||I==="__source"||I==="ref"&&Y.ref===void 0||(W[I]=Y[I]);var I=arguments.length-2;if(I===1)W.children=ee;else if(1<I){for(var Pe=Array(I),De=0;De<I;De++)Pe[De]=arguments[De+2];W.children=Pe}return le(j.type,R,void 0,void 0,Z,W)},pe.createContext=function(j){return j={$$typeof:m,_currentValue:j,_currentValue2:j,_threadCount:0,Provider:null,Consumer:null},j.Provider=j,j.Consumer={$$typeof:h,_context:j},j},pe.createElement=function(j,Y,ee){var W,R={},Z=null;if(Y!=null)for(W in Y.key!==void 0&&(Z=""+Y.key),Y)K.call(Y,W)&&W!=="key"&&W!=="__self"&&W!=="__source"&&(R[W]=Y[W]);var I=arguments.length-2;if(I===1)R.children=ee;else if(1<I){for(var Pe=Array(I),De=0;De<I;De++)Pe[De]=arguments[De+2];R.children=Pe}if(j&&j.defaultProps)for(W in I=j.defaultProps,I)R[W]===void 0&&(R[W]=I[W]);return le(j,Z,void 0,void 0,null,R)},pe.createRef=function(){return{current:null}},pe.forwardRef=function(j){return{$$typeof:b,render:j}},pe.isValidElement=$,pe.lazy=function(j){return{$$typeof:y,_payload:{_status:-1,_result:j},_init:G}},pe.memo=function(j,Y){return{$$typeof:x,type:j,compare:Y===void 0?null:Y}},pe.startTransition=function(j){var Y=P.T,ee={};P.T=ee;try{var W=j(),R=P.S;R!==null&&R(ee,W),typeof W=="object"&&W!==null&&typeof W.then=="function"&&W.then(he,te)}catch(Z){te(Z)}finally{P.T=Y}},pe.unstable_useCacheRefresh=function(){return P.H.useCacheRefresh()},pe.use=function(j){return P.H.use(j)},pe.useActionState=function(j,Y,ee){return P.H.useActionState(j,Y,ee)},pe.useCallback=function(j,Y){return P.H.useCallback(j,Y)},pe.useContext=function(j){return P.H.useContext(j)},pe.useDebugValue=function(){},pe.useDeferredValue=function(j,Y){return P.H.useDeferredValue(j,Y)},pe.useEffect=function(j,Y,ee){var W=P.H;if(typeof ee=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return W.useEffect(j,Y)},pe.useId=function(){return P.H.useId()},pe.useImperativeHandle=function(j,Y,ee){return P.H.useImperativeHandle(j,Y,ee)},pe.useInsertionEffect=function(j,Y){return P.H.useInsertionEffect(j,Y)},pe.useLayoutEffect=function(j,Y){return P.H.useLayoutEffect(j,Y)},pe.useMemo=function(j,Y){return P.H.useMemo(j,Y)},pe.useOptimistic=function(j,Y){return P.H.useOptimistic(j,Y)},pe.useReducer=function(j,Y,ee){return P.H.useReducer(j,Y,ee)},pe.useRef=function(j){return P.H.useRef(j)},pe.useState=function(j){return P.H.useState(j)},pe.useSyncExternalStore=function(j,Y,ee){return P.H.useSyncExternalStore(j,Y,ee)},pe.useTransition=function(){return P.H.useTransition()},pe.version="19.1.1",pe}var Fh;function Xo(){return Fh||(Fh=1,xo.exports=Lb()),xo.exports}var k=Xo();const Am=wm(k);var bo={exports:{}},ll={},go={exports:{}},yo={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $h;function Hb(){return $h||($h=1,function(a){function r(N,G){var te=N.length;N.push(G);e:for(;0<te;){var he=te-1>>>1,j=N[he];if(0<d(j,G))N[he]=G,N[te]=j,te=he;else break e}}function c(N){return N.length===0?null:N[0]}function o(N){if(N.length===0)return null;var G=N[0],te=N.pop();if(te!==G){N[0]=te;e:for(var he=0,j=N.length,Y=j>>>1;he<Y;){var ee=2*(he+1)-1,W=N[ee],R=ee+1,Z=N[R];if(0>d(W,te))R<j&&0>d(Z,W)?(N[he]=Z,N[R]=te,he=R):(N[he]=W,N[ee]=te,he=ee);else if(R<j&&0>d(Z,te))N[he]=Z,N[R]=te,he=R;else break e}}return G}function d(N,G){var te=N.sortIndex-G.sortIndex;return te!==0?te:N.id-G.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var h=performance;a.unstable_now=function(){return h.now()}}else{var m=Date,b=m.now();a.unstable_now=function(){return m.now()-b}}var g=[],x=[],y=1,S=null,U=3,B=!1,O=!1,A=!1,T=!1,X=typeof setTimeout=="function"?setTimeout:null,L=typeof clearTimeout=="function"?clearTimeout:null,F=typeof setImmediate<"u"?setImmediate:null;function ae(N){for(var G=c(x);G!==null;){if(G.callback===null)o(x);else if(G.startTime<=N)o(x),G.sortIndex=G.expirationTime,r(g,G);else break;G=c(x)}}function P(N){if(A=!1,ae(N),!O)if(c(g)!==null)O=!0,K||(K=!0,de());else{var G=c(x);G!==null&&qe(P,G.startTime-N)}}var K=!1,le=-1,Q=5,$=-1;function J(){return T?!0:!(a.unstable_now()-$<Q)}function xe(){if(T=!1,K){var N=a.unstable_now();$=N;var G=!0;try{e:{O=!1,A&&(A=!1,L(le),le=-1),B=!0;var te=U;try{t:{for(ae(N),S=c(g);S!==null&&!(S.expirationTime>N&&J());){var he=S.callback;if(typeof he=="function"){S.callback=null,U=S.priorityLevel;var j=he(S.expirationTime<=N);if(N=a.unstable_now(),typeof j=="function"){S.callback=j,ae(N),G=!0;break t}S===c(g)&&o(g),ae(N)}else o(g);S=c(g)}if(S!==null)G=!0;else{var Y=c(x);Y!==null&&qe(P,Y.startTime-N),G=!1}}break e}finally{S=null,U=te,B=!1}G=void 0}}finally{G?de():K=!1}}}var de;if(typeof F=="function")de=function(){F(xe)};else if(typeof MessageChannel<"u"){var Ce=new MessageChannel,Ge=Ce.port2;Ce.port1.onmessage=xe,de=function(){Ge.postMessage(null)}}else de=function(){X(xe,0)};function qe(N,G){le=X(function(){N(a.unstable_now())},G)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(N){N.callback=null},a.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Q=0<N?Math.floor(1e3/N):5},a.unstable_getCurrentPriorityLevel=function(){return U},a.unstable_next=function(N){switch(U){case 1:case 2:case 3:var G=3;break;default:G=U}var te=U;U=G;try{return N()}finally{U=te}},a.unstable_requestPaint=function(){T=!0},a.unstable_runWithPriority=function(N,G){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var te=U;U=N;try{return G()}finally{U=te}},a.unstable_scheduleCallback=function(N,G,te){var he=a.unstable_now();switch(typeof te=="object"&&te!==null?(te=te.delay,te=typeof te=="number"&&0<te?he+te:he):te=he,N){case 1:var j=-1;break;case 2:j=250;break;case 5:j=1073741823;break;case 4:j=1e4;break;default:j=5e3}return j=te+j,N={id:y++,callback:G,priorityLevel:N,startTime:te,expirationTime:j,sortIndex:-1},te>he?(N.sortIndex=te,r(x,N),c(g)===null&&N===c(x)&&(A?(L(le),le=-1):A=!0,qe(P,te-he))):(N.sortIndex=j,r(g,N),O||B||(O=!0,K||(K=!0,de()))),N},a.unstable_shouldYield=J,a.unstable_wrapCallback=function(N){var G=U;return function(){var te=U;U=G;try{return N.apply(this,arguments)}finally{U=te}}}}(yo)),yo}var Wh;function Yb(){return Wh||(Wh=1,go.exports=Hb()),go.exports}var vo={exports:{}},ht={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ph;function Vb(){if(Ph)return ht;Ph=1;var a=Xo();function r(g){var x="https://react.dev/errors/"+g;if(1<arguments.length){x+="?args[]="+encodeURIComponent(arguments[1]);for(var y=2;y<arguments.length;y++)x+="&args[]="+encodeURIComponent(arguments[y])}return"Minified React error #"+g+"; visit "+x+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(){}var o={d:{f:c,r:function(){throw Error(r(522))},D:c,C:c,L:c,m:c,X:c,S:c,M:c},p:0,findDOMNode:null},d=Symbol.for("react.portal");function h(g,x,y){var S=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:d,key:S==null?null:""+S,children:g,containerInfo:x,implementation:y}}var m=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function b(g,x){if(g==="font")return"";if(typeof x=="string")return x==="use-credentials"?x:""}return ht.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,ht.createPortal=function(g,x){var y=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!x||x.nodeType!==1&&x.nodeType!==9&&x.nodeType!==11)throw Error(r(299));return h(g,x,null,y)},ht.flushSync=function(g){var x=m.T,y=o.p;try{if(m.T=null,o.p=2,g)return g()}finally{m.T=x,o.p=y,o.d.f()}},ht.preconnect=function(g,x){typeof g=="string"&&(x?(x=x.crossOrigin,x=typeof x=="string"?x==="use-credentials"?x:"":void 0):x=null,o.d.C(g,x))},ht.prefetchDNS=function(g){typeof g=="string"&&o.d.D(g)},ht.preinit=function(g,x){if(typeof g=="string"&&x&&typeof x.as=="string"){var y=x.as,S=b(y,x.crossOrigin),U=typeof x.integrity=="string"?x.integrity:void 0,B=typeof x.fetchPriority=="string"?x.fetchPriority:void 0;y==="style"?o.d.S(g,typeof x.precedence=="string"?x.precedence:void 0,{crossOrigin:S,integrity:U,fetchPriority:B}):y==="script"&&o.d.X(g,{crossOrigin:S,integrity:U,fetchPriority:B,nonce:typeof x.nonce=="string"?x.nonce:void 0})}},ht.preinitModule=function(g,x){if(typeof g=="string")if(typeof x=="object"&&x!==null){if(x.as==null||x.as==="script"){var y=b(x.as,x.crossOrigin);o.d.M(g,{crossOrigin:y,integrity:typeof x.integrity=="string"?x.integrity:void 0,nonce:typeof x.nonce=="string"?x.nonce:void 0})}}else x==null&&o.d.M(g)},ht.preload=function(g,x){if(typeof g=="string"&&typeof x=="object"&&x!==null&&typeof x.as=="string"){var y=x.as,S=b(y,x.crossOrigin);o.d.L(g,y,{crossOrigin:S,integrity:typeof x.integrity=="string"?x.integrity:void 0,nonce:typeof x.nonce=="string"?x.nonce:void 0,type:typeof x.type=="string"?x.type:void 0,fetchPriority:typeof x.fetchPriority=="string"?x.fetchPriority:void 0,referrerPolicy:typeof x.referrerPolicy=="string"?x.referrerPolicy:void 0,imageSrcSet:typeof x.imageSrcSet=="string"?x.imageSrcSet:void 0,imageSizes:typeof x.imageSizes=="string"?x.imageSizes:void 0,media:typeof x.media=="string"?x.media:void 0})}},ht.preloadModule=function(g,x){if(typeof g=="string")if(x){var y=b(x.as,x.crossOrigin);o.d.m(g,{as:typeof x.as=="string"&&x.as!=="script"?x.as:void 0,crossOrigin:y,integrity:typeof x.integrity=="string"?x.integrity:void 0})}else o.d.m(g)},ht.requestFormReset=function(g){o.d.r(g)},ht.unstable_batchedUpdates=function(g,x){return g(x)},ht.useFormState=function(g,x,y){return m.H.useFormState(g,x,y)},ht.useFormStatus=function(){return m.H.useHostTransitionStatus()},ht.version="19.1.1",ht}var Ih;function Gb(){if(Ih)return vo.exports;Ih=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(r){console.error(r)}}return a(),vo.exports=Vb(),vo.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var em;function Xb(){if(em)return ll;em=1;var a=Yb(),r=Xo(),c=Gb();function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function h(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function m(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function b(e){if(h(e)!==e)throw Error(o(188))}function g(e){var t=e.alternate;if(!t){if(t=h(e),t===null)throw Error(o(188));return t!==e?null:e}for(var n=e,s=t;;){var i=n.return;if(i===null)break;var u=i.alternate;if(u===null){if(s=i.return,s!==null){n=s;continue}break}if(i.child===u.child){for(u=i.child;u;){if(u===n)return b(i),e;if(u===s)return b(i),t;u=u.sibling}throw Error(o(188))}if(n.return!==s.return)n=i,s=u;else{for(var f=!1,p=i.child;p;){if(p===n){f=!0,n=i,s=u;break}if(p===s){f=!0,s=i,n=u;break}p=p.sibling}if(!f){for(p=u.child;p;){if(p===n){f=!0,n=u,s=i;break}if(p===s){f=!0,s=u,n=i;break}p=p.sibling}if(!f)throw Error(o(189))}}if(n.alternate!==s)throw Error(o(190))}if(n.tag!==3)throw Error(o(188));return n.stateNode.current===n?e:t}function x(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=x(e),t!==null)return t;e=e.sibling}return null}var y=Object.assign,S=Symbol.for("react.element"),U=Symbol.for("react.transitional.element"),B=Symbol.for("react.portal"),O=Symbol.for("react.fragment"),A=Symbol.for("react.strict_mode"),T=Symbol.for("react.profiler"),X=Symbol.for("react.provider"),L=Symbol.for("react.consumer"),F=Symbol.for("react.context"),ae=Symbol.for("react.forward_ref"),P=Symbol.for("react.suspense"),K=Symbol.for("react.suspense_list"),le=Symbol.for("react.memo"),Q=Symbol.for("react.lazy"),$=Symbol.for("react.activity"),J=Symbol.for("react.memo_cache_sentinel"),xe=Symbol.iterator;function de(e){return e===null||typeof e!="object"?null:(e=xe&&e[xe]||e["@@iterator"],typeof e=="function"?e:null)}var Ce=Symbol.for("react.client.reference");function Ge(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Ce?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case O:return"Fragment";case T:return"Profiler";case A:return"StrictMode";case P:return"Suspense";case K:return"SuspenseList";case $:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case B:return"Portal";case F:return(e.displayName||"Context")+".Provider";case L:return(e._context.displayName||"Context")+".Consumer";case ae:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case le:return t=e.displayName||null,t!==null?t:Ge(e.type)||"Memo";case Q:t=e._payload,e=e._init;try{return Ge(e(t))}catch{}}return null}var qe=Array.isArray,N=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,G=c.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,te={pending:!1,data:null,method:null,action:null},he=[],j=-1;function Y(e){return{current:e}}function ee(e){0>j||(e.current=he[j],he[j]=null,j--)}function W(e,t){j++,he[j]=e.current,e.current=t}var R=Y(null),Z=Y(null),I=Y(null),Pe=Y(null);function De(e,t){switch(W(I,t),W(Z,e),W(R,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?vh(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=vh(t),e=jh(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}ee(R),W(R,e)}function ne(){ee(R),ee(Z),ee(I)}function Se(e){e.memoizedState!==null&&W(Pe,e);var t=R.current,n=jh(t,e.type);t!==n&&(W(Z,e),W(R,n))}function oe(e){Z.current===e&&(ee(R),ee(Z)),Pe.current===e&&(ee(Pe),Ia._currentValue=te)}var ye=Object.prototype.hasOwnProperty,Ee=a.unstable_scheduleCallback,be=a.unstable_cancelCallback,Ye=a.unstable_shouldYield,me=a.unstable_requestPaint,Oe=a.unstable_now,zt=a.unstable_getCurrentPriorityLevel,Io=a.unstable_ImmediatePriority,eu=a.unstable_UserBlockingPriority,pl=a.unstable_NormalPriority,gp=a.unstable_LowPriority,tu=a.unstable_IdlePriority,yp=a.log,vp=a.unstable_setDisableYieldValue,ia=null,At=null;function wn(e){if(typeof yp=="function"&&vp(e),At&&typeof At.setStrictMode=="function")try{At.setStrictMode(ia,e)}catch{}}var Et=Math.clz32?Math.clz32:Sp,jp=Math.log,Np=Math.LN2;function Sp(e){return e>>>=0,e===0?32:31-(jp(e)/Np|0)|0}var xl=256,bl=4194304;function $n(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function gl(e,t,n){var s=e.pendingLanes;if(s===0)return 0;var i=0,u=e.suspendedLanes,f=e.pingedLanes;e=e.warmLanes;var p=s&134217727;return p!==0?(s=p&~u,s!==0?i=$n(s):(f&=p,f!==0?i=$n(f):n||(n=p&~e,n!==0&&(i=$n(n))))):(p=s&~u,p!==0?i=$n(p):f!==0?i=$n(f):n||(n=s&~e,n!==0&&(i=$n(n)))),i===0?0:t!==0&&t!==i&&(t&u)===0&&(u=i&-i,n=t&-t,u>=n||u===32&&(n&4194048)!==0)?t:i}function ca(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function wp(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function nu(){var e=xl;return xl<<=1,(xl&4194048)===0&&(xl=256),e}function su(){var e=bl;return bl<<=1,(bl&62914560)===0&&(bl=4194304),e}function si(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function oa(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Ap(e,t,n,s,i,u){var f=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var p=e.entanglements,v=e.expirationTimes,C=e.hiddenUpdates;for(n=f&~n;0<n;){var q=31-Et(n),V=1<<q;p[q]=0,v[q]=-1;var D=C[q];if(D!==null)for(C[q]=null,q=0;q<D.length;q++){var M=D[q];M!==null&&(M.lane&=-536870913)}n&=~V}s!==0&&au(e,s,0),u!==0&&i===0&&e.tag!==0&&(e.suspendedLanes|=u&~(f&~t))}function au(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var s=31-Et(t);e.entangledLanes|=t,e.entanglements[s]=e.entanglements[s]|1073741824|n&4194090}function lu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var s=31-Et(n),i=1<<s;i&t|e[s]&t&&(e[s]|=t),n&=~i}}function ai(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function li(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function ru(){var e=G.p;return e!==0?e:(e=window.event,e===void 0?32:Hh(e.type))}function Ep(e,t){var n=G.p;try{return G.p=e,t()}finally{G.p=n}}var An=Math.random().toString(36).slice(2),dt="__reactFiber$"+An,bt="__reactProps$"+An,gs="__reactContainer$"+An,ri="__reactEvents$"+An,Tp="__reactListeners$"+An,_p="__reactHandles$"+An,iu="__reactResources$"+An,ua="__reactMarker$"+An;function ii(e){delete e[dt],delete e[bt],delete e[ri],delete e[Tp],delete e[_p]}function ys(e){var t=e[dt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[gs]||n[dt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Ah(e);e!==null;){if(n=e[dt])return n;e=Ah(e)}return t}e=n,n=e.parentNode}return null}function vs(e){if(e=e[dt]||e[gs]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function da(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(o(33))}function js(e){var t=e[iu];return t||(t=e[iu]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function at(e){e[ua]=!0}var cu=new Set,ou={};function Wn(e,t){Ns(e,t),Ns(e+"Capture",t)}function Ns(e,t){for(ou[e]=t,e=0;e<t.length;e++)cu.add(t[e])}var Rp=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),uu={},du={};function Cp(e){return ye.call(du,e)?!0:ye.call(uu,e)?!1:Rp.test(e)?du[e]=!0:(uu[e]=!0,!1)}function yl(e,t,n){if(Cp(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var s=t.toLowerCase().slice(0,5);if(s!=="data-"&&s!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function vl(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function on(e,t,n,s){if(s===null)e.removeAttribute(n);else{switch(typeof s){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+s)}}var ci,fu;function Ss(e){if(ci===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);ci=t&&t[1]||"",fu=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+ci+e+fu}var oi=!1;function ui(e,t){if(!e||oi)return"";oi=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var s={DetermineComponentFrameRoot:function(){try{if(t){var V=function(){throw Error()};if(Object.defineProperty(V.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(V,[])}catch(M){var D=M}Reflect.construct(e,[],V)}else{try{V.call()}catch(M){D=M}e.call(V.prototype)}}else{try{throw Error()}catch(M){D=M}(V=e())&&typeof V.catch=="function"&&V.catch(function(){})}}catch(M){if(M&&D&&typeof M.stack=="string")return[M.stack,D.stack]}return[null,null]}};s.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(s.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(s.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=s.DetermineComponentFrameRoot(),f=u[0],p=u[1];if(f&&p){var v=f.split(`
`),C=p.split(`
`);for(i=s=0;s<v.length&&!v[s].includes("DetermineComponentFrameRoot");)s++;for(;i<C.length&&!C[i].includes("DetermineComponentFrameRoot");)i++;if(s===v.length||i===C.length)for(s=v.length-1,i=C.length-1;1<=s&&0<=i&&v[s]!==C[i];)i--;for(;1<=s&&0<=i;s--,i--)if(v[s]!==C[i]){if(s!==1||i!==1)do if(s--,i--,0>i||v[s]!==C[i]){var q=`
`+v[s].replace(" at new "," at ");return e.displayName&&q.includes("<anonymous>")&&(q=q.replace("<anonymous>",e.displayName)),q}while(1<=s&&0<=i);break}}}finally{oi=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?Ss(n):""}function Op(e){switch(e.tag){case 26:case 27:case 5:return Ss(e.type);case 16:return Ss("Lazy");case 13:return Ss("Suspense");case 19:return Ss("SuspenseList");case 0:case 15:return ui(e.type,!1);case 11:return ui(e.type.render,!1);case 1:return ui(e.type,!0);case 31:return Ss("Activity");default:return""}}function hu(e){try{var t="";do t+=Op(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Bt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function mu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Dp(e){var t=mu(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),s=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,u=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(f){s=""+f,u.call(this,f)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return s},setValue:function(f){s=""+f},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function jl(e){e._valueTracker||(e._valueTracker=Dp(e))}function pu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),s="";return e&&(s=mu(e)?e.checked?"true":"false":e.value),e=s,e!==n?(t.setValue(e),!0):!1}function Nl(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Up=/[\n"\\]/g;function kt(e){return e.replace(Up,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function di(e,t,n,s,i,u,f,p){e.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?e.type=f:e.removeAttribute("type"),t!=null?f==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Bt(t)):e.value!==""+Bt(t)&&(e.value=""+Bt(t)):f!=="submit"&&f!=="reset"||e.removeAttribute("value"),t!=null?fi(e,f,Bt(t)):n!=null?fi(e,f,Bt(n)):s!=null&&e.removeAttribute("value"),i==null&&u!=null&&(e.defaultChecked=!!u),i!=null&&(e.checked=i&&typeof i!="function"&&typeof i!="symbol"),p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"?e.name=""+Bt(p):e.removeAttribute("name")}function xu(e,t,n,s,i,u,f,p){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(e.type=u),t!=null||n!=null){if(!(u!=="submit"&&u!=="reset"||t!=null))return;n=n!=null?""+Bt(n):"",t=t!=null?""+Bt(t):n,p||t===e.value||(e.value=t),e.defaultValue=t}s=s??i,s=typeof s!="function"&&typeof s!="symbol"&&!!s,e.checked=p?e.checked:!!s,e.defaultChecked=!!s,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.name=f)}function fi(e,t,n){t==="number"&&Nl(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function ws(e,t,n,s){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&s&&(e[n].defaultSelected=!0)}else{for(n=""+Bt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,s&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function bu(e,t,n){if(t!=null&&(t=""+Bt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+Bt(n):""}function gu(e,t,n,s){if(t==null){if(s!=null){if(n!=null)throw Error(o(92));if(qe(s)){if(1<s.length)throw Error(o(93));s=s[0]}n=s}n==null&&(n=""),t=n}n=Bt(t),e.defaultValue=n,s=e.textContent,s===n&&s!==""&&s!==null&&(e.value=s)}function As(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Mp=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function yu(e,t,n){var s=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?s?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":s?e.setProperty(t,n):typeof n!="number"||n===0||Mp.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function vu(e,t,n){if(t!=null&&typeof t!="object")throw Error(o(62));if(e=e.style,n!=null){for(var s in n)!n.hasOwnProperty(s)||t!=null&&t.hasOwnProperty(s)||(s.indexOf("--")===0?e.setProperty(s,""):s==="float"?e.cssFloat="":e[s]="");for(var i in t)s=t[i],t.hasOwnProperty(i)&&n[i]!==s&&yu(e,i,s)}else for(var u in t)t.hasOwnProperty(u)&&yu(e,u,t[u])}function hi(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var zp=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Bp=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Sl(e){return Bp.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var mi=null;function pi(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Es=null,Ts=null;function ju(e){var t=vs(e);if(t&&(e=t.stateNode)){var n=e[bt]||null;e:switch(e=t.stateNode,t.type){case"input":if(di(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+kt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var s=n[t];if(s!==e&&s.form===e.form){var i=s[bt]||null;if(!i)throw Error(o(90));di(s,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<n.length;t++)s=n[t],s.form===e.form&&pu(s)}break e;case"textarea":bu(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&ws(e,!!n.multiple,t,!1)}}}var xi=!1;function Nu(e,t,n){if(xi)return e(t,n);xi=!0;try{var s=e(t);return s}finally{if(xi=!1,(Es!==null||Ts!==null)&&(cr(),Es&&(t=Es,e=Ts,Ts=Es=null,ju(t),e)))for(t=0;t<e.length;t++)ju(e[t])}}function fa(e,t){var n=e.stateNode;if(n===null)return null;var s=n[bt]||null;if(s===null)return null;n=s[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(s=!s.disabled)||(e=e.type,s=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!s;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(o(231,t,typeof n));return n}var un=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),bi=!1;if(un)try{var ha={};Object.defineProperty(ha,"passive",{get:function(){bi=!0}}),window.addEventListener("test",ha,ha),window.removeEventListener("test",ha,ha)}catch{bi=!1}var En=null,gi=null,wl=null;function Su(){if(wl)return wl;var e,t=gi,n=t.length,s,i="value"in En?En.value:En.textContent,u=i.length;for(e=0;e<n&&t[e]===i[e];e++);var f=n-e;for(s=1;s<=f&&t[n-s]===i[u-s];s++);return wl=i.slice(e,1<s?1-s:void 0)}function Al(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function El(){return!0}function wu(){return!1}function gt(e){function t(n,s,i,u,f){this._reactName=n,this._targetInst=i,this.type=s,this.nativeEvent=u,this.target=f,this.currentTarget=null;for(var p in e)e.hasOwnProperty(p)&&(n=e[p],this[p]=n?n(u):u[p]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?El:wu,this.isPropagationStopped=wu,this}return y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=El)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=El)},persist:function(){},isPersistent:El}),t}var Pn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Tl=gt(Pn),ma=y({},Pn,{view:0,detail:0}),kp=gt(ma),yi,vi,pa,_l=y({},ma,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ni,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==pa&&(pa&&e.type==="mousemove"?(yi=e.screenX-pa.screenX,vi=e.screenY-pa.screenY):vi=yi=0,pa=e),yi)},movementY:function(e){return"movementY"in e?e.movementY:vi}}),Au=gt(_l),qp=y({},_l,{dataTransfer:0}),Lp=gt(qp),Hp=y({},ma,{relatedTarget:0}),ji=gt(Hp),Yp=y({},Pn,{animationName:0,elapsedTime:0,pseudoElement:0}),Vp=gt(Yp),Gp=y({},Pn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Xp=gt(Gp),Qp=y({},Pn,{data:0}),Eu=gt(Qp),Zp={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Kp={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Jp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Fp(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Jp[e])?!!t[e]:!1}function Ni(){return Fp}var $p=y({},ma,{key:function(e){if(e.key){var t=Zp[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Al(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Kp[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ni,charCode:function(e){return e.type==="keypress"?Al(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Al(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Wp=gt($p),Pp=y({},_l,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Tu=gt(Pp),Ip=y({},ma,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ni}),ex=gt(Ip),tx=y({},Pn,{propertyName:0,elapsedTime:0,pseudoElement:0}),nx=gt(tx),sx=y({},_l,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ax=gt(sx),lx=y({},Pn,{newState:0,oldState:0}),rx=gt(lx),ix=[9,13,27,32],Si=un&&"CompositionEvent"in window,xa=null;un&&"documentMode"in document&&(xa=document.documentMode);var cx=un&&"TextEvent"in window&&!xa,_u=un&&(!Si||xa&&8<xa&&11>=xa),Ru=" ",Cu=!1;function Ou(e,t){switch(e){case"keyup":return ix.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Du(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var _s=!1;function ox(e,t){switch(e){case"compositionend":return Du(t);case"keypress":return t.which!==32?null:(Cu=!0,Ru);case"textInput":return e=t.data,e===Ru&&Cu?null:e;default:return null}}function ux(e,t){if(_s)return e==="compositionend"||!Si&&Ou(e,t)?(e=Su(),wl=gi=En=null,_s=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return _u&&t.locale!=="ko"?null:t.data;default:return null}}var dx={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Uu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!dx[e.type]:t==="textarea"}function Mu(e,t,n,s){Es?Ts?Ts.push(s):Ts=[s]:Es=s,t=mr(t,"onChange"),0<t.length&&(n=new Tl("onChange","change",null,n,s),e.push({event:n,listeners:t}))}var ba=null,ga=null;function fx(e){ph(e,0)}function Rl(e){var t=da(e);if(pu(t))return e}function zu(e,t){if(e==="change")return t}var Bu=!1;if(un){var wi;if(un){var Ai="oninput"in document;if(!Ai){var ku=document.createElement("div");ku.setAttribute("oninput","return;"),Ai=typeof ku.oninput=="function"}wi=Ai}else wi=!1;Bu=wi&&(!document.documentMode||9<document.documentMode)}function qu(){ba&&(ba.detachEvent("onpropertychange",Lu),ga=ba=null)}function Lu(e){if(e.propertyName==="value"&&Rl(ga)){var t=[];Mu(t,ga,e,pi(e)),Nu(fx,t)}}function hx(e,t,n){e==="focusin"?(qu(),ba=t,ga=n,ba.attachEvent("onpropertychange",Lu)):e==="focusout"&&qu()}function mx(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Rl(ga)}function px(e,t){if(e==="click")return Rl(t)}function xx(e,t){if(e==="input"||e==="change")return Rl(t)}function bx(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Tt=typeof Object.is=="function"?Object.is:bx;function ya(e,t){if(Tt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),s=Object.keys(t);if(n.length!==s.length)return!1;for(s=0;s<n.length;s++){var i=n[s];if(!ye.call(t,i)||!Tt(e[i],t[i]))return!1}return!0}function Hu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Yu(e,t){var n=Hu(e);e=0;for(var s;n;){if(n.nodeType===3){if(s=e+n.textContent.length,e<=t&&s>=t)return{node:n,offset:t-e};e=s}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Hu(n)}}function Vu(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Vu(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Gu(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Nl(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Nl(e.document)}return t}function Ei(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var gx=un&&"documentMode"in document&&11>=document.documentMode,Rs=null,Ti=null,va=null,_i=!1;function Xu(e,t,n){var s=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;_i||Rs==null||Rs!==Nl(s)||(s=Rs,"selectionStart"in s&&Ei(s)?s={start:s.selectionStart,end:s.selectionEnd}:(s=(s.ownerDocument&&s.ownerDocument.defaultView||window).getSelection(),s={anchorNode:s.anchorNode,anchorOffset:s.anchorOffset,focusNode:s.focusNode,focusOffset:s.focusOffset}),va&&ya(va,s)||(va=s,s=mr(Ti,"onSelect"),0<s.length&&(t=new Tl("onSelect","select",null,t,n),e.push({event:t,listeners:s}),t.target=Rs)))}function In(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Cs={animationend:In("Animation","AnimationEnd"),animationiteration:In("Animation","AnimationIteration"),animationstart:In("Animation","AnimationStart"),transitionrun:In("Transition","TransitionRun"),transitionstart:In("Transition","TransitionStart"),transitioncancel:In("Transition","TransitionCancel"),transitionend:In("Transition","TransitionEnd")},Ri={},Qu={};un&&(Qu=document.createElement("div").style,"AnimationEvent"in window||(delete Cs.animationend.animation,delete Cs.animationiteration.animation,delete Cs.animationstart.animation),"TransitionEvent"in window||delete Cs.transitionend.transition);function es(e){if(Ri[e])return Ri[e];if(!Cs[e])return e;var t=Cs[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Qu)return Ri[e]=t[n];return e}var Zu=es("animationend"),Ku=es("animationiteration"),Ju=es("animationstart"),yx=es("transitionrun"),vx=es("transitionstart"),jx=es("transitioncancel"),Fu=es("transitionend"),$u=new Map,Ci="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Ci.push("scrollEnd");function Zt(e,t){$u.set(e,t),Wn(t,[e])}var Wu=new WeakMap;function qt(e,t){if(typeof e=="object"&&e!==null){var n=Wu.get(e);return n!==void 0?n:(t={value:e,source:t,stack:hu(t)},Wu.set(e,t),t)}return{value:e,source:t,stack:hu(t)}}var Lt=[],Os=0,Oi=0;function Cl(){for(var e=Os,t=Oi=Os=0;t<e;){var n=Lt[t];Lt[t++]=null;var s=Lt[t];Lt[t++]=null;var i=Lt[t];Lt[t++]=null;var u=Lt[t];if(Lt[t++]=null,s!==null&&i!==null){var f=s.pending;f===null?i.next=i:(i.next=f.next,f.next=i),s.pending=i}u!==0&&Pu(n,i,u)}}function Ol(e,t,n,s){Lt[Os++]=e,Lt[Os++]=t,Lt[Os++]=n,Lt[Os++]=s,Oi|=s,e.lanes|=s,e=e.alternate,e!==null&&(e.lanes|=s)}function Di(e,t,n,s){return Ol(e,t,n,s),Dl(e)}function Ds(e,t){return Ol(e,null,null,t),Dl(e)}function Pu(e,t,n){e.lanes|=n;var s=e.alternate;s!==null&&(s.lanes|=n);for(var i=!1,u=e.return;u!==null;)u.childLanes|=n,s=u.alternate,s!==null&&(s.childLanes|=n),u.tag===22&&(e=u.stateNode,e===null||e._visibility&1||(i=!0)),e=u,u=u.return;return e.tag===3?(u=e.stateNode,i&&t!==null&&(i=31-Et(n),e=u.hiddenUpdates,s=e[i],s===null?e[i]=[t]:s.push(t),t.lane=n|536870912),u):null}function Dl(e){if(50<Qa)throw Qa=0,qc=null,Error(o(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Us={};function Nx(e,t,n,s){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=s,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function _t(e,t,n,s){return new Nx(e,t,n,s)}function Ui(e){return e=e.prototype,!(!e||!e.isReactComponent)}function dn(e,t){var n=e.alternate;return n===null?(n=_t(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Iu(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ul(e,t,n,s,i,u){var f=0;if(s=e,typeof e=="function")Ui(e)&&(f=1);else if(typeof e=="string")f=wb(e,n,R.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case $:return e=_t(31,n,t,i),e.elementType=$,e.lanes=u,e;case O:return ts(n.children,i,u,t);case A:f=8,i|=24;break;case T:return e=_t(12,n,t,i|2),e.elementType=T,e.lanes=u,e;case P:return e=_t(13,n,t,i),e.elementType=P,e.lanes=u,e;case K:return e=_t(19,n,t,i),e.elementType=K,e.lanes=u,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case X:case F:f=10;break e;case L:f=9;break e;case ae:f=11;break e;case le:f=14;break e;case Q:f=16,s=null;break e}f=29,n=Error(o(130,e===null?"null":typeof e,"")),s=null}return t=_t(f,n,t,i),t.elementType=e,t.type=s,t.lanes=u,t}function ts(e,t,n,s){return e=_t(7,e,s,t),e.lanes=n,e}function Mi(e,t,n){return e=_t(6,e,null,t),e.lanes=n,e}function zi(e,t,n){return t=_t(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Ms=[],zs=0,Ml=null,zl=0,Ht=[],Yt=0,ns=null,fn=1,hn="";function ss(e,t){Ms[zs++]=zl,Ms[zs++]=Ml,Ml=e,zl=t}function ed(e,t,n){Ht[Yt++]=fn,Ht[Yt++]=hn,Ht[Yt++]=ns,ns=e;var s=fn;e=hn;var i=32-Et(s)-1;s&=~(1<<i),n+=1;var u=32-Et(t)+i;if(30<u){var f=i-i%5;u=(s&(1<<f)-1).toString(32),s>>=f,i-=f,fn=1<<32-Et(t)+i|n<<i|s,hn=u+e}else fn=1<<u|n<<i|s,hn=e}function Bi(e){e.return!==null&&(ss(e,1),ed(e,1,0))}function ki(e){for(;e===Ml;)Ml=Ms[--zs],Ms[zs]=null,zl=Ms[--zs],Ms[zs]=null;for(;e===ns;)ns=Ht[--Yt],Ht[Yt]=null,hn=Ht[--Yt],Ht[Yt]=null,fn=Ht[--Yt],Ht[Yt]=null}var pt=null,Ke=null,Re=!1,as=null,Wt=!1,qi=Error(o(519));function ls(e){var t=Error(o(418,""));throw Sa(qt(t,e)),qi}function td(e){var t=e.stateNode,n=e.type,s=e.memoizedProps;switch(t[dt]=e,t[bt]=s,n){case"dialog":Ne("cancel",t),Ne("close",t);break;case"iframe":case"object":case"embed":Ne("load",t);break;case"video":case"audio":for(n=0;n<Ka.length;n++)Ne(Ka[n],t);break;case"source":Ne("error",t);break;case"img":case"image":case"link":Ne("error",t),Ne("load",t);break;case"details":Ne("toggle",t);break;case"input":Ne("invalid",t),xu(t,s.value,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name,!0),jl(t);break;case"select":Ne("invalid",t);break;case"textarea":Ne("invalid",t),gu(t,s.value,s.defaultValue,s.children),jl(t)}n=s.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||s.suppressHydrationWarning===!0||yh(t.textContent,n)?(s.popover!=null&&(Ne("beforetoggle",t),Ne("toggle",t)),s.onScroll!=null&&Ne("scroll",t),s.onScrollEnd!=null&&Ne("scrollend",t),s.onClick!=null&&(t.onclick=pr),t=!0):t=!1,t||ls(e)}function nd(e){for(pt=e.return;pt;)switch(pt.tag){case 5:case 13:Wt=!1;return;case 27:case 3:Wt=!0;return;default:pt=pt.return}}function ja(e){if(e!==pt)return!1;if(!Re)return nd(e),Re=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||eo(e.type,e.memoizedProps)),n=!n),n&&Ke&&ls(e),nd(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){Ke=Jt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}Ke=null}}else t===27?(t=Ke,Vn(e.type)?(e=ao,ao=null,Ke=e):Ke=t):Ke=pt?Jt(e.stateNode.nextSibling):null;return!0}function Na(){Ke=pt=null,Re=!1}function sd(){var e=as;return e!==null&&(jt===null?jt=e:jt.push.apply(jt,e),as=null),e}function Sa(e){as===null?as=[e]:as.push(e)}var Li=Y(null),rs=null,mn=null;function Tn(e,t,n){W(Li,t._currentValue),t._currentValue=n}function pn(e){e._currentValue=Li.current,ee(Li)}function Hi(e,t,n){for(;e!==null;){var s=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,s!==null&&(s.childLanes|=t)):s!==null&&(s.childLanes&t)!==t&&(s.childLanes|=t),e===n)break;e=e.return}}function Yi(e,t,n,s){var i=e.child;for(i!==null&&(i.return=e);i!==null;){var u=i.dependencies;if(u!==null){var f=i.child;u=u.firstContext;e:for(;u!==null;){var p=u;u=i;for(var v=0;v<t.length;v++)if(p.context===t[v]){u.lanes|=n,p=u.alternate,p!==null&&(p.lanes|=n),Hi(u.return,n,e),s||(f=null);break e}u=p.next}}else if(i.tag===18){if(f=i.return,f===null)throw Error(o(341));f.lanes|=n,u=f.alternate,u!==null&&(u.lanes|=n),Hi(f,n,e),f=null}else f=i.child;if(f!==null)f.return=i;else for(f=i;f!==null;){if(f===e){f=null;break}if(i=f.sibling,i!==null){i.return=f.return,f=i;break}f=f.return}i=f}}function wa(e,t,n,s){e=null;for(var i=t,u=!1;i!==null;){if(!u){if((i.flags&524288)!==0)u=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var f=i.alternate;if(f===null)throw Error(o(387));if(f=f.memoizedProps,f!==null){var p=i.type;Tt(i.pendingProps.value,f.value)||(e!==null?e.push(p):e=[p])}}else if(i===Pe.current){if(f=i.alternate,f===null)throw Error(o(387));f.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(e!==null?e.push(Ia):e=[Ia])}i=i.return}e!==null&&Yi(t,e,n,s),t.flags|=262144}function Bl(e){for(e=e.firstContext;e!==null;){if(!Tt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function is(e){rs=e,mn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function ft(e){return ad(rs,e)}function kl(e,t){return rs===null&&is(e),ad(e,t)}function ad(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},mn===null){if(e===null)throw Error(o(308));mn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else mn=mn.next=t;return n}var Sx=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,s){e.push(s)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},wx=a.unstable_scheduleCallback,Ax=a.unstable_NormalPriority,nt={$$typeof:F,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Vi(){return{controller:new Sx,data:new Map,refCount:0}}function Aa(e){e.refCount--,e.refCount===0&&wx(Ax,function(){e.controller.abort()})}var Ea=null,Gi=0,Bs=0,ks=null;function Ex(e,t){if(Ea===null){var n=Ea=[];Gi=0,Bs=Qc(),ks={status:"pending",value:void 0,then:function(s){n.push(s)}}}return Gi++,t.then(ld,ld),t}function ld(){if(--Gi===0&&Ea!==null){ks!==null&&(ks.status="fulfilled");var e=Ea;Ea=null,Bs=0,ks=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Tx(e,t){var n=[],s={status:"pending",value:null,reason:null,then:function(i){n.push(i)}};return e.then(function(){s.status="fulfilled",s.value=t;for(var i=0;i<n.length;i++)(0,n[i])(t)},function(i){for(s.status="rejected",s.reason=i,i=0;i<n.length;i++)(0,n[i])(void 0)}),s}var rd=N.S;N.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Ex(e,t),rd!==null&&rd(e,t)};var cs=Y(null);function Xi(){var e=cs.current;return e!==null?e:He.pooledCache}function ql(e,t){t===null?W(cs,cs.current):W(cs,t.pool)}function id(){var e=Xi();return e===null?null:{parent:nt._currentValue,pool:e}}var Ta=Error(o(460)),cd=Error(o(474)),Ll=Error(o(542)),Qi={then:function(){}};function od(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Hl(){}function ud(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(Hl,Hl),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,fd(e),e;default:if(typeof t.status=="string")t.then(Hl,Hl);else{if(e=He,e!==null&&100<e.shellSuspendCounter)throw Error(o(482));e=t,e.status="pending",e.then(function(s){if(t.status==="pending"){var i=t;i.status="fulfilled",i.value=s}},function(s){if(t.status==="pending"){var i=t;i.status="rejected",i.reason=s}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,fd(e),e}throw _a=t,Ta}}var _a=null;function dd(){if(_a===null)throw Error(o(459));var e=_a;return _a=null,e}function fd(e){if(e===Ta||e===Ll)throw Error(o(483))}var _n=!1;function Zi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Ki(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Rn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Cn(e,t,n){var s=e.updateQueue;if(s===null)return null;if(s=s.shared,(Ue&2)!==0){var i=s.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),s.pending=t,t=Dl(e),Pu(e,null,n),t}return Ol(e,s,t,n),Dl(e)}function Ra(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var s=t.lanes;s&=e.pendingLanes,n|=s,t.lanes=n,lu(e,n)}}function Ji(e,t){var n=e.updateQueue,s=e.alternate;if(s!==null&&(s=s.updateQueue,n===s)){var i=null,u=null;if(n=n.firstBaseUpdate,n!==null){do{var f={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};u===null?i=u=f:u=u.next=f,n=n.next}while(n!==null);u===null?i=u=t:u=u.next=t}else i=u=t;n={baseState:s.baseState,firstBaseUpdate:i,lastBaseUpdate:u,shared:s.shared,callbacks:s.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var Fi=!1;function Ca(){if(Fi){var e=ks;if(e!==null)throw e}}function Oa(e,t,n,s){Fi=!1;var i=e.updateQueue;_n=!1;var u=i.firstBaseUpdate,f=i.lastBaseUpdate,p=i.shared.pending;if(p!==null){i.shared.pending=null;var v=p,C=v.next;v.next=null,f===null?u=C:f.next=C,f=v;var q=e.alternate;q!==null&&(q=q.updateQueue,p=q.lastBaseUpdate,p!==f&&(p===null?q.firstBaseUpdate=C:p.next=C,q.lastBaseUpdate=v))}if(u!==null){var V=i.baseState;f=0,q=C=v=null,p=u;do{var D=p.lane&-536870913,M=D!==p.lane;if(M?(Ae&D)===D:(s&D)===D){D!==0&&D===Bs&&(Fi=!0),q!==null&&(q=q.next={lane:0,tag:p.tag,payload:p.payload,callback:null,next:null});e:{var ue=e,ie=p;D=t;var ke=n;switch(ie.tag){case 1:if(ue=ie.payload,typeof ue=="function"){V=ue.call(ke,V,D);break e}V=ue;break e;case 3:ue.flags=ue.flags&-65537|128;case 0:if(ue=ie.payload,D=typeof ue=="function"?ue.call(ke,V,D):ue,D==null)break e;V=y({},V,D);break e;case 2:_n=!0}}D=p.callback,D!==null&&(e.flags|=64,M&&(e.flags|=8192),M=i.callbacks,M===null?i.callbacks=[D]:M.push(D))}else M={lane:D,tag:p.tag,payload:p.payload,callback:p.callback,next:null},q===null?(C=q=M,v=V):q=q.next=M,f|=D;if(p=p.next,p===null){if(p=i.shared.pending,p===null)break;M=p,p=M.next,M.next=null,i.lastBaseUpdate=M,i.shared.pending=null}}while(!0);q===null&&(v=V),i.baseState=v,i.firstBaseUpdate=C,i.lastBaseUpdate=q,u===null&&(i.shared.lanes=0),qn|=f,e.lanes=f,e.memoizedState=V}}function hd(e,t){if(typeof e!="function")throw Error(o(191,e));e.call(t)}function md(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)hd(n[e],t)}var qs=Y(null),Yl=Y(0);function pd(e,t){e=Nn,W(Yl,e),W(qs,t),Nn=e|t.baseLanes}function $i(){W(Yl,Nn),W(qs,qs.current)}function Wi(){Nn=Yl.current,ee(qs),ee(Yl)}var On=0,ge=null,ze=null,Ie=null,Vl=!1,Ls=!1,os=!1,Gl=0,Da=0,Hs=null,_x=0;function Fe(){throw Error(o(321))}function Pi(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Tt(e[n],t[n]))return!1;return!0}function Ii(e,t,n,s,i,u){return On=u,ge=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,N.H=e===null||e.memoizedState===null?Pd:Id,os=!1,u=n(s,i),os=!1,Ls&&(u=bd(t,n,s,i)),xd(e),u}function xd(e){N.H=Fl;var t=ze!==null&&ze.next!==null;if(On=0,Ie=ze=ge=null,Vl=!1,Da=0,Hs=null,t)throw Error(o(300));e===null||lt||(e=e.dependencies,e!==null&&Bl(e)&&(lt=!0))}function bd(e,t,n,s){ge=e;var i=0;do{if(Ls&&(Hs=null),Da=0,Ls=!1,25<=i)throw Error(o(301));if(i+=1,Ie=ze=null,e.updateQueue!=null){var u=e.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}N.H=zx,u=t(n,s)}while(Ls);return u}function Rx(){var e=N.H,t=e.useState()[0];return t=typeof t.then=="function"?Ua(t):t,e=e.useState()[0],(ze!==null?ze.memoizedState:null)!==e&&(ge.flags|=1024),t}function ec(){var e=Gl!==0;return Gl=0,e}function tc(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function nc(e){if(Vl){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Vl=!1}On=0,Ie=ze=ge=null,Ls=!1,Da=Gl=0,Hs=null}function yt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ie===null?ge.memoizedState=Ie=e:Ie=Ie.next=e,Ie}function et(){if(ze===null){var e=ge.alternate;e=e!==null?e.memoizedState:null}else e=ze.next;var t=Ie===null?ge.memoizedState:Ie.next;if(t!==null)Ie=t,ze=e;else{if(e===null)throw ge.alternate===null?Error(o(467)):Error(o(310));ze=e,e={memoizedState:ze.memoizedState,baseState:ze.baseState,baseQueue:ze.baseQueue,queue:ze.queue,next:null},Ie===null?ge.memoizedState=Ie=e:Ie=Ie.next=e}return Ie}function sc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Ua(e){var t=Da;return Da+=1,Hs===null&&(Hs=[]),e=ud(Hs,e,t),t=ge,(Ie===null?t.memoizedState:Ie.next)===null&&(t=t.alternate,N.H=t===null||t.memoizedState===null?Pd:Id),e}function Xl(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Ua(e);if(e.$$typeof===F)return ft(e)}throw Error(o(438,String(e)))}function ac(e){var t=null,n=ge.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var s=ge.alternate;s!==null&&(s=s.updateQueue,s!==null&&(s=s.memoCache,s!=null&&(t={data:s.data.map(function(i){return i.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=sc(),ge.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),s=0;s<e;s++)n[s]=J;return t.index++,n}function xn(e,t){return typeof t=="function"?t(e):t}function Ql(e){var t=et();return lc(t,ze,e)}function lc(e,t,n){var s=e.queue;if(s===null)throw Error(o(311));s.lastRenderedReducer=n;var i=e.baseQueue,u=s.pending;if(u!==null){if(i!==null){var f=i.next;i.next=u.next,u.next=f}t.baseQueue=i=u,s.pending=null}if(u=e.baseState,i===null)e.memoizedState=u;else{t=i.next;var p=f=null,v=null,C=t,q=!1;do{var V=C.lane&-536870913;if(V!==C.lane?(Ae&V)===V:(On&V)===V){var D=C.revertLane;if(D===0)v!==null&&(v=v.next={lane:0,revertLane:0,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null}),V===Bs&&(q=!0);else if((On&D)===D){C=C.next,D===Bs&&(q=!0);continue}else V={lane:0,revertLane:C.revertLane,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null},v===null?(p=v=V,f=u):v=v.next=V,ge.lanes|=D,qn|=D;V=C.action,os&&n(u,V),u=C.hasEagerState?C.eagerState:n(u,V)}else D={lane:V,revertLane:C.revertLane,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null},v===null?(p=v=D,f=u):v=v.next=D,ge.lanes|=V,qn|=V;C=C.next}while(C!==null&&C!==t);if(v===null?f=u:v.next=p,!Tt(u,e.memoizedState)&&(lt=!0,q&&(n=ks,n!==null)))throw n;e.memoizedState=u,e.baseState=f,e.baseQueue=v,s.lastRenderedState=u}return i===null&&(s.lanes=0),[e.memoizedState,s.dispatch]}function rc(e){var t=et(),n=t.queue;if(n===null)throw Error(o(311));n.lastRenderedReducer=e;var s=n.dispatch,i=n.pending,u=t.memoizedState;if(i!==null){n.pending=null;var f=i=i.next;do u=e(u,f.action),f=f.next;while(f!==i);Tt(u,t.memoizedState)||(lt=!0),t.memoizedState=u,t.baseQueue===null&&(t.baseState=u),n.lastRenderedState=u}return[u,s]}function gd(e,t,n){var s=ge,i=et(),u=Re;if(u){if(n===void 0)throw Error(o(407));n=n()}else n=t();var f=!Tt((ze||i).memoizedState,n);f&&(i.memoizedState=n,lt=!0),i=i.queue;var p=jd.bind(null,s,i,e);if(Ma(2048,8,p,[e]),i.getSnapshot!==t||f||Ie!==null&&Ie.memoizedState.tag&1){if(s.flags|=2048,Ys(9,Zl(),vd.bind(null,s,i,n,t),null),He===null)throw Error(o(349));u||(On&124)!==0||yd(s,t,n)}return n}function yd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ge.updateQueue,t===null?(t=sc(),ge.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function vd(e,t,n,s){t.value=n,t.getSnapshot=s,Nd(t)&&Sd(e)}function jd(e,t,n){return n(function(){Nd(t)&&Sd(e)})}function Nd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Tt(e,n)}catch{return!0}}function Sd(e){var t=Ds(e,2);t!==null&&Ut(t,e,2)}function ic(e){var t=yt();if(typeof e=="function"){var n=e;if(e=n(),os){wn(!0);try{n()}finally{wn(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:xn,lastRenderedState:e},t}function wd(e,t,n,s){return e.baseState=n,lc(e,ze,typeof s=="function"?s:xn)}function Cx(e,t,n,s,i){if(Jl(e))throw Error(o(485));if(e=t.action,e!==null){var u={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){u.listeners.push(f)}};N.T!==null?n(!0):u.isTransition=!1,s(u),n=t.pending,n===null?(u.next=t.pending=u,Ad(t,u)):(u.next=n.next,t.pending=n.next=u)}}function Ad(e,t){var n=t.action,s=t.payload,i=e.state;if(t.isTransition){var u=N.T,f={};N.T=f;try{var p=n(i,s),v=N.S;v!==null&&v(f,p),Ed(e,t,p)}catch(C){cc(e,t,C)}finally{N.T=u}}else try{u=n(i,s),Ed(e,t,u)}catch(C){cc(e,t,C)}}function Ed(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(s){Td(e,t,s)},function(s){return cc(e,t,s)}):Td(e,t,n)}function Td(e,t,n){t.status="fulfilled",t.value=n,_d(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,Ad(e,n)))}function cc(e,t,n){var s=e.pending;if(e.pending=null,s!==null){s=s.next;do t.status="rejected",t.reason=n,_d(t),t=t.next;while(t!==s)}e.action=null}function _d(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Rd(e,t){return t}function Cd(e,t){if(Re){var n=He.formState;if(n!==null){e:{var s=ge;if(Re){if(Ke){t:{for(var i=Ke,u=Wt;i.nodeType!==8;){if(!u){i=null;break t}if(i=Jt(i.nextSibling),i===null){i=null;break t}}u=i.data,i=u==="F!"||u==="F"?i:null}if(i){Ke=Jt(i.nextSibling),s=i.data==="F!";break e}}ls(s)}s=!1}s&&(t=n[0])}}return n=yt(),n.memoizedState=n.baseState=t,s={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Rd,lastRenderedState:t},n.queue=s,n=Fd.bind(null,ge,s),s.dispatch=n,s=ic(!1),u=hc.bind(null,ge,!1,s.queue),s=yt(),i={state:t,dispatch:null,action:e,pending:null},s.queue=i,n=Cx.bind(null,ge,i,u,n),i.dispatch=n,s.memoizedState=e,[t,n,!1]}function Od(e){var t=et();return Dd(t,ze,e)}function Dd(e,t,n){if(t=lc(e,t,Rd)[0],e=Ql(xn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var s=Ua(t)}catch(f){throw f===Ta?Ll:f}else s=t;t=et();var i=t.queue,u=i.dispatch;return n!==t.memoizedState&&(ge.flags|=2048,Ys(9,Zl(),Ox.bind(null,i,n),null)),[s,u,e]}function Ox(e,t){e.action=t}function Ud(e){var t=et(),n=ze;if(n!==null)return Dd(t,n,e);et(),t=t.memoizedState,n=et();var s=n.queue.dispatch;return n.memoizedState=e,[t,s,!1]}function Ys(e,t,n,s){return e={tag:e,create:n,deps:s,inst:t,next:null},t=ge.updateQueue,t===null&&(t=sc(),ge.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(s=n.next,n.next=e,e.next=s,t.lastEffect=e),e}function Zl(){return{destroy:void 0,resource:void 0}}function Md(){return et().memoizedState}function Kl(e,t,n,s){var i=yt();s=s===void 0?null:s,ge.flags|=e,i.memoizedState=Ys(1|t,Zl(),n,s)}function Ma(e,t,n,s){var i=et();s=s===void 0?null:s;var u=i.memoizedState.inst;ze!==null&&s!==null&&Pi(s,ze.memoizedState.deps)?i.memoizedState=Ys(t,u,n,s):(ge.flags|=e,i.memoizedState=Ys(1|t,u,n,s))}function zd(e,t){Kl(8390656,8,e,t)}function Bd(e,t){Ma(2048,8,e,t)}function kd(e,t){return Ma(4,2,e,t)}function qd(e,t){return Ma(4,4,e,t)}function Ld(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Hd(e,t,n){n=n!=null?n.concat([e]):null,Ma(4,4,Ld.bind(null,t,e),n)}function oc(){}function Yd(e,t){var n=et();t=t===void 0?null:t;var s=n.memoizedState;return t!==null&&Pi(t,s[1])?s[0]:(n.memoizedState=[e,t],e)}function Vd(e,t){var n=et();t=t===void 0?null:t;var s=n.memoizedState;if(t!==null&&Pi(t,s[1]))return s[0];if(s=e(),os){wn(!0);try{e()}finally{wn(!1)}}return n.memoizedState=[s,t],s}function uc(e,t,n){return n===void 0||(On&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=Zf(),ge.lanes|=e,qn|=e,n)}function Gd(e,t,n,s){return Tt(n,t)?n:qs.current!==null?(e=uc(e,n,s),Tt(e,t)||(lt=!0),e):(On&42)===0?(lt=!0,e.memoizedState=n):(e=Zf(),ge.lanes|=e,qn|=e,t)}function Xd(e,t,n,s,i){var u=G.p;G.p=u!==0&&8>u?u:8;var f=N.T,p={};N.T=p,hc(e,!1,t,n);try{var v=i(),C=N.S;if(C!==null&&C(p,v),v!==null&&typeof v=="object"&&typeof v.then=="function"){var q=Tx(v,s);za(e,t,q,Dt(e))}else za(e,t,s,Dt(e))}catch(V){za(e,t,{then:function(){},status:"rejected",reason:V},Dt())}finally{G.p=u,N.T=f}}function Dx(){}function dc(e,t,n,s){if(e.tag!==5)throw Error(o(476));var i=Qd(e).queue;Xd(e,i,t,te,n===null?Dx:function(){return Zd(e),n(s)})}function Qd(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:te,baseState:te,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:xn,lastRenderedState:te},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:xn,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Zd(e){var t=Qd(e).next.queue;za(e,t,{},Dt())}function fc(){return ft(Ia)}function Kd(){return et().memoizedState}function Jd(){return et().memoizedState}function Ux(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=Dt();e=Rn(n);var s=Cn(t,e,n);s!==null&&(Ut(s,t,n),Ra(s,t,n)),t={cache:Vi()},e.payload=t;return}t=t.return}}function Mx(e,t,n){var s=Dt();n={lane:s,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Jl(e)?$d(t,n):(n=Di(e,t,n,s),n!==null&&(Ut(n,e,s),Wd(n,t,s)))}function Fd(e,t,n){var s=Dt();za(e,t,n,s)}function za(e,t,n,s){var i={lane:s,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Jl(e))$d(t,i);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=t.lastRenderedReducer,u!==null))try{var f=t.lastRenderedState,p=u(f,n);if(i.hasEagerState=!0,i.eagerState=p,Tt(p,f))return Ol(e,t,i,0),He===null&&Cl(),!1}catch{}finally{}if(n=Di(e,t,i,s),n!==null)return Ut(n,e,s),Wd(n,t,s),!0}return!1}function hc(e,t,n,s){if(s={lane:2,revertLane:Qc(),action:s,hasEagerState:!1,eagerState:null,next:null},Jl(e)){if(t)throw Error(o(479))}else t=Di(e,n,s,2),t!==null&&Ut(t,e,2)}function Jl(e){var t=e.alternate;return e===ge||t!==null&&t===ge}function $d(e,t){Ls=Vl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Wd(e,t,n){if((n&4194048)!==0){var s=t.lanes;s&=e.pendingLanes,n|=s,t.lanes=n,lu(e,n)}}var Fl={readContext:ft,use:Xl,useCallback:Fe,useContext:Fe,useEffect:Fe,useImperativeHandle:Fe,useLayoutEffect:Fe,useInsertionEffect:Fe,useMemo:Fe,useReducer:Fe,useRef:Fe,useState:Fe,useDebugValue:Fe,useDeferredValue:Fe,useTransition:Fe,useSyncExternalStore:Fe,useId:Fe,useHostTransitionStatus:Fe,useFormState:Fe,useActionState:Fe,useOptimistic:Fe,useMemoCache:Fe,useCacheRefresh:Fe},Pd={readContext:ft,use:Xl,useCallback:function(e,t){return yt().memoizedState=[e,t===void 0?null:t],e},useContext:ft,useEffect:zd,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,Kl(4194308,4,Ld.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Kl(4194308,4,e,t)},useInsertionEffect:function(e,t){Kl(4,2,e,t)},useMemo:function(e,t){var n=yt();t=t===void 0?null:t;var s=e();if(os){wn(!0);try{e()}finally{wn(!1)}}return n.memoizedState=[s,t],s},useReducer:function(e,t,n){var s=yt();if(n!==void 0){var i=n(t);if(os){wn(!0);try{n(t)}finally{wn(!1)}}}else i=t;return s.memoizedState=s.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},s.queue=e,e=e.dispatch=Mx.bind(null,ge,e),[s.memoizedState,e]},useRef:function(e){var t=yt();return e={current:e},t.memoizedState=e},useState:function(e){e=ic(e);var t=e.queue,n=Fd.bind(null,ge,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:oc,useDeferredValue:function(e,t){var n=yt();return uc(n,e,t)},useTransition:function(){var e=ic(!1);return e=Xd.bind(null,ge,e.queue,!0,!1),yt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var s=ge,i=yt();if(Re){if(n===void 0)throw Error(o(407));n=n()}else{if(n=t(),He===null)throw Error(o(349));(Ae&124)!==0||yd(s,t,n)}i.memoizedState=n;var u={value:n,getSnapshot:t};return i.queue=u,zd(jd.bind(null,s,u,e),[e]),s.flags|=2048,Ys(9,Zl(),vd.bind(null,s,u,n,t),null),n},useId:function(){var e=yt(),t=He.identifierPrefix;if(Re){var n=hn,s=fn;n=(s&~(1<<32-Et(s)-1)).toString(32)+n,t="«"+t+"R"+n,n=Gl++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=_x++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:fc,useFormState:Cd,useActionState:Cd,useOptimistic:function(e){var t=yt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=hc.bind(null,ge,!0,n),n.dispatch=t,[e,t]},useMemoCache:ac,useCacheRefresh:function(){return yt().memoizedState=Ux.bind(null,ge)}},Id={readContext:ft,use:Xl,useCallback:Yd,useContext:ft,useEffect:Bd,useImperativeHandle:Hd,useInsertionEffect:kd,useLayoutEffect:qd,useMemo:Vd,useReducer:Ql,useRef:Md,useState:function(){return Ql(xn)},useDebugValue:oc,useDeferredValue:function(e,t){var n=et();return Gd(n,ze.memoizedState,e,t)},useTransition:function(){var e=Ql(xn)[0],t=et().memoizedState;return[typeof e=="boolean"?e:Ua(e),t]},useSyncExternalStore:gd,useId:Kd,useHostTransitionStatus:fc,useFormState:Od,useActionState:Od,useOptimistic:function(e,t){var n=et();return wd(n,ze,e,t)},useMemoCache:ac,useCacheRefresh:Jd},zx={readContext:ft,use:Xl,useCallback:Yd,useContext:ft,useEffect:Bd,useImperativeHandle:Hd,useInsertionEffect:kd,useLayoutEffect:qd,useMemo:Vd,useReducer:rc,useRef:Md,useState:function(){return rc(xn)},useDebugValue:oc,useDeferredValue:function(e,t){var n=et();return ze===null?uc(n,e,t):Gd(n,ze.memoizedState,e,t)},useTransition:function(){var e=rc(xn)[0],t=et().memoizedState;return[typeof e=="boolean"?e:Ua(e),t]},useSyncExternalStore:gd,useId:Kd,useHostTransitionStatus:fc,useFormState:Ud,useActionState:Ud,useOptimistic:function(e,t){var n=et();return ze!==null?wd(n,ze,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:ac,useCacheRefresh:Jd},Vs=null,Ba=0;function $l(e){var t=Ba;return Ba+=1,Vs===null&&(Vs=[]),ud(Vs,e,t)}function ka(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Wl(e,t){throw t.$$typeof===S?Error(o(525)):(e=Object.prototype.toString.call(t),Error(o(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function ef(e){var t=e._init;return t(e._payload)}function tf(e){function t(E,w){if(e){var _=E.deletions;_===null?(E.deletions=[w],E.flags|=16):_.push(w)}}function n(E,w){if(!e)return null;for(;w!==null;)t(E,w),w=w.sibling;return null}function s(E){for(var w=new Map;E!==null;)E.key!==null?w.set(E.key,E):w.set(E.index,E),E=E.sibling;return w}function i(E,w){return E=dn(E,w),E.index=0,E.sibling=null,E}function u(E,w,_){return E.index=_,e?(_=E.alternate,_!==null?(_=_.index,_<w?(E.flags|=67108866,w):_):(E.flags|=67108866,w)):(E.flags|=1048576,w)}function f(E){return e&&E.alternate===null&&(E.flags|=67108866),E}function p(E,w,_,H){return w===null||w.tag!==6?(w=Mi(_,E.mode,H),w.return=E,w):(w=i(w,_),w.return=E,w)}function v(E,w,_,H){var se=_.type;return se===O?q(E,w,_.props.children,H,_.key):w!==null&&(w.elementType===se||typeof se=="object"&&se!==null&&se.$$typeof===Q&&ef(se)===w.type)?(w=i(w,_.props),ka(w,_),w.return=E,w):(w=Ul(_.type,_.key,_.props,null,E.mode,H),ka(w,_),w.return=E,w)}function C(E,w,_,H){return w===null||w.tag!==4||w.stateNode.containerInfo!==_.containerInfo||w.stateNode.implementation!==_.implementation?(w=zi(_,E.mode,H),w.return=E,w):(w=i(w,_.children||[]),w.return=E,w)}function q(E,w,_,H,se){return w===null||w.tag!==7?(w=ts(_,E.mode,H,se),w.return=E,w):(w=i(w,_),w.return=E,w)}function V(E,w,_){if(typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint")return w=Mi(""+w,E.mode,_),w.return=E,w;if(typeof w=="object"&&w!==null){switch(w.$$typeof){case U:return _=Ul(w.type,w.key,w.props,null,E.mode,_),ka(_,w),_.return=E,_;case B:return w=zi(w,E.mode,_),w.return=E,w;case Q:var H=w._init;return w=H(w._payload),V(E,w,_)}if(qe(w)||de(w))return w=ts(w,E.mode,_,null),w.return=E,w;if(typeof w.then=="function")return V(E,$l(w),_);if(w.$$typeof===F)return V(E,kl(E,w),_);Wl(E,w)}return null}function D(E,w,_,H){var se=w!==null?w.key:null;if(typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint")return se!==null?null:p(E,w,""+_,H);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case U:return _.key===se?v(E,w,_,H):null;case B:return _.key===se?C(E,w,_,H):null;case Q:return se=_._init,_=se(_._payload),D(E,w,_,H)}if(qe(_)||de(_))return se!==null?null:q(E,w,_,H,null);if(typeof _.then=="function")return D(E,w,$l(_),H);if(_.$$typeof===F)return D(E,w,kl(E,_),H);Wl(E,_)}return null}function M(E,w,_,H,se){if(typeof H=="string"&&H!==""||typeof H=="number"||typeof H=="bigint")return E=E.get(_)||null,p(w,E,""+H,se);if(typeof H=="object"&&H!==null){switch(H.$$typeof){case U:return E=E.get(H.key===null?_:H.key)||null,v(w,E,H,se);case B:return E=E.get(H.key===null?_:H.key)||null,C(w,E,H,se);case Q:var ve=H._init;return H=ve(H._payload),M(E,w,_,H,se)}if(qe(H)||de(H))return E=E.get(_)||null,q(w,E,H,se,null);if(typeof H.then=="function")return M(E,w,_,$l(H),se);if(H.$$typeof===F)return M(E,w,_,kl(w,H),se);Wl(w,H)}return null}function ue(E,w,_,H){for(var se=null,ve=null,re=w,ce=w=0,it=null;re!==null&&ce<_.length;ce++){re.index>ce?(it=re,re=null):it=re.sibling;var _e=D(E,re,_[ce],H);if(_e===null){re===null&&(re=it);break}e&&re&&_e.alternate===null&&t(E,re),w=u(_e,w,ce),ve===null?se=_e:ve.sibling=_e,ve=_e,re=it}if(ce===_.length)return n(E,re),Re&&ss(E,ce),se;if(re===null){for(;ce<_.length;ce++)re=V(E,_[ce],H),re!==null&&(w=u(re,w,ce),ve===null?se=re:ve.sibling=re,ve=re);return Re&&ss(E,ce),se}for(re=s(re);ce<_.length;ce++)it=M(re,E,ce,_[ce],H),it!==null&&(e&&it.alternate!==null&&re.delete(it.key===null?ce:it.key),w=u(it,w,ce),ve===null?se=it:ve.sibling=it,ve=it);return e&&re.forEach(function(Kn){return t(E,Kn)}),Re&&ss(E,ce),se}function ie(E,w,_,H){if(_==null)throw Error(o(151));for(var se=null,ve=null,re=w,ce=w=0,it=null,_e=_.next();re!==null&&!_e.done;ce++,_e=_.next()){re.index>ce?(it=re,re=null):it=re.sibling;var Kn=D(E,re,_e.value,H);if(Kn===null){re===null&&(re=it);break}e&&re&&Kn.alternate===null&&t(E,re),w=u(Kn,w,ce),ve===null?se=Kn:ve.sibling=Kn,ve=Kn,re=it}if(_e.done)return n(E,re),Re&&ss(E,ce),se;if(re===null){for(;!_e.done;ce++,_e=_.next())_e=V(E,_e.value,H),_e!==null&&(w=u(_e,w,ce),ve===null?se=_e:ve.sibling=_e,ve=_e);return Re&&ss(E,ce),se}for(re=s(re);!_e.done;ce++,_e=_.next())_e=M(re,E,ce,_e.value,H),_e!==null&&(e&&_e.alternate!==null&&re.delete(_e.key===null?ce:_e.key),w=u(_e,w,ce),ve===null?se=_e:ve.sibling=_e,ve=_e);return e&&re.forEach(function(Bb){return t(E,Bb)}),Re&&ss(E,ce),se}function ke(E,w,_,H){if(typeof _=="object"&&_!==null&&_.type===O&&_.key===null&&(_=_.props.children),typeof _=="object"&&_!==null){switch(_.$$typeof){case U:e:{for(var se=_.key;w!==null;){if(w.key===se){if(se=_.type,se===O){if(w.tag===7){n(E,w.sibling),H=i(w,_.props.children),H.return=E,E=H;break e}}else if(w.elementType===se||typeof se=="object"&&se!==null&&se.$$typeof===Q&&ef(se)===w.type){n(E,w.sibling),H=i(w,_.props),ka(H,_),H.return=E,E=H;break e}n(E,w);break}else t(E,w);w=w.sibling}_.type===O?(H=ts(_.props.children,E.mode,H,_.key),H.return=E,E=H):(H=Ul(_.type,_.key,_.props,null,E.mode,H),ka(H,_),H.return=E,E=H)}return f(E);case B:e:{for(se=_.key;w!==null;){if(w.key===se)if(w.tag===4&&w.stateNode.containerInfo===_.containerInfo&&w.stateNode.implementation===_.implementation){n(E,w.sibling),H=i(w,_.children||[]),H.return=E,E=H;break e}else{n(E,w);break}else t(E,w);w=w.sibling}H=zi(_,E.mode,H),H.return=E,E=H}return f(E);case Q:return se=_._init,_=se(_._payload),ke(E,w,_,H)}if(qe(_))return ue(E,w,_,H);if(de(_)){if(se=de(_),typeof se!="function")throw Error(o(150));return _=se.call(_),ie(E,w,_,H)}if(typeof _.then=="function")return ke(E,w,$l(_),H);if(_.$$typeof===F)return ke(E,w,kl(E,_),H);Wl(E,_)}return typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint"?(_=""+_,w!==null&&w.tag===6?(n(E,w.sibling),H=i(w,_),H.return=E,E=H):(n(E,w),H=Mi(_,E.mode,H),H.return=E,E=H),f(E)):n(E,w)}return function(E,w,_,H){try{Ba=0;var se=ke(E,w,_,H);return Vs=null,se}catch(re){if(re===Ta||re===Ll)throw re;var ve=_t(29,re,null,E.mode);return ve.lanes=H,ve.return=E,ve}finally{}}}var Gs=tf(!0),nf=tf(!1),Vt=Y(null),Pt=null;function Dn(e){var t=e.alternate;W(st,st.current&1),W(Vt,e),Pt===null&&(t===null||qs.current!==null||t.memoizedState!==null)&&(Pt=e)}function sf(e){if(e.tag===22){if(W(st,st.current),W(Vt,e),Pt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Pt=e)}}else Un()}function Un(){W(st,st.current),W(Vt,Vt.current)}function bn(e){ee(Vt),Pt===e&&(Pt=null),ee(st)}var st=Y(0);function Pl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||so(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function mc(e,t,n,s){t=e.memoizedState,n=n(s,t),n=n==null?t:y({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var pc={enqueueSetState:function(e,t,n){e=e._reactInternals;var s=Dt(),i=Rn(s);i.payload=t,n!=null&&(i.callback=n),t=Cn(e,i,s),t!==null&&(Ut(t,e,s),Ra(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var s=Dt(),i=Rn(s);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Cn(e,i,s),t!==null&&(Ut(t,e,s),Ra(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Dt(),s=Rn(n);s.tag=2,t!=null&&(s.callback=t),t=Cn(e,s,n),t!==null&&(Ut(t,e,n),Ra(t,e,n))}};function af(e,t,n,s,i,u,f){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(s,u,f):t.prototype&&t.prototype.isPureReactComponent?!ya(n,s)||!ya(i,u):!0}function lf(e,t,n,s){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,s),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,s),t.state!==e&&pc.enqueueReplaceState(t,t.state,null)}function us(e,t){var n=t;if("ref"in t){n={};for(var s in t)s!=="ref"&&(n[s]=t[s])}if(e=e.defaultProps){n===t&&(n=y({},n));for(var i in e)n[i]===void 0&&(n[i]=e[i])}return n}var Il=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function rf(e){Il(e)}function cf(e){console.error(e)}function of(e){Il(e)}function er(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(s){setTimeout(function(){throw s})}}function uf(e,t,n){try{var s=e.onCaughtError;s(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function xc(e,t,n){return n=Rn(n),n.tag=3,n.payload={element:null},n.callback=function(){er(e,t)},n}function df(e){return e=Rn(e),e.tag=3,e}function ff(e,t,n,s){var i=n.type.getDerivedStateFromError;if(typeof i=="function"){var u=s.value;e.payload=function(){return i(u)},e.callback=function(){uf(t,n,s)}}var f=n.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(e.callback=function(){uf(t,n,s),typeof i!="function"&&(Ln===null?Ln=new Set([this]):Ln.add(this));var p=s.stack;this.componentDidCatch(s.value,{componentStack:p!==null?p:""})})}function Bx(e,t,n,s,i){if(n.flags|=32768,s!==null&&typeof s=="object"&&typeof s.then=="function"){if(t=n.alternate,t!==null&&wa(t,n,i,!0),n=Vt.current,n!==null){switch(n.tag){case 13:return Pt===null?Hc():n.alternate===null&&Je===0&&(Je=3),n.flags&=-257,n.flags|=65536,n.lanes=i,s===Qi?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([s]):t.add(s),Vc(e,s,i)),!1;case 22:return n.flags|=65536,s===Qi?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([s])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([s]):n.add(s)),Vc(e,s,i)),!1}throw Error(o(435,n.tag))}return Vc(e,s,i),Hc(),!1}if(Re)return t=Vt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=i,s!==qi&&(e=Error(o(422),{cause:s}),Sa(qt(e,n)))):(s!==qi&&(t=Error(o(423),{cause:s}),Sa(qt(t,n))),e=e.current.alternate,e.flags|=65536,i&=-i,e.lanes|=i,s=qt(s,n),i=xc(e.stateNode,s,i),Ji(e,i),Je!==4&&(Je=2)),!1;var u=Error(o(520),{cause:s});if(u=qt(u,n),Xa===null?Xa=[u]:Xa.push(u),Je!==4&&(Je=2),t===null)return!0;s=qt(s,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=i&-i,n.lanes|=e,e=xc(n.stateNode,s,e),Ji(n,e),!1;case 1:if(t=n.type,u=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(Ln===null||!Ln.has(u))))return n.flags|=65536,i&=-i,n.lanes|=i,i=df(i),ff(i,e,n,s),Ji(n,i),!1}n=n.return}while(n!==null);return!1}var hf=Error(o(461)),lt=!1;function ct(e,t,n,s){t.child=e===null?nf(t,null,n,s):Gs(t,e.child,n,s)}function mf(e,t,n,s,i){n=n.render;var u=t.ref;if("ref"in s){var f={};for(var p in s)p!=="ref"&&(f[p]=s[p])}else f=s;return is(t),s=Ii(e,t,n,f,u,i),p=ec(),e!==null&&!lt?(tc(e,t,i),gn(e,t,i)):(Re&&p&&Bi(t),t.flags|=1,ct(e,t,s,i),t.child)}function pf(e,t,n,s,i){if(e===null){var u=n.type;return typeof u=="function"&&!Ui(u)&&u.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=u,xf(e,t,u,s,i)):(e=Ul(n.type,null,s,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(u=e.child,!wc(e,i)){var f=u.memoizedProps;if(n=n.compare,n=n!==null?n:ya,n(f,s)&&e.ref===t.ref)return gn(e,t,i)}return t.flags|=1,e=dn(u,s),e.ref=t.ref,e.return=t,t.child=e}function xf(e,t,n,s,i){if(e!==null){var u=e.memoizedProps;if(ya(u,s)&&e.ref===t.ref)if(lt=!1,t.pendingProps=s=u,wc(e,i))(e.flags&131072)!==0&&(lt=!0);else return t.lanes=e.lanes,gn(e,t,i)}return bc(e,t,n,s,i)}function bf(e,t,n){var s=t.pendingProps,i=s.children,u=e!==null?e.memoizedState:null;if(s.mode==="hidden"){if((t.flags&128)!==0){if(s=u!==null?u.baseLanes|n:n,e!==null){for(i=t.child=e.child,u=0;i!==null;)u=u|i.lanes|i.childLanes,i=i.sibling;t.childLanes=u&~s}else t.childLanes=0,t.child=null;return gf(e,t,s,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&ql(t,u!==null?u.cachePool:null),u!==null?pd(t,u):$i(),sf(t);else return t.lanes=t.childLanes=536870912,gf(e,t,u!==null?u.baseLanes|n:n,n)}else u!==null?(ql(t,u.cachePool),pd(t,u),Un(),t.memoizedState=null):(e!==null&&ql(t,null),$i(),Un());return ct(e,t,i,n),t.child}function gf(e,t,n,s){var i=Xi();return i=i===null?null:{parent:nt._currentValue,pool:i},t.memoizedState={baseLanes:n,cachePool:i},e!==null&&ql(t,null),$i(),sf(t),e!==null&&wa(e,t,s,!0),null}function tr(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(o(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function bc(e,t,n,s,i){return is(t),n=Ii(e,t,n,s,void 0,i),s=ec(),e!==null&&!lt?(tc(e,t,i),gn(e,t,i)):(Re&&s&&Bi(t),t.flags|=1,ct(e,t,n,i),t.child)}function yf(e,t,n,s,i,u){return is(t),t.updateQueue=null,n=bd(t,s,n,i),xd(e),s=ec(),e!==null&&!lt?(tc(e,t,u),gn(e,t,u)):(Re&&s&&Bi(t),t.flags|=1,ct(e,t,n,u),t.child)}function vf(e,t,n,s,i){if(is(t),t.stateNode===null){var u=Us,f=n.contextType;typeof f=="object"&&f!==null&&(u=ft(f)),u=new n(s,u),t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=pc,t.stateNode=u,u._reactInternals=t,u=t.stateNode,u.props=s,u.state=t.memoizedState,u.refs={},Zi(t),f=n.contextType,u.context=typeof f=="object"&&f!==null?ft(f):Us,u.state=t.memoizedState,f=n.getDerivedStateFromProps,typeof f=="function"&&(mc(t,n,f,s),u.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(f=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),f!==u.state&&pc.enqueueReplaceState(u,u.state,null),Oa(t,s,u,i),Ca(),u.state=t.memoizedState),typeof u.componentDidMount=="function"&&(t.flags|=4194308),s=!0}else if(e===null){u=t.stateNode;var p=t.memoizedProps,v=us(n,p);u.props=v;var C=u.context,q=n.contextType;f=Us,typeof q=="object"&&q!==null&&(f=ft(q));var V=n.getDerivedStateFromProps;q=typeof V=="function"||typeof u.getSnapshotBeforeUpdate=="function",p=t.pendingProps!==p,q||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(p||C!==f)&&lf(t,u,s,f),_n=!1;var D=t.memoizedState;u.state=D,Oa(t,s,u,i),Ca(),C=t.memoizedState,p||D!==C||_n?(typeof V=="function"&&(mc(t,n,V,s),C=t.memoizedState),(v=_n||af(t,n,v,s,D,C,f))?(q||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(t.flags|=4194308)):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=s,t.memoizedState=C),u.props=s,u.state=C,u.context=f,s=v):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),s=!1)}else{u=t.stateNode,Ki(e,t),f=t.memoizedProps,q=us(n,f),u.props=q,V=t.pendingProps,D=u.context,C=n.contextType,v=Us,typeof C=="object"&&C!==null&&(v=ft(C)),p=n.getDerivedStateFromProps,(C=typeof p=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(f!==V||D!==v)&&lf(t,u,s,v),_n=!1,D=t.memoizedState,u.state=D,Oa(t,s,u,i),Ca();var M=t.memoizedState;f!==V||D!==M||_n||e!==null&&e.dependencies!==null&&Bl(e.dependencies)?(typeof p=="function"&&(mc(t,n,p,s),M=t.memoizedState),(q=_n||af(t,n,q,s,D,M,v)||e!==null&&e.dependencies!==null&&Bl(e.dependencies))?(C||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(s,M,v),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(s,M,v)),typeof u.componentDidUpdate=="function"&&(t.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof u.componentDidUpdate!="function"||f===e.memoizedProps&&D===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&D===e.memoizedState||(t.flags|=1024),t.memoizedProps=s,t.memoizedState=M),u.props=s,u.state=M,u.context=v,s=q):(typeof u.componentDidUpdate!="function"||f===e.memoizedProps&&D===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&D===e.memoizedState||(t.flags|=1024),s=!1)}return u=s,tr(e,t),s=(t.flags&128)!==0,u||s?(u=t.stateNode,n=s&&typeof n.getDerivedStateFromError!="function"?null:u.render(),t.flags|=1,e!==null&&s?(t.child=Gs(t,e.child,null,i),t.child=Gs(t,null,n,i)):ct(e,t,n,i),t.memoizedState=u.state,e=t.child):e=gn(e,t,i),e}function jf(e,t,n,s){return Na(),t.flags|=256,ct(e,t,n,s),t.child}var gc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function yc(e){return{baseLanes:e,cachePool:id()}}function vc(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=Gt),e}function Nf(e,t,n){var s=t.pendingProps,i=!1,u=(t.flags&128)!==0,f;if((f=u)||(f=e!==null&&e.memoizedState===null?!1:(st.current&2)!==0),f&&(i=!0,t.flags&=-129),f=(t.flags&32)!==0,t.flags&=-33,e===null){if(Re){if(i?Dn(t):Un(),Re){var p=Ke,v;if(v=p){e:{for(v=p,p=Wt;v.nodeType!==8;){if(!p){p=null;break e}if(v=Jt(v.nextSibling),v===null){p=null;break e}}p=v}p!==null?(t.memoizedState={dehydrated:p,treeContext:ns!==null?{id:fn,overflow:hn}:null,retryLane:536870912,hydrationErrors:null},v=_t(18,null,null,0),v.stateNode=p,v.return=t,t.child=v,pt=t,Ke=null,v=!0):v=!1}v||ls(t)}if(p=t.memoizedState,p!==null&&(p=p.dehydrated,p!==null))return so(p)?t.lanes=32:t.lanes=536870912,null;bn(t)}return p=s.children,s=s.fallback,i?(Un(),i=t.mode,p=nr({mode:"hidden",children:p},i),s=ts(s,i,n,null),p.return=t,s.return=t,p.sibling=s,t.child=p,i=t.child,i.memoizedState=yc(n),i.childLanes=vc(e,f,n),t.memoizedState=gc,s):(Dn(t),jc(t,p))}if(v=e.memoizedState,v!==null&&(p=v.dehydrated,p!==null)){if(u)t.flags&256?(Dn(t),t.flags&=-257,t=Nc(e,t,n)):t.memoizedState!==null?(Un(),t.child=e.child,t.flags|=128,t=null):(Un(),i=s.fallback,p=t.mode,s=nr({mode:"visible",children:s.children},p),i=ts(i,p,n,null),i.flags|=2,s.return=t,i.return=t,s.sibling=i,t.child=s,Gs(t,e.child,null,n),s=t.child,s.memoizedState=yc(n),s.childLanes=vc(e,f,n),t.memoizedState=gc,t=i);else if(Dn(t),so(p)){if(f=p.nextSibling&&p.nextSibling.dataset,f)var C=f.dgst;f=C,s=Error(o(419)),s.stack="",s.digest=f,Sa({value:s,source:null,stack:null}),t=Nc(e,t,n)}else if(lt||wa(e,t,n,!1),f=(n&e.childLanes)!==0,lt||f){if(f=He,f!==null&&(s=n&-n,s=(s&42)!==0?1:ai(s),s=(s&(f.suspendedLanes|n))!==0?0:s,s!==0&&s!==v.retryLane))throw v.retryLane=s,Ds(e,s),Ut(f,e,s),hf;p.data==="$?"||Hc(),t=Nc(e,t,n)}else p.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=v.treeContext,Ke=Jt(p.nextSibling),pt=t,Re=!0,as=null,Wt=!1,e!==null&&(Ht[Yt++]=fn,Ht[Yt++]=hn,Ht[Yt++]=ns,fn=e.id,hn=e.overflow,ns=t),t=jc(t,s.children),t.flags|=4096);return t}return i?(Un(),i=s.fallback,p=t.mode,v=e.child,C=v.sibling,s=dn(v,{mode:"hidden",children:s.children}),s.subtreeFlags=v.subtreeFlags&65011712,C!==null?i=dn(C,i):(i=ts(i,p,n,null),i.flags|=2),i.return=t,s.return=t,s.sibling=i,t.child=s,s=i,i=t.child,p=e.child.memoizedState,p===null?p=yc(n):(v=p.cachePool,v!==null?(C=nt._currentValue,v=v.parent!==C?{parent:C,pool:C}:v):v=id(),p={baseLanes:p.baseLanes|n,cachePool:v}),i.memoizedState=p,i.childLanes=vc(e,f,n),t.memoizedState=gc,s):(Dn(t),n=e.child,e=n.sibling,n=dn(n,{mode:"visible",children:s.children}),n.return=t,n.sibling=null,e!==null&&(f=t.deletions,f===null?(t.deletions=[e],t.flags|=16):f.push(e)),t.child=n,t.memoizedState=null,n)}function jc(e,t){return t=nr({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function nr(e,t){return e=_t(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Nc(e,t,n){return Gs(t,e.child,null,n),e=jc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Sf(e,t,n){e.lanes|=t;var s=e.alternate;s!==null&&(s.lanes|=t),Hi(e.return,t,n)}function Sc(e,t,n,s,i){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:s,tail:n,tailMode:i}:(u.isBackwards=t,u.rendering=null,u.renderingStartTime=0,u.last=s,u.tail=n,u.tailMode=i)}function wf(e,t,n){var s=t.pendingProps,i=s.revealOrder,u=s.tail;if(ct(e,t,s.children,n),s=st.current,(s&2)!==0)s=s&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Sf(e,n,t);else if(e.tag===19)Sf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}s&=1}switch(W(st,s),i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Pl(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Sc(t,!1,i,n,u);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Pl(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Sc(t,!0,n,null,u);break;case"together":Sc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function gn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),qn|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(wa(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(o(153));if(t.child!==null){for(e=t.child,n=dn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=dn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function wc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Bl(e)))}function kx(e,t,n){switch(t.tag){case 3:De(t,t.stateNode.containerInfo),Tn(t,nt,e.memoizedState.cache),Na();break;case 27:case 5:Se(t);break;case 4:De(t,t.stateNode.containerInfo);break;case 10:Tn(t,t.type,t.memoizedProps.value);break;case 13:var s=t.memoizedState;if(s!==null)return s.dehydrated!==null?(Dn(t),t.flags|=128,null):(n&t.child.childLanes)!==0?Nf(e,t,n):(Dn(t),e=gn(e,t,n),e!==null?e.sibling:null);Dn(t);break;case 19:var i=(e.flags&128)!==0;if(s=(n&t.childLanes)!==0,s||(wa(e,t,n,!1),s=(n&t.childLanes)!==0),i){if(s)return wf(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),W(st,st.current),s)break;return null;case 22:case 23:return t.lanes=0,bf(e,t,n);case 24:Tn(t,nt,e.memoizedState.cache)}return gn(e,t,n)}function Af(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)lt=!0;else{if(!wc(e,n)&&(t.flags&128)===0)return lt=!1,kx(e,t,n);lt=(e.flags&131072)!==0}else lt=!1,Re&&(t.flags&1048576)!==0&&ed(t,zl,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var s=t.elementType,i=s._init;if(s=i(s._payload),t.type=s,typeof s=="function")Ui(s)?(e=us(s,e),t.tag=1,t=vf(null,t,s,e,n)):(t.tag=0,t=bc(null,t,s,e,n));else{if(s!=null){if(i=s.$$typeof,i===ae){t.tag=11,t=mf(null,t,s,e,n);break e}else if(i===le){t.tag=14,t=pf(null,t,s,e,n);break e}}throw t=Ge(s)||s,Error(o(306,t,""))}}return t;case 0:return bc(e,t,t.type,t.pendingProps,n);case 1:return s=t.type,i=us(s,t.pendingProps),vf(e,t,s,i,n);case 3:e:{if(De(t,t.stateNode.containerInfo),e===null)throw Error(o(387));s=t.pendingProps;var u=t.memoizedState;i=u.element,Ki(e,t),Oa(t,s,null,n);var f=t.memoizedState;if(s=f.cache,Tn(t,nt,s),s!==u.cache&&Yi(t,[nt],n,!0),Ca(),s=f.element,u.isDehydrated)if(u={element:s,isDehydrated:!1,cache:f.cache},t.updateQueue.baseState=u,t.memoizedState=u,t.flags&256){t=jf(e,t,s,n);break e}else if(s!==i){i=qt(Error(o(424)),t),Sa(i),t=jf(e,t,s,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ke=Jt(e.firstChild),pt=t,Re=!0,as=null,Wt=!0,n=nf(t,null,s,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Na(),s===i){t=gn(e,t,n);break e}ct(e,t,s,n)}t=t.child}return t;case 26:return tr(e,t),e===null?(n=Rh(t.type,null,t.pendingProps,null))?t.memoizedState=n:Re||(n=t.type,e=t.pendingProps,s=xr(I.current).createElement(n),s[dt]=t,s[bt]=e,ut(s,n,e),at(s),t.stateNode=s):t.memoizedState=Rh(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Se(t),e===null&&Re&&(s=t.stateNode=Eh(t.type,t.pendingProps,I.current),pt=t,Wt=!0,i=Ke,Vn(t.type)?(ao=i,Ke=Jt(s.firstChild)):Ke=i),ct(e,t,t.pendingProps.children,n),tr(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Re&&((i=s=Ke)&&(s=db(s,t.type,t.pendingProps,Wt),s!==null?(t.stateNode=s,pt=t,Ke=Jt(s.firstChild),Wt=!1,i=!0):i=!1),i||ls(t)),Se(t),i=t.type,u=t.pendingProps,f=e!==null?e.memoizedProps:null,s=u.children,eo(i,u)?s=null:f!==null&&eo(i,f)&&(t.flags|=32),t.memoizedState!==null&&(i=Ii(e,t,Rx,null,null,n),Ia._currentValue=i),tr(e,t),ct(e,t,s,n),t.child;case 6:return e===null&&Re&&((e=n=Ke)&&(n=fb(n,t.pendingProps,Wt),n!==null?(t.stateNode=n,pt=t,Ke=null,e=!0):e=!1),e||ls(t)),null;case 13:return Nf(e,t,n);case 4:return De(t,t.stateNode.containerInfo),s=t.pendingProps,e===null?t.child=Gs(t,null,s,n):ct(e,t,s,n),t.child;case 11:return mf(e,t,t.type,t.pendingProps,n);case 7:return ct(e,t,t.pendingProps,n),t.child;case 8:return ct(e,t,t.pendingProps.children,n),t.child;case 12:return ct(e,t,t.pendingProps.children,n),t.child;case 10:return s=t.pendingProps,Tn(t,t.type,s.value),ct(e,t,s.children,n),t.child;case 9:return i=t.type._context,s=t.pendingProps.children,is(t),i=ft(i),s=s(i),t.flags|=1,ct(e,t,s,n),t.child;case 14:return pf(e,t,t.type,t.pendingProps,n);case 15:return xf(e,t,t.type,t.pendingProps,n);case 19:return wf(e,t,n);case 31:return s=t.pendingProps,n=t.mode,s={mode:s.mode,children:s.children},e===null?(n=nr(s,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=dn(e.child,s),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return bf(e,t,n);case 24:return is(t),s=ft(nt),e===null?(i=Xi(),i===null&&(i=He,u=Vi(),i.pooledCache=u,u.refCount++,u!==null&&(i.pooledCacheLanes|=n),i=u),t.memoizedState={parent:s,cache:i},Zi(t),Tn(t,nt,i)):((e.lanes&n)!==0&&(Ki(e,t),Oa(t,null,null,n),Ca()),i=e.memoizedState,u=t.memoizedState,i.parent!==s?(i={parent:s,cache:s},t.memoizedState=i,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=i),Tn(t,nt,s)):(s=u.cache,Tn(t,nt,s),s!==i.cache&&Yi(t,[nt],n,!0))),ct(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(o(156,t.tag))}function yn(e){e.flags|=4}function Ef(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Mh(t)){if(t=Vt.current,t!==null&&((Ae&4194048)===Ae?Pt!==null:(Ae&62914560)!==Ae&&(Ae&536870912)===0||t!==Pt))throw _a=Qi,cd;e.flags|=8192}}function sr(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?su():536870912,e.lanes|=t,Ks|=t)}function qa(e,t){if(!Re)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var s=null;n!==null;)n.alternate!==null&&(s=n),n=n.sibling;s===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:s.sibling=null}}function Ze(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,s=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,s|=i.subtreeFlags&65011712,s|=i.flags&65011712,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,s|=i.subtreeFlags,s|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=s,e.childLanes=n,t}function qx(e,t,n){var s=t.pendingProps;switch(ki(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ze(t),null;case 1:return Ze(t),null;case 3:return n=t.stateNode,s=null,e!==null&&(s=e.memoizedState.cache),t.memoizedState.cache!==s&&(t.flags|=2048),pn(nt),ne(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(ja(t)?yn(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,sd())),Ze(t),null;case 26:return n=t.memoizedState,e===null?(yn(t),n!==null?(Ze(t),Ef(t,n)):(Ze(t),t.flags&=-16777217)):n?n!==e.memoizedState?(yn(t),Ze(t),Ef(t,n)):(Ze(t),t.flags&=-16777217):(e.memoizedProps!==s&&yn(t),Ze(t),t.flags&=-16777217),null;case 27:oe(t),n=I.current;var i=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==s&&yn(t);else{if(!s){if(t.stateNode===null)throw Error(o(166));return Ze(t),null}e=R.current,ja(t)?td(t):(e=Eh(i,s,n),t.stateNode=e,yn(t))}return Ze(t),null;case 5:if(oe(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==s&&yn(t);else{if(!s){if(t.stateNode===null)throw Error(o(166));return Ze(t),null}if(e=R.current,ja(t))td(t);else{switch(i=xr(I.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof s.is=="string"?i.createElement("select",{is:s.is}):i.createElement("select"),s.multiple?e.multiple=!0:s.size&&(e.size=s.size);break;default:e=typeof s.is=="string"?i.createElement(n,{is:s.is}):i.createElement(n)}}e[dt]=t,e[bt]=s;e:for(i=t.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;i.sibling===null;){if(i.return===null||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(ut(e,n,s),n){case"button":case"input":case"select":case"textarea":e=!!s.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&yn(t)}}return Ze(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==s&&yn(t);else{if(typeof s!="string"&&t.stateNode===null)throw Error(o(166));if(e=I.current,ja(t)){if(e=t.stateNode,n=t.memoizedProps,s=null,i=pt,i!==null)switch(i.tag){case 27:case 5:s=i.memoizedProps}e[dt]=t,e=!!(e.nodeValue===n||s!==null&&s.suppressHydrationWarning===!0||yh(e.nodeValue,n)),e||ls(t)}else e=xr(e).createTextNode(s),e[dt]=t,t.stateNode=e}return Ze(t),null;case 13:if(s=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(i=ja(t),s!==null&&s.dehydrated!==null){if(e===null){if(!i)throw Error(o(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(o(317));i[dt]=t}else Na(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ze(t),i=!1}else i=sd(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return t.flags&256?(bn(t),t):(bn(t),null)}if(bn(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=s!==null,e=e!==null&&e.memoizedState!==null,n){s=t.child,i=null,s.alternate!==null&&s.alternate.memoizedState!==null&&s.alternate.memoizedState.cachePool!==null&&(i=s.alternate.memoizedState.cachePool.pool);var u=null;s.memoizedState!==null&&s.memoizedState.cachePool!==null&&(u=s.memoizedState.cachePool.pool),u!==i&&(s.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),sr(t,t.updateQueue),Ze(t),null;case 4:return ne(),e===null&&Fc(t.stateNode.containerInfo),Ze(t),null;case 10:return pn(t.type),Ze(t),null;case 19:if(ee(st),i=t.memoizedState,i===null)return Ze(t),null;if(s=(t.flags&128)!==0,u=i.rendering,u===null)if(s)qa(i,!1);else{if(Je!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(u=Pl(e),u!==null){for(t.flags|=128,qa(i,!1),e=u.updateQueue,t.updateQueue=e,sr(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)Iu(n,e),n=n.sibling;return W(st,st.current&1|2),t.child}e=e.sibling}i.tail!==null&&Oe()>rr&&(t.flags|=128,s=!0,qa(i,!1),t.lanes=4194304)}else{if(!s)if(e=Pl(u),e!==null){if(t.flags|=128,s=!0,e=e.updateQueue,t.updateQueue=e,sr(t,e),qa(i,!0),i.tail===null&&i.tailMode==="hidden"&&!u.alternate&&!Re)return Ze(t),null}else 2*Oe()-i.renderingStartTime>rr&&n!==536870912&&(t.flags|=128,s=!0,qa(i,!1),t.lanes=4194304);i.isBackwards?(u.sibling=t.child,t.child=u):(e=i.last,e!==null?e.sibling=u:t.child=u,i.last=u)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Oe(),t.sibling=null,e=st.current,W(st,s?e&1|2:e&1),t):(Ze(t),null);case 22:case 23:return bn(t),Wi(),s=t.memoizedState!==null,e!==null?e.memoizedState!==null!==s&&(t.flags|=8192):s&&(t.flags|=8192),s?(n&536870912)!==0&&(t.flags&128)===0&&(Ze(t),t.subtreeFlags&6&&(t.flags|=8192)):Ze(t),n=t.updateQueue,n!==null&&sr(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),s=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(s=t.memoizedState.cachePool.pool),s!==n&&(t.flags|=2048),e!==null&&ee(cs),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),pn(nt),Ze(t),null;case 25:return null;case 30:return null}throw Error(o(156,t.tag))}function Lx(e,t){switch(ki(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return pn(nt),ne(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return oe(t),null;case 13:if(bn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(o(340));Na()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ee(st),null;case 4:return ne(),null;case 10:return pn(t.type),null;case 22:case 23:return bn(t),Wi(),e!==null&&ee(cs),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return pn(nt),null;case 25:return null;default:return null}}function Tf(e,t){switch(ki(t),t.tag){case 3:pn(nt),ne();break;case 26:case 27:case 5:oe(t);break;case 4:ne();break;case 13:bn(t);break;case 19:ee(st);break;case 10:pn(t.type);break;case 22:case 23:bn(t),Wi(),e!==null&&ee(cs);break;case 24:pn(nt)}}function La(e,t){try{var n=t.updateQueue,s=n!==null?n.lastEffect:null;if(s!==null){var i=s.next;n=i;do{if((n.tag&e)===e){s=void 0;var u=n.create,f=n.inst;s=u(),f.destroy=s}n=n.next}while(n!==i)}}catch(p){Le(t,t.return,p)}}function Mn(e,t,n){try{var s=t.updateQueue,i=s!==null?s.lastEffect:null;if(i!==null){var u=i.next;s=u;do{if((s.tag&e)===e){var f=s.inst,p=f.destroy;if(p!==void 0){f.destroy=void 0,i=t;var v=n,C=p;try{C()}catch(q){Le(i,v,q)}}}s=s.next}while(s!==u)}}catch(q){Le(t,t.return,q)}}function _f(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{md(t,n)}catch(s){Le(e,e.return,s)}}}function Rf(e,t,n){n.props=us(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(s){Le(e,t,s)}}function Ha(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var s=e.stateNode;break;case 30:s=e.stateNode;break;default:s=e.stateNode}typeof n=="function"?e.refCleanup=n(s):n.current=s}}catch(i){Le(e,t,i)}}function It(e,t){var n=e.ref,s=e.refCleanup;if(n!==null)if(typeof s=="function")try{s()}catch(i){Le(e,t,i)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(i){Le(e,t,i)}else n.current=null}function Cf(e){var t=e.type,n=e.memoizedProps,s=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&s.focus();break e;case"img":n.src?s.src=n.src:n.srcSet&&(s.srcset=n.srcSet)}}catch(i){Le(e,e.return,i)}}function Ac(e,t,n){try{var s=e.stateNode;rb(s,e.type,n,t),s[bt]=t}catch(i){Le(e,e.return,i)}}function Of(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Vn(e.type)||e.tag===4}function Ec(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Of(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Vn(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Tc(e,t,n){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=pr));else if(s!==4&&(s===27&&Vn(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(Tc(e,t,n),e=e.sibling;e!==null;)Tc(e,t,n),e=e.sibling}function ar(e,t,n){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(s!==4&&(s===27&&Vn(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(ar(e,t,n),e=e.sibling;e!==null;)ar(e,t,n),e=e.sibling}function Df(e){var t=e.stateNode,n=e.memoizedProps;try{for(var s=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);ut(t,s,n),t[dt]=e,t[bt]=n}catch(u){Le(e,e.return,u)}}var vn=!1,$e=!1,_c=!1,Uf=typeof WeakSet=="function"?WeakSet:Set,rt=null;function Hx(e,t){if(e=e.containerInfo,Pc=Nr,e=Gu(e),Ei(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var s=n.getSelection&&n.getSelection();if(s&&s.rangeCount!==0){n=s.anchorNode;var i=s.anchorOffset,u=s.focusNode;s=s.focusOffset;try{n.nodeType,u.nodeType}catch{n=null;break e}var f=0,p=-1,v=-1,C=0,q=0,V=e,D=null;t:for(;;){for(var M;V!==n||i!==0&&V.nodeType!==3||(p=f+i),V!==u||s!==0&&V.nodeType!==3||(v=f+s),V.nodeType===3&&(f+=V.nodeValue.length),(M=V.firstChild)!==null;)D=V,V=M;for(;;){if(V===e)break t;if(D===n&&++C===i&&(p=f),D===u&&++q===s&&(v=f),(M=V.nextSibling)!==null)break;V=D,D=V.parentNode}V=M}n=p===-1||v===-1?null:{start:p,end:v}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ic={focusedElem:e,selectionRange:n},Nr=!1,rt=t;rt!==null;)if(t=rt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,rt=e;else for(;rt!==null;){switch(t=rt,u=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&u!==null){e=void 0,n=t,i=u.memoizedProps,u=u.memoizedState,s=n.stateNode;try{var ue=us(n.type,i,n.elementType===n.type);e=s.getSnapshotBeforeUpdate(ue,u),s.__reactInternalSnapshotBeforeUpdate=e}catch(ie){Le(n,n.return,ie)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)no(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":no(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(o(163))}if(e=t.sibling,e!==null){e.return=t.return,rt=e;break}rt=t.return}}function Mf(e,t,n){var s=n.flags;switch(n.tag){case 0:case 11:case 15:zn(e,n),s&4&&La(5,n);break;case 1:if(zn(e,n),s&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(f){Le(n,n.return,f)}else{var i=us(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(f){Le(n,n.return,f)}}s&64&&_f(n),s&512&&Ha(n,n.return);break;case 3:if(zn(e,n),s&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{md(e,t)}catch(f){Le(n,n.return,f)}}break;case 27:t===null&&s&4&&Df(n);case 26:case 5:zn(e,n),t===null&&s&4&&Cf(n),s&512&&Ha(n,n.return);break;case 12:zn(e,n);break;case 13:zn(e,n),s&4&&kf(e,n),s&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=Fx.bind(null,n),hb(e,n))));break;case 22:if(s=n.memoizedState!==null||vn,!s){t=t!==null&&t.memoizedState!==null||$e,i=vn;var u=$e;vn=s,($e=t)&&!u?Bn(e,n,(n.subtreeFlags&8772)!==0):zn(e,n),vn=i,$e=u}break;case 30:break;default:zn(e,n)}}function zf(e){var t=e.alternate;t!==null&&(e.alternate=null,zf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&ii(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Xe=null,vt=!1;function jn(e,t,n){for(n=n.child;n!==null;)Bf(e,t,n),n=n.sibling}function Bf(e,t,n){if(At&&typeof At.onCommitFiberUnmount=="function")try{At.onCommitFiberUnmount(ia,n)}catch{}switch(n.tag){case 26:$e||It(n,t),jn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:$e||It(n,t);var s=Xe,i=vt;Vn(n.type)&&(Xe=n.stateNode,vt=!1),jn(e,t,n),Fa(n.stateNode),Xe=s,vt=i;break;case 5:$e||It(n,t);case 6:if(s=Xe,i=vt,Xe=null,jn(e,t,n),Xe=s,vt=i,Xe!==null)if(vt)try{(Xe.nodeType===9?Xe.body:Xe.nodeName==="HTML"?Xe.ownerDocument.body:Xe).removeChild(n.stateNode)}catch(u){Le(n,t,u)}else try{Xe.removeChild(n.stateNode)}catch(u){Le(n,t,u)}break;case 18:Xe!==null&&(vt?(e=Xe,wh(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),sl(e)):wh(Xe,n.stateNode));break;case 4:s=Xe,i=vt,Xe=n.stateNode.containerInfo,vt=!0,jn(e,t,n),Xe=s,vt=i;break;case 0:case 11:case 14:case 15:$e||Mn(2,n,t),$e||Mn(4,n,t),jn(e,t,n);break;case 1:$e||(It(n,t),s=n.stateNode,typeof s.componentWillUnmount=="function"&&Rf(n,t,s)),jn(e,t,n);break;case 21:jn(e,t,n);break;case 22:$e=(s=$e)||n.memoizedState!==null,jn(e,t,n),$e=s;break;default:jn(e,t,n)}}function kf(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{sl(e)}catch(n){Le(t,t.return,n)}}function Yx(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Uf),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Uf),t;default:throw Error(o(435,e.tag))}}function Rc(e,t){var n=Yx(e);t.forEach(function(s){var i=$x.bind(null,e,s);n.has(s)||(n.add(s),s.then(i,i))})}function Rt(e,t){var n=t.deletions;if(n!==null)for(var s=0;s<n.length;s++){var i=n[s],u=e,f=t,p=f;e:for(;p!==null;){switch(p.tag){case 27:if(Vn(p.type)){Xe=p.stateNode,vt=!1;break e}break;case 5:Xe=p.stateNode,vt=!1;break e;case 3:case 4:Xe=p.stateNode.containerInfo,vt=!0;break e}p=p.return}if(Xe===null)throw Error(o(160));Bf(u,f,i),Xe=null,vt=!1,u=i.alternate,u!==null&&(u.return=null),i.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)qf(t,e),t=t.sibling}var Kt=null;function qf(e,t){var n=e.alternate,s=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Rt(t,e),Ct(e),s&4&&(Mn(3,e,e.return),La(3,e),Mn(5,e,e.return));break;case 1:Rt(t,e),Ct(e),s&512&&($e||n===null||It(n,n.return)),s&64&&vn&&(e=e.updateQueue,e!==null&&(s=e.callbacks,s!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?s:n.concat(s))));break;case 26:var i=Kt;if(Rt(t,e),Ct(e),s&512&&($e||n===null||It(n,n.return)),s&4){var u=n!==null?n.memoizedState:null;if(s=e.memoizedState,n===null)if(s===null)if(e.stateNode===null){e:{s=e.type,n=e.memoizedProps,i=i.ownerDocument||i;t:switch(s){case"title":u=i.getElementsByTagName("title")[0],(!u||u[ua]||u[dt]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=i.createElement(s),i.head.insertBefore(u,i.querySelector("head > title"))),ut(u,s,n),u[dt]=e,at(u),s=u;break e;case"link":var f=Dh("link","href",i).get(s+(n.href||""));if(f){for(var p=0;p<f.length;p++)if(u=f[p],u.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&u.getAttribute("rel")===(n.rel==null?null:n.rel)&&u.getAttribute("title")===(n.title==null?null:n.title)&&u.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){f.splice(p,1);break t}}u=i.createElement(s),ut(u,s,n),i.head.appendChild(u);break;case"meta":if(f=Dh("meta","content",i).get(s+(n.content||""))){for(p=0;p<f.length;p++)if(u=f[p],u.getAttribute("content")===(n.content==null?null:""+n.content)&&u.getAttribute("name")===(n.name==null?null:n.name)&&u.getAttribute("property")===(n.property==null?null:n.property)&&u.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&u.getAttribute("charset")===(n.charSet==null?null:n.charSet)){f.splice(p,1);break t}}u=i.createElement(s),ut(u,s,n),i.head.appendChild(u);break;default:throw Error(o(468,s))}u[dt]=e,at(u),s=u}e.stateNode=s}else Uh(i,e.type,e.stateNode);else e.stateNode=Oh(i,s,e.memoizedProps);else u!==s?(u===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):u.count--,s===null?Uh(i,e.type,e.stateNode):Oh(i,s,e.memoizedProps)):s===null&&e.stateNode!==null&&Ac(e,e.memoizedProps,n.memoizedProps)}break;case 27:Rt(t,e),Ct(e),s&512&&($e||n===null||It(n,n.return)),n!==null&&s&4&&Ac(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Rt(t,e),Ct(e),s&512&&($e||n===null||It(n,n.return)),e.flags&32){i=e.stateNode;try{As(i,"")}catch(M){Le(e,e.return,M)}}s&4&&e.stateNode!=null&&(i=e.memoizedProps,Ac(e,i,n!==null?n.memoizedProps:i)),s&1024&&(_c=!0);break;case 6:if(Rt(t,e),Ct(e),s&4){if(e.stateNode===null)throw Error(o(162));s=e.memoizedProps,n=e.stateNode;try{n.nodeValue=s}catch(M){Le(e,e.return,M)}}break;case 3:if(yr=null,i=Kt,Kt=br(t.containerInfo),Rt(t,e),Kt=i,Ct(e),s&4&&n!==null&&n.memoizedState.isDehydrated)try{sl(t.containerInfo)}catch(M){Le(e,e.return,M)}_c&&(_c=!1,Lf(e));break;case 4:s=Kt,Kt=br(e.stateNode.containerInfo),Rt(t,e),Ct(e),Kt=s;break;case 12:Rt(t,e),Ct(e);break;case 13:Rt(t,e),Ct(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(zc=Oe()),s&4&&(s=e.updateQueue,s!==null&&(e.updateQueue=null,Rc(e,s)));break;case 22:i=e.memoizedState!==null;var v=n!==null&&n.memoizedState!==null,C=vn,q=$e;if(vn=C||i,$e=q||v,Rt(t,e),$e=q,vn=C,Ct(e),s&8192)e:for(t=e.stateNode,t._visibility=i?t._visibility&-2:t._visibility|1,i&&(n===null||v||vn||$e||ds(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){v=n=t;try{if(u=v.stateNode,i)f=u.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{p=v.stateNode;var V=v.memoizedProps.style,D=V!=null&&V.hasOwnProperty("display")?V.display:null;p.style.display=D==null||typeof D=="boolean"?"":(""+D).trim()}}catch(M){Le(v,v.return,M)}}}else if(t.tag===6){if(n===null){v=t;try{v.stateNode.nodeValue=i?"":v.memoizedProps}catch(M){Le(v,v.return,M)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}s&4&&(s=e.updateQueue,s!==null&&(n=s.retryQueue,n!==null&&(s.retryQueue=null,Rc(e,n))));break;case 19:Rt(t,e),Ct(e),s&4&&(s=e.updateQueue,s!==null&&(e.updateQueue=null,Rc(e,s)));break;case 30:break;case 21:break;default:Rt(t,e),Ct(e)}}function Ct(e){var t=e.flags;if(t&2){try{for(var n,s=e.return;s!==null;){if(Of(s)){n=s;break}s=s.return}if(n==null)throw Error(o(160));switch(n.tag){case 27:var i=n.stateNode,u=Ec(e);ar(e,u,i);break;case 5:var f=n.stateNode;n.flags&32&&(As(f,""),n.flags&=-33);var p=Ec(e);ar(e,p,f);break;case 3:case 4:var v=n.stateNode.containerInfo,C=Ec(e);Tc(e,C,v);break;default:throw Error(o(161))}}catch(q){Le(e,e.return,q)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Lf(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Lf(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function zn(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Mf(e,t.alternate,t),t=t.sibling}function ds(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Mn(4,t,t.return),ds(t);break;case 1:It(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&Rf(t,t.return,n),ds(t);break;case 27:Fa(t.stateNode);case 26:case 5:It(t,t.return),ds(t);break;case 22:t.memoizedState===null&&ds(t);break;case 30:ds(t);break;default:ds(t)}e=e.sibling}}function Bn(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var s=t.alternate,i=e,u=t,f=u.flags;switch(u.tag){case 0:case 11:case 15:Bn(i,u,n),La(4,u);break;case 1:if(Bn(i,u,n),s=u,i=s.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(C){Le(s,s.return,C)}if(s=u,i=s.updateQueue,i!==null){var p=s.stateNode;try{var v=i.shared.hiddenCallbacks;if(v!==null)for(i.shared.hiddenCallbacks=null,i=0;i<v.length;i++)hd(v[i],p)}catch(C){Le(s,s.return,C)}}n&&f&64&&_f(u),Ha(u,u.return);break;case 27:Df(u);case 26:case 5:Bn(i,u,n),n&&s===null&&f&4&&Cf(u),Ha(u,u.return);break;case 12:Bn(i,u,n);break;case 13:Bn(i,u,n),n&&f&4&&kf(i,u);break;case 22:u.memoizedState===null&&Bn(i,u,n),Ha(u,u.return);break;case 30:break;default:Bn(i,u,n)}t=t.sibling}}function Cc(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&Aa(n))}function Oc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Aa(e))}function en(e,t,n,s){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Hf(e,t,n,s),t=t.sibling}function Hf(e,t,n,s){var i=t.flags;switch(t.tag){case 0:case 11:case 15:en(e,t,n,s),i&2048&&La(9,t);break;case 1:en(e,t,n,s);break;case 3:en(e,t,n,s),i&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Aa(e)));break;case 12:if(i&2048){en(e,t,n,s),e=t.stateNode;try{var u=t.memoizedProps,f=u.id,p=u.onPostCommit;typeof p=="function"&&p(f,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(v){Le(t,t.return,v)}}else en(e,t,n,s);break;case 13:en(e,t,n,s);break;case 23:break;case 22:u=t.stateNode,f=t.alternate,t.memoizedState!==null?u._visibility&2?en(e,t,n,s):Ya(e,t):u._visibility&2?en(e,t,n,s):(u._visibility|=2,Xs(e,t,n,s,(t.subtreeFlags&10256)!==0)),i&2048&&Cc(f,t);break;case 24:en(e,t,n,s),i&2048&&Oc(t.alternate,t);break;default:en(e,t,n,s)}}function Xs(e,t,n,s,i){for(i=i&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var u=e,f=t,p=n,v=s,C=f.flags;switch(f.tag){case 0:case 11:case 15:Xs(u,f,p,v,i),La(8,f);break;case 23:break;case 22:var q=f.stateNode;f.memoizedState!==null?q._visibility&2?Xs(u,f,p,v,i):Ya(u,f):(q._visibility|=2,Xs(u,f,p,v,i)),i&&C&2048&&Cc(f.alternate,f);break;case 24:Xs(u,f,p,v,i),i&&C&2048&&Oc(f.alternate,f);break;default:Xs(u,f,p,v,i)}t=t.sibling}}function Ya(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,s=t,i=s.flags;switch(s.tag){case 22:Ya(n,s),i&2048&&Cc(s.alternate,s);break;case 24:Ya(n,s),i&2048&&Oc(s.alternate,s);break;default:Ya(n,s)}t=t.sibling}}var Va=8192;function Qs(e){if(e.subtreeFlags&Va)for(e=e.child;e!==null;)Yf(e),e=e.sibling}function Yf(e){switch(e.tag){case 26:Qs(e),e.flags&Va&&e.memoizedState!==null&&Eb(Kt,e.memoizedState,e.memoizedProps);break;case 5:Qs(e);break;case 3:case 4:var t=Kt;Kt=br(e.stateNode.containerInfo),Qs(e),Kt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Va,Va=16777216,Qs(e),Va=t):Qs(e));break;default:Qs(e)}}function Vf(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Ga(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var s=t[n];rt=s,Xf(s,e)}Vf(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Gf(e),e=e.sibling}function Gf(e){switch(e.tag){case 0:case 11:case 15:Ga(e),e.flags&2048&&Mn(9,e,e.return);break;case 3:Ga(e);break;case 12:Ga(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,lr(e)):Ga(e);break;default:Ga(e)}}function lr(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var s=t[n];rt=s,Xf(s,e)}Vf(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Mn(8,t,t.return),lr(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,lr(t));break;default:lr(t)}e=e.sibling}}function Xf(e,t){for(;rt!==null;){var n=rt;switch(n.tag){case 0:case 11:case 15:Mn(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var s=n.memoizedState.cachePool.pool;s!=null&&s.refCount++}break;case 24:Aa(n.memoizedState.cache)}if(s=n.child,s!==null)s.return=n,rt=s;else e:for(n=e;rt!==null;){s=rt;var i=s.sibling,u=s.return;if(zf(s),s===n){rt=null;break e}if(i!==null){i.return=u,rt=i;break e}rt=u}}}var Vx={getCacheForType:function(e){var t=ft(nt),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},Gx=typeof WeakMap=="function"?WeakMap:Map,Ue=0,He=null,je=null,Ae=0,Me=0,Ot=null,kn=!1,Zs=!1,Dc=!1,Nn=0,Je=0,qn=0,fs=0,Uc=0,Gt=0,Ks=0,Xa=null,jt=null,Mc=!1,zc=0,rr=1/0,ir=null,Ln=null,ot=0,Hn=null,Js=null,Fs=0,Bc=0,kc=null,Qf=null,Qa=0,qc=null;function Dt(){if((Ue&2)!==0&&Ae!==0)return Ae&-Ae;if(N.T!==null){var e=Bs;return e!==0?e:Qc()}return ru()}function Zf(){Gt===0&&(Gt=(Ae&536870912)===0||Re?nu():536870912);var e=Vt.current;return e!==null&&(e.flags|=32),Gt}function Ut(e,t,n){(e===He&&(Me===2||Me===9)||e.cancelPendingCommit!==null)&&($s(e,0),Yn(e,Ae,Gt,!1)),oa(e,n),((Ue&2)===0||e!==He)&&(e===He&&((Ue&2)===0&&(fs|=n),Je===4&&Yn(e,Ae,Gt,!1)),tn(e))}function Kf(e,t,n){if((Ue&6)!==0)throw Error(o(327));var s=!n&&(t&124)===0&&(t&e.expiredLanes)===0||ca(e,t),i=s?Zx(e,t):Yc(e,t,!0),u=s;do{if(i===0){Zs&&!s&&Yn(e,t,0,!1);break}else{if(n=e.current.alternate,u&&!Xx(n)){i=Yc(e,t,!1),u=!1;continue}if(i===2){if(u=t,e.errorRecoveryDisabledLanes&u)var f=0;else f=e.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){t=f;e:{var p=e;i=Xa;var v=p.current.memoizedState.isDehydrated;if(v&&($s(p,f).flags|=256),f=Yc(p,f,!1),f!==2){if(Dc&&!v){p.errorRecoveryDisabledLanes|=u,fs|=u,i=4;break e}u=jt,jt=i,u!==null&&(jt===null?jt=u:jt.push.apply(jt,u))}i=f}if(u=!1,i!==2)continue}}if(i===1){$s(e,0),Yn(e,t,0,!0);break}e:{switch(s=e,u=i,u){case 0:case 1:throw Error(o(345));case 4:if((t&4194048)!==t)break;case 6:Yn(s,t,Gt,!kn);break e;case 2:jt=null;break;case 3:case 5:break;default:throw Error(o(329))}if((t&62914560)===t&&(i=zc+300-Oe(),10<i)){if(Yn(s,t,Gt,!kn),gl(s,0,!0)!==0)break e;s.timeoutHandle=Nh(Jf.bind(null,s,n,jt,ir,Mc,t,Gt,fs,Ks,kn,u,2,-0,0),i);break e}Jf(s,n,jt,ir,Mc,t,Gt,fs,Ks,kn,u,0,-0,0)}}break}while(!0);tn(e)}function Jf(e,t,n,s,i,u,f,p,v,C,q,V,D,M){if(e.timeoutHandle=-1,V=t.subtreeFlags,(V&8192||(V&16785408)===16785408)&&(Pa={stylesheets:null,count:0,unsuspend:Ab},Yf(t),V=Tb(),V!==null)){e.cancelPendingCommit=V(th.bind(null,e,t,u,n,s,i,f,p,v,q,1,D,M)),Yn(e,u,f,!C);return}th(e,t,u,n,s,i,f,p,v)}function Xx(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var s=0;s<n.length;s++){var i=n[s],u=i.getSnapshot;i=i.value;try{if(!Tt(u(),i))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Yn(e,t,n,s){t&=~Uc,t&=~fs,e.suspendedLanes|=t,e.pingedLanes&=~t,s&&(e.warmLanes|=t),s=e.expirationTimes;for(var i=t;0<i;){var u=31-Et(i),f=1<<u;s[u]=-1,i&=~f}n!==0&&au(e,n,t)}function cr(){return(Ue&6)===0?(Za(0),!1):!0}function Lc(){if(je!==null){if(Me===0)var e=je.return;else e=je,mn=rs=null,nc(e),Vs=null,Ba=0,e=je;for(;e!==null;)Tf(e.alternate,e),e=e.return;je=null}}function $s(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,cb(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),Lc(),He=e,je=n=dn(e.current,null),Ae=t,Me=0,Ot=null,kn=!1,Zs=ca(e,t),Dc=!1,Ks=Gt=Uc=fs=qn=Je=0,jt=Xa=null,Mc=!1,(t&8)!==0&&(t|=t&32);var s=e.entangledLanes;if(s!==0)for(e=e.entanglements,s&=t;0<s;){var i=31-Et(s),u=1<<i;t|=e[i],s&=~u}return Nn=t,Cl(),n}function Ff(e,t){ge=null,N.H=Fl,t===Ta||t===Ll?(t=dd(),Me=3):t===cd?(t=dd(),Me=4):Me=t===hf?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Ot=t,je===null&&(Je=1,er(e,qt(t,e.current)))}function $f(){var e=N.H;return N.H=Fl,e===null?Fl:e}function Wf(){var e=N.A;return N.A=Vx,e}function Hc(){Je=4,kn||(Ae&4194048)!==Ae&&Vt.current!==null||(Zs=!0),(qn&134217727)===0&&(fs&134217727)===0||He===null||Yn(He,Ae,Gt,!1)}function Yc(e,t,n){var s=Ue;Ue|=2;var i=$f(),u=Wf();(He!==e||Ae!==t)&&(ir=null,$s(e,t)),t=!1;var f=Je;e:do try{if(Me!==0&&je!==null){var p=je,v=Ot;switch(Me){case 8:Lc(),f=6;break e;case 3:case 2:case 9:case 6:Vt.current===null&&(t=!0);var C=Me;if(Me=0,Ot=null,Ws(e,p,v,C),n&&Zs){f=0;break e}break;default:C=Me,Me=0,Ot=null,Ws(e,p,v,C)}}Qx(),f=Je;break}catch(q){Ff(e,q)}while(!0);return t&&e.shellSuspendCounter++,mn=rs=null,Ue=s,N.H=i,N.A=u,je===null&&(He=null,Ae=0,Cl()),f}function Qx(){for(;je!==null;)Pf(je)}function Zx(e,t){var n=Ue;Ue|=2;var s=$f(),i=Wf();He!==e||Ae!==t?(ir=null,rr=Oe()+500,$s(e,t)):Zs=ca(e,t);e:do try{if(Me!==0&&je!==null){t=je;var u=Ot;t:switch(Me){case 1:Me=0,Ot=null,Ws(e,t,u,1);break;case 2:case 9:if(od(u)){Me=0,Ot=null,If(t);break}t=function(){Me!==2&&Me!==9||He!==e||(Me=7),tn(e)},u.then(t,t);break e;case 3:Me=7;break e;case 4:Me=5;break e;case 7:od(u)?(Me=0,Ot=null,If(t)):(Me=0,Ot=null,Ws(e,t,u,7));break;case 5:var f=null;switch(je.tag){case 26:f=je.memoizedState;case 5:case 27:var p=je;if(!f||Mh(f)){Me=0,Ot=null;var v=p.sibling;if(v!==null)je=v;else{var C=p.return;C!==null?(je=C,or(C)):je=null}break t}}Me=0,Ot=null,Ws(e,t,u,5);break;case 6:Me=0,Ot=null,Ws(e,t,u,6);break;case 8:Lc(),Je=6;break e;default:throw Error(o(462))}}Kx();break}catch(q){Ff(e,q)}while(!0);return mn=rs=null,N.H=s,N.A=i,Ue=n,je!==null?0:(He=null,Ae=0,Cl(),Je)}function Kx(){for(;je!==null&&!Ye();)Pf(je)}function Pf(e){var t=Af(e.alternate,e,Nn);e.memoizedProps=e.pendingProps,t===null?or(e):je=t}function If(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=yf(n,t,t.pendingProps,t.type,void 0,Ae);break;case 11:t=yf(n,t,t.pendingProps,t.type.render,t.ref,Ae);break;case 5:nc(t);default:Tf(n,t),t=je=Iu(t,Nn),t=Af(n,t,Nn)}e.memoizedProps=e.pendingProps,t===null?or(e):je=t}function Ws(e,t,n,s){mn=rs=null,nc(t),Vs=null,Ba=0;var i=t.return;try{if(Bx(e,i,t,n,Ae)){Je=1,er(e,qt(n,e.current)),je=null;return}}catch(u){if(i!==null)throw je=i,u;Je=1,er(e,qt(n,e.current)),je=null;return}t.flags&32768?(Re||s===1?e=!0:Zs||(Ae&536870912)!==0?e=!1:(kn=e=!0,(s===2||s===9||s===3||s===6)&&(s=Vt.current,s!==null&&s.tag===13&&(s.flags|=16384))),eh(t,e)):or(t)}function or(e){var t=e;do{if((t.flags&32768)!==0){eh(t,kn);return}e=t.return;var n=qx(t.alternate,t,Nn);if(n!==null){je=n;return}if(t=t.sibling,t!==null){je=t;return}je=t=e}while(t!==null);Je===0&&(Je=5)}function eh(e,t){do{var n=Lx(e.alternate,e);if(n!==null){n.flags&=32767,je=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){je=e;return}je=e=n}while(e!==null);Je=6,je=null}function th(e,t,n,s,i,u,f,p,v){e.cancelPendingCommit=null;do ur();while(ot!==0);if((Ue&6)!==0)throw Error(o(327));if(t!==null){if(t===e.current)throw Error(o(177));if(u=t.lanes|t.childLanes,u|=Oi,Ap(e,n,u,f,p,v),e===He&&(je=He=null,Ae=0),Js=t,Hn=e,Fs=n,Bc=u,kc=i,Qf=s,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Wx(pl,function(){return rh(),null})):(e.callbackNode=null,e.callbackPriority=0),s=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||s){s=N.T,N.T=null,i=G.p,G.p=2,f=Ue,Ue|=4;try{Hx(e,t,n)}finally{Ue=f,G.p=i,N.T=s}}ot=1,nh(),sh(),ah()}}function nh(){if(ot===1){ot=0;var e=Hn,t=Js,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=N.T,N.T=null;var s=G.p;G.p=2;var i=Ue;Ue|=4;try{qf(t,e);var u=Ic,f=Gu(e.containerInfo),p=u.focusedElem,v=u.selectionRange;if(f!==p&&p&&p.ownerDocument&&Vu(p.ownerDocument.documentElement,p)){if(v!==null&&Ei(p)){var C=v.start,q=v.end;if(q===void 0&&(q=C),"selectionStart"in p)p.selectionStart=C,p.selectionEnd=Math.min(q,p.value.length);else{var V=p.ownerDocument||document,D=V&&V.defaultView||window;if(D.getSelection){var M=D.getSelection(),ue=p.textContent.length,ie=Math.min(v.start,ue),ke=v.end===void 0?ie:Math.min(v.end,ue);!M.extend&&ie>ke&&(f=ke,ke=ie,ie=f);var E=Yu(p,ie),w=Yu(p,ke);if(E&&w&&(M.rangeCount!==1||M.anchorNode!==E.node||M.anchorOffset!==E.offset||M.focusNode!==w.node||M.focusOffset!==w.offset)){var _=V.createRange();_.setStart(E.node,E.offset),M.removeAllRanges(),ie>ke?(M.addRange(_),M.extend(w.node,w.offset)):(_.setEnd(w.node,w.offset),M.addRange(_))}}}}for(V=[],M=p;M=M.parentNode;)M.nodeType===1&&V.push({element:M,left:M.scrollLeft,top:M.scrollTop});for(typeof p.focus=="function"&&p.focus(),p=0;p<V.length;p++){var H=V[p];H.element.scrollLeft=H.left,H.element.scrollTop=H.top}}Nr=!!Pc,Ic=Pc=null}finally{Ue=i,G.p=s,N.T=n}}e.current=t,ot=2}}function sh(){if(ot===2){ot=0;var e=Hn,t=Js,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=N.T,N.T=null;var s=G.p;G.p=2;var i=Ue;Ue|=4;try{Mf(e,t.alternate,t)}finally{Ue=i,G.p=s,N.T=n}}ot=3}}function ah(){if(ot===4||ot===3){ot=0,me();var e=Hn,t=Js,n=Fs,s=Qf;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?ot=5:(ot=0,Js=Hn=null,lh(e,e.pendingLanes));var i=e.pendingLanes;if(i===0&&(Ln=null),li(n),t=t.stateNode,At&&typeof At.onCommitFiberRoot=="function")try{At.onCommitFiberRoot(ia,t,void 0,(t.current.flags&128)===128)}catch{}if(s!==null){t=N.T,i=G.p,G.p=2,N.T=null;try{for(var u=e.onRecoverableError,f=0;f<s.length;f++){var p=s[f];u(p.value,{componentStack:p.stack})}}finally{N.T=t,G.p=i}}(Fs&3)!==0&&ur(),tn(e),i=e.pendingLanes,(n&4194090)!==0&&(i&42)!==0?e===qc?Qa++:(Qa=0,qc=e):Qa=0,Za(0)}}function lh(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Aa(t)))}function ur(e){return nh(),sh(),ah(),rh()}function rh(){if(ot!==5)return!1;var e=Hn,t=Bc;Bc=0;var n=li(Fs),s=N.T,i=G.p;try{G.p=32>n?32:n,N.T=null,n=kc,kc=null;var u=Hn,f=Fs;if(ot=0,Js=Hn=null,Fs=0,(Ue&6)!==0)throw Error(o(331));var p=Ue;if(Ue|=4,Gf(u.current),Hf(u,u.current,f,n),Ue=p,Za(0,!1),At&&typeof At.onPostCommitFiberRoot=="function")try{At.onPostCommitFiberRoot(ia,u)}catch{}return!0}finally{G.p=i,N.T=s,lh(e,t)}}function ih(e,t,n){t=qt(n,t),t=xc(e.stateNode,t,2),e=Cn(e,t,2),e!==null&&(oa(e,2),tn(e))}function Le(e,t,n){if(e.tag===3)ih(e,e,n);else for(;t!==null;){if(t.tag===3){ih(t,e,n);break}else if(t.tag===1){var s=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof s.componentDidCatch=="function"&&(Ln===null||!Ln.has(s))){e=qt(n,e),n=df(2),s=Cn(t,n,2),s!==null&&(ff(n,s,t,e),oa(s,2),tn(s));break}}t=t.return}}function Vc(e,t,n){var s=e.pingCache;if(s===null){s=e.pingCache=new Gx;var i=new Set;s.set(t,i)}else i=s.get(t),i===void 0&&(i=new Set,s.set(t,i));i.has(n)||(Dc=!0,i.add(n),e=Jx.bind(null,e,t,n),t.then(e,e))}function Jx(e,t,n){var s=e.pingCache;s!==null&&s.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,He===e&&(Ae&n)===n&&(Je===4||Je===3&&(Ae&62914560)===Ae&&300>Oe()-zc?(Ue&2)===0&&$s(e,0):Uc|=n,Ks===Ae&&(Ks=0)),tn(e)}function ch(e,t){t===0&&(t=su()),e=Ds(e,t),e!==null&&(oa(e,t),tn(e))}function Fx(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),ch(e,n)}function $x(e,t){var n=0;switch(e.tag){case 13:var s=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:s=e.stateNode;break;case 22:s=e.stateNode._retryCache;break;default:throw Error(o(314))}s!==null&&s.delete(t),ch(e,n)}function Wx(e,t){return Ee(e,t)}var dr=null,Ps=null,Gc=!1,fr=!1,Xc=!1,hs=0;function tn(e){e!==Ps&&e.next===null&&(Ps===null?dr=Ps=e:Ps=Ps.next=e),fr=!0,Gc||(Gc=!0,Ix())}function Za(e,t){if(!Xc&&fr){Xc=!0;do for(var n=!1,s=dr;s!==null;){if(e!==0){var i=s.pendingLanes;if(i===0)var u=0;else{var f=s.suspendedLanes,p=s.pingedLanes;u=(1<<31-Et(42|e)+1)-1,u&=i&~(f&~p),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(n=!0,fh(s,u))}else u=Ae,u=gl(s,s===He?u:0,s.cancelPendingCommit!==null||s.timeoutHandle!==-1),(u&3)===0||ca(s,u)||(n=!0,fh(s,u));s=s.next}while(n);Xc=!1}}function Px(){oh()}function oh(){fr=Gc=!1;var e=0;hs!==0&&(ib()&&(e=hs),hs=0);for(var t=Oe(),n=null,s=dr;s!==null;){var i=s.next,u=uh(s,t);u===0?(s.next=null,n===null?dr=i:n.next=i,i===null&&(Ps=n)):(n=s,(e!==0||(u&3)!==0)&&(fr=!0)),s=i}Za(e)}function uh(e,t){for(var n=e.suspendedLanes,s=e.pingedLanes,i=e.expirationTimes,u=e.pendingLanes&-62914561;0<u;){var f=31-Et(u),p=1<<f,v=i[f];v===-1?((p&n)===0||(p&s)!==0)&&(i[f]=wp(p,t)):v<=t&&(e.expiredLanes|=p),u&=~p}if(t=He,n=Ae,n=gl(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),s=e.callbackNode,n===0||e===t&&(Me===2||Me===9)||e.cancelPendingCommit!==null)return s!==null&&s!==null&&be(s),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||ca(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(s!==null&&be(s),li(n)){case 2:case 8:n=eu;break;case 32:n=pl;break;case 268435456:n=tu;break;default:n=pl}return s=dh.bind(null,e),n=Ee(n,s),e.callbackPriority=t,e.callbackNode=n,t}return s!==null&&s!==null&&be(s),e.callbackPriority=2,e.callbackNode=null,2}function dh(e,t){if(ot!==0&&ot!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(ur()&&e.callbackNode!==n)return null;var s=Ae;return s=gl(e,e===He?s:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),s===0?null:(Kf(e,s,t),uh(e,Oe()),e.callbackNode!=null&&e.callbackNode===n?dh.bind(null,e):null)}function fh(e,t){if(ur())return null;Kf(e,t,!0)}function Ix(){ob(function(){(Ue&6)!==0?Ee(Io,Px):oh()})}function Qc(){return hs===0&&(hs=nu()),hs}function hh(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Sl(""+e)}function mh(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function eb(e,t,n,s,i){if(t==="submit"&&n&&n.stateNode===i){var u=hh((i[bt]||null).action),f=s.submitter;f&&(t=(t=f[bt]||null)?hh(t.formAction):f.getAttribute("formAction"),t!==null&&(u=t,f=null));var p=new Tl("action","action",null,s,i);e.push({event:p,listeners:[{instance:null,listener:function(){if(s.defaultPrevented){if(hs!==0){var v=f?mh(i,f):new FormData(i);dc(n,{pending:!0,data:v,method:i.method,action:u},null,v)}}else typeof u=="function"&&(p.preventDefault(),v=f?mh(i,f):new FormData(i),dc(n,{pending:!0,data:v,method:i.method,action:u},u,v))},currentTarget:i}]})}}for(var Zc=0;Zc<Ci.length;Zc++){var Kc=Ci[Zc],tb=Kc.toLowerCase(),nb=Kc[0].toUpperCase()+Kc.slice(1);Zt(tb,"on"+nb)}Zt(Zu,"onAnimationEnd"),Zt(Ku,"onAnimationIteration"),Zt(Ju,"onAnimationStart"),Zt("dblclick","onDoubleClick"),Zt("focusin","onFocus"),Zt("focusout","onBlur"),Zt(yx,"onTransitionRun"),Zt(vx,"onTransitionStart"),Zt(jx,"onTransitionCancel"),Zt(Fu,"onTransitionEnd"),Ns("onMouseEnter",["mouseout","mouseover"]),Ns("onMouseLeave",["mouseout","mouseover"]),Ns("onPointerEnter",["pointerout","pointerover"]),Ns("onPointerLeave",["pointerout","pointerover"]),Wn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Wn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Wn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Wn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Wn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Wn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ka="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),sb=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ka));function ph(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var s=e[n],i=s.event;s=s.listeners;e:{var u=void 0;if(t)for(var f=s.length-1;0<=f;f--){var p=s[f],v=p.instance,C=p.currentTarget;if(p=p.listener,v!==u&&i.isPropagationStopped())break e;u=p,i.currentTarget=C;try{u(i)}catch(q){Il(q)}i.currentTarget=null,u=v}else for(f=0;f<s.length;f++){if(p=s[f],v=p.instance,C=p.currentTarget,p=p.listener,v!==u&&i.isPropagationStopped())break e;u=p,i.currentTarget=C;try{u(i)}catch(q){Il(q)}i.currentTarget=null,u=v}}}}function Ne(e,t){var n=t[ri];n===void 0&&(n=t[ri]=new Set);var s=e+"__bubble";n.has(s)||(xh(t,e,2,!1),n.add(s))}function Jc(e,t,n){var s=0;t&&(s|=4),xh(n,e,s,t)}var hr="_reactListening"+Math.random().toString(36).slice(2);function Fc(e){if(!e[hr]){e[hr]=!0,cu.forEach(function(n){n!=="selectionchange"&&(sb.has(n)||Jc(n,!1,e),Jc(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[hr]||(t[hr]=!0,Jc("selectionchange",!1,t))}}function xh(e,t,n,s){switch(Hh(t)){case 2:var i=Cb;break;case 8:i=Ob;break;default:i=oo}n=i.bind(null,t,n,e),i=void 0,!bi||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),s?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function $c(e,t,n,s,i){var u=s;if((t&1)===0&&(t&2)===0&&s!==null)e:for(;;){if(s===null)return;var f=s.tag;if(f===3||f===4){var p=s.stateNode.containerInfo;if(p===i)break;if(f===4)for(f=s.return;f!==null;){var v=f.tag;if((v===3||v===4)&&f.stateNode.containerInfo===i)return;f=f.return}for(;p!==null;){if(f=ys(p),f===null)return;if(v=f.tag,v===5||v===6||v===26||v===27){s=u=f;continue e}p=p.parentNode}}s=s.return}Nu(function(){var C=u,q=pi(n),V=[];e:{var D=$u.get(e);if(D!==void 0){var M=Tl,ue=e;switch(e){case"keypress":if(Al(n)===0)break e;case"keydown":case"keyup":M=Wp;break;case"focusin":ue="focus",M=ji;break;case"focusout":ue="blur",M=ji;break;case"beforeblur":case"afterblur":M=ji;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":M=Au;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":M=Lp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":M=ex;break;case Zu:case Ku:case Ju:M=Vp;break;case Fu:M=nx;break;case"scroll":case"scrollend":M=kp;break;case"wheel":M=ax;break;case"copy":case"cut":case"paste":M=Xp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":M=Tu;break;case"toggle":case"beforetoggle":M=rx}var ie=(t&4)!==0,ke=!ie&&(e==="scroll"||e==="scrollend"),E=ie?D!==null?D+"Capture":null:D;ie=[];for(var w=C,_;w!==null;){var H=w;if(_=H.stateNode,H=H.tag,H!==5&&H!==26&&H!==27||_===null||E===null||(H=fa(w,E),H!=null&&ie.push(Ja(w,H,_))),ke)break;w=w.return}0<ie.length&&(D=new M(D,ue,null,n,q),V.push({event:D,listeners:ie}))}}if((t&7)===0){e:{if(D=e==="mouseover"||e==="pointerover",M=e==="mouseout"||e==="pointerout",D&&n!==mi&&(ue=n.relatedTarget||n.fromElement)&&(ys(ue)||ue[gs]))break e;if((M||D)&&(D=q.window===q?q:(D=q.ownerDocument)?D.defaultView||D.parentWindow:window,M?(ue=n.relatedTarget||n.toElement,M=C,ue=ue?ys(ue):null,ue!==null&&(ke=h(ue),ie=ue.tag,ue!==ke||ie!==5&&ie!==27&&ie!==6)&&(ue=null)):(M=null,ue=C),M!==ue)){if(ie=Au,H="onMouseLeave",E="onMouseEnter",w="mouse",(e==="pointerout"||e==="pointerover")&&(ie=Tu,H="onPointerLeave",E="onPointerEnter",w="pointer"),ke=M==null?D:da(M),_=ue==null?D:da(ue),D=new ie(H,w+"leave",M,n,q),D.target=ke,D.relatedTarget=_,H=null,ys(q)===C&&(ie=new ie(E,w+"enter",ue,n,q),ie.target=_,ie.relatedTarget=ke,H=ie),ke=H,M&&ue)t:{for(ie=M,E=ue,w=0,_=ie;_;_=Is(_))w++;for(_=0,H=E;H;H=Is(H))_++;for(;0<w-_;)ie=Is(ie),w--;for(;0<_-w;)E=Is(E),_--;for(;w--;){if(ie===E||E!==null&&ie===E.alternate)break t;ie=Is(ie),E=Is(E)}ie=null}else ie=null;M!==null&&bh(V,D,M,ie,!1),ue!==null&&ke!==null&&bh(V,ke,ue,ie,!0)}}e:{if(D=C?da(C):window,M=D.nodeName&&D.nodeName.toLowerCase(),M==="select"||M==="input"&&D.type==="file")var se=zu;else if(Uu(D))if(Bu)se=xx;else{se=mx;var ve=hx}else M=D.nodeName,!M||M.toLowerCase()!=="input"||D.type!=="checkbox"&&D.type!=="radio"?C&&hi(C.elementType)&&(se=zu):se=px;if(se&&(se=se(e,C))){Mu(V,se,n,q);break e}ve&&ve(e,D,C),e==="focusout"&&C&&D.type==="number"&&C.memoizedProps.value!=null&&fi(D,"number",D.value)}switch(ve=C?da(C):window,e){case"focusin":(Uu(ve)||ve.contentEditable==="true")&&(Rs=ve,Ti=C,va=null);break;case"focusout":va=Ti=Rs=null;break;case"mousedown":_i=!0;break;case"contextmenu":case"mouseup":case"dragend":_i=!1,Xu(V,n,q);break;case"selectionchange":if(gx)break;case"keydown":case"keyup":Xu(V,n,q)}var re;if(Si)e:{switch(e){case"compositionstart":var ce="onCompositionStart";break e;case"compositionend":ce="onCompositionEnd";break e;case"compositionupdate":ce="onCompositionUpdate";break e}ce=void 0}else _s?Ou(e,n)&&(ce="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(ce="onCompositionStart");ce&&(_u&&n.locale!=="ko"&&(_s||ce!=="onCompositionStart"?ce==="onCompositionEnd"&&_s&&(re=Su()):(En=q,gi="value"in En?En.value:En.textContent,_s=!0)),ve=mr(C,ce),0<ve.length&&(ce=new Eu(ce,e,null,n,q),V.push({event:ce,listeners:ve}),re?ce.data=re:(re=Du(n),re!==null&&(ce.data=re)))),(re=cx?ox(e,n):ux(e,n))&&(ce=mr(C,"onBeforeInput"),0<ce.length&&(ve=new Eu("onBeforeInput","beforeinput",null,n,q),V.push({event:ve,listeners:ce}),ve.data=re)),eb(V,e,C,n,q)}ph(V,t)})}function Ja(e,t,n){return{instance:e,listener:t,currentTarget:n}}function mr(e,t){for(var n=t+"Capture",s=[];e!==null;){var i=e,u=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||u===null||(i=fa(e,n),i!=null&&s.unshift(Ja(e,i,u)),i=fa(e,t),i!=null&&s.push(Ja(e,i,u))),e.tag===3)return s;e=e.return}return[]}function Is(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function bh(e,t,n,s,i){for(var u=t._reactName,f=[];n!==null&&n!==s;){var p=n,v=p.alternate,C=p.stateNode;if(p=p.tag,v!==null&&v===s)break;p!==5&&p!==26&&p!==27||C===null||(v=C,i?(C=fa(n,u),C!=null&&f.unshift(Ja(n,C,v))):i||(C=fa(n,u),C!=null&&f.push(Ja(n,C,v)))),n=n.return}f.length!==0&&e.push({event:t,listeners:f})}var ab=/\r\n?/g,lb=/\u0000|\uFFFD/g;function gh(e){return(typeof e=="string"?e:""+e).replace(ab,`
`).replace(lb,"")}function yh(e,t){return t=gh(t),gh(e)===t}function pr(){}function Be(e,t,n,s,i,u){switch(n){case"children":typeof s=="string"?t==="body"||t==="textarea"&&s===""||As(e,s):(typeof s=="number"||typeof s=="bigint")&&t!=="body"&&As(e,""+s);break;case"className":vl(e,"class",s);break;case"tabIndex":vl(e,"tabindex",s);break;case"dir":case"role":case"viewBox":case"width":case"height":vl(e,n,s);break;case"style":vu(e,s,u);break;case"data":if(t!=="object"){vl(e,"data",s);break}case"src":case"href":if(s===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(s==null||typeof s=="function"||typeof s=="symbol"||typeof s=="boolean"){e.removeAttribute(n);break}s=Sl(""+s),e.setAttribute(n,s);break;case"action":case"formAction":if(typeof s=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(n==="formAction"?(t!=="input"&&Be(e,t,"name",i.name,i,null),Be(e,t,"formEncType",i.formEncType,i,null),Be(e,t,"formMethod",i.formMethod,i,null),Be(e,t,"formTarget",i.formTarget,i,null)):(Be(e,t,"encType",i.encType,i,null),Be(e,t,"method",i.method,i,null),Be(e,t,"target",i.target,i,null)));if(s==null||typeof s=="symbol"||typeof s=="boolean"){e.removeAttribute(n);break}s=Sl(""+s),e.setAttribute(n,s);break;case"onClick":s!=null&&(e.onclick=pr);break;case"onScroll":s!=null&&Ne("scroll",e);break;case"onScrollEnd":s!=null&&Ne("scrollend",e);break;case"dangerouslySetInnerHTML":if(s!=null){if(typeof s!="object"||!("__html"in s))throw Error(o(61));if(n=s.__html,n!=null){if(i.children!=null)throw Error(o(60));e.innerHTML=n}}break;case"multiple":e.multiple=s&&typeof s!="function"&&typeof s!="symbol";break;case"muted":e.muted=s&&typeof s!="function"&&typeof s!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(s==null||typeof s=="function"||typeof s=="boolean"||typeof s=="symbol"){e.removeAttribute("xlink:href");break}n=Sl(""+s),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":s!=null&&typeof s!="function"&&typeof s!="symbol"?e.setAttribute(n,""+s):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":s&&typeof s!="function"&&typeof s!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":s===!0?e.setAttribute(n,""):s!==!1&&s!=null&&typeof s!="function"&&typeof s!="symbol"?e.setAttribute(n,s):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":s!=null&&typeof s!="function"&&typeof s!="symbol"&&!isNaN(s)&&1<=s?e.setAttribute(n,s):e.removeAttribute(n);break;case"rowSpan":case"start":s==null||typeof s=="function"||typeof s=="symbol"||isNaN(s)?e.removeAttribute(n):e.setAttribute(n,s);break;case"popover":Ne("beforetoggle",e),Ne("toggle",e),yl(e,"popover",s);break;case"xlinkActuate":on(e,"http://www.w3.org/1999/xlink","xlink:actuate",s);break;case"xlinkArcrole":on(e,"http://www.w3.org/1999/xlink","xlink:arcrole",s);break;case"xlinkRole":on(e,"http://www.w3.org/1999/xlink","xlink:role",s);break;case"xlinkShow":on(e,"http://www.w3.org/1999/xlink","xlink:show",s);break;case"xlinkTitle":on(e,"http://www.w3.org/1999/xlink","xlink:title",s);break;case"xlinkType":on(e,"http://www.w3.org/1999/xlink","xlink:type",s);break;case"xmlBase":on(e,"http://www.w3.org/XML/1998/namespace","xml:base",s);break;case"xmlLang":on(e,"http://www.w3.org/XML/1998/namespace","xml:lang",s);break;case"xmlSpace":on(e,"http://www.w3.org/XML/1998/namespace","xml:space",s);break;case"is":yl(e,"is",s);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=zp.get(n)||n,yl(e,n,s))}}function Wc(e,t,n,s,i,u){switch(n){case"style":vu(e,s,u);break;case"dangerouslySetInnerHTML":if(s!=null){if(typeof s!="object"||!("__html"in s))throw Error(o(61));if(n=s.__html,n!=null){if(i.children!=null)throw Error(o(60));e.innerHTML=n}}break;case"children":typeof s=="string"?As(e,s):(typeof s=="number"||typeof s=="bigint")&&As(e,""+s);break;case"onScroll":s!=null&&Ne("scroll",e);break;case"onScrollEnd":s!=null&&Ne("scrollend",e);break;case"onClick":s!=null&&(e.onclick=pr);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!ou.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(i=n.endsWith("Capture"),t=n.slice(2,i?n.length-7:void 0),u=e[bt]||null,u=u!=null?u[n]:null,typeof u=="function"&&e.removeEventListener(t,u,i),typeof s=="function")){typeof u!="function"&&u!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,s,i);break e}n in e?e[n]=s:s===!0?e.setAttribute(n,""):yl(e,n,s)}}}function ut(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Ne("error",e),Ne("load",e);var s=!1,i=!1,u;for(u in n)if(n.hasOwnProperty(u)){var f=n[u];if(f!=null)switch(u){case"src":s=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Be(e,t,u,f,n,null)}}i&&Be(e,t,"srcSet",n.srcSet,n,null),s&&Be(e,t,"src",n.src,n,null);return;case"input":Ne("invalid",e);var p=u=f=i=null,v=null,C=null;for(s in n)if(n.hasOwnProperty(s)){var q=n[s];if(q!=null)switch(s){case"name":i=q;break;case"type":f=q;break;case"checked":v=q;break;case"defaultChecked":C=q;break;case"value":u=q;break;case"defaultValue":p=q;break;case"children":case"dangerouslySetInnerHTML":if(q!=null)throw Error(o(137,t));break;default:Be(e,t,s,q,n,null)}}xu(e,u,p,v,C,f,i,!1),jl(e);return;case"select":Ne("invalid",e),s=f=u=null;for(i in n)if(n.hasOwnProperty(i)&&(p=n[i],p!=null))switch(i){case"value":u=p;break;case"defaultValue":f=p;break;case"multiple":s=p;default:Be(e,t,i,p,n,null)}t=u,n=f,e.multiple=!!s,t!=null?ws(e,!!s,t,!1):n!=null&&ws(e,!!s,n,!0);return;case"textarea":Ne("invalid",e),u=i=s=null;for(f in n)if(n.hasOwnProperty(f)&&(p=n[f],p!=null))switch(f){case"value":s=p;break;case"defaultValue":i=p;break;case"children":u=p;break;case"dangerouslySetInnerHTML":if(p!=null)throw Error(o(91));break;default:Be(e,t,f,p,n,null)}gu(e,s,i,u),jl(e);return;case"option":for(v in n)if(n.hasOwnProperty(v)&&(s=n[v],s!=null))switch(v){case"selected":e.selected=s&&typeof s!="function"&&typeof s!="symbol";break;default:Be(e,t,v,s,n,null)}return;case"dialog":Ne("beforetoggle",e),Ne("toggle",e),Ne("cancel",e),Ne("close",e);break;case"iframe":case"object":Ne("load",e);break;case"video":case"audio":for(s=0;s<Ka.length;s++)Ne(Ka[s],e);break;case"image":Ne("error",e),Ne("load",e);break;case"details":Ne("toggle",e);break;case"embed":case"source":case"link":Ne("error",e),Ne("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(C in n)if(n.hasOwnProperty(C)&&(s=n[C],s!=null))switch(C){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Be(e,t,C,s,n,null)}return;default:if(hi(t)){for(q in n)n.hasOwnProperty(q)&&(s=n[q],s!==void 0&&Wc(e,t,q,s,n,void 0));return}}for(p in n)n.hasOwnProperty(p)&&(s=n[p],s!=null&&Be(e,t,p,s,n,null))}function rb(e,t,n,s){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,u=null,f=null,p=null,v=null,C=null,q=null;for(M in n){var V=n[M];if(n.hasOwnProperty(M)&&V!=null)switch(M){case"checked":break;case"value":break;case"defaultValue":v=V;default:s.hasOwnProperty(M)||Be(e,t,M,null,s,V)}}for(var D in s){var M=s[D];if(V=n[D],s.hasOwnProperty(D)&&(M!=null||V!=null))switch(D){case"type":u=M;break;case"name":i=M;break;case"checked":C=M;break;case"defaultChecked":q=M;break;case"value":f=M;break;case"defaultValue":p=M;break;case"children":case"dangerouslySetInnerHTML":if(M!=null)throw Error(o(137,t));break;default:M!==V&&Be(e,t,D,M,s,V)}}di(e,f,p,v,C,q,u,i);return;case"select":M=f=p=D=null;for(u in n)if(v=n[u],n.hasOwnProperty(u)&&v!=null)switch(u){case"value":break;case"multiple":M=v;default:s.hasOwnProperty(u)||Be(e,t,u,null,s,v)}for(i in s)if(u=s[i],v=n[i],s.hasOwnProperty(i)&&(u!=null||v!=null))switch(i){case"value":D=u;break;case"defaultValue":p=u;break;case"multiple":f=u;default:u!==v&&Be(e,t,i,u,s,v)}t=p,n=f,s=M,D!=null?ws(e,!!n,D,!1):!!s!=!!n&&(t!=null?ws(e,!!n,t,!0):ws(e,!!n,n?[]:"",!1));return;case"textarea":M=D=null;for(p in n)if(i=n[p],n.hasOwnProperty(p)&&i!=null&&!s.hasOwnProperty(p))switch(p){case"value":break;case"children":break;default:Be(e,t,p,null,s,i)}for(f in s)if(i=s[f],u=n[f],s.hasOwnProperty(f)&&(i!=null||u!=null))switch(f){case"value":D=i;break;case"defaultValue":M=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(o(91));break;default:i!==u&&Be(e,t,f,i,s,u)}bu(e,D,M);return;case"option":for(var ue in n)if(D=n[ue],n.hasOwnProperty(ue)&&D!=null&&!s.hasOwnProperty(ue))switch(ue){case"selected":e.selected=!1;break;default:Be(e,t,ue,null,s,D)}for(v in s)if(D=s[v],M=n[v],s.hasOwnProperty(v)&&D!==M&&(D!=null||M!=null))switch(v){case"selected":e.selected=D&&typeof D!="function"&&typeof D!="symbol";break;default:Be(e,t,v,D,s,M)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ie in n)D=n[ie],n.hasOwnProperty(ie)&&D!=null&&!s.hasOwnProperty(ie)&&Be(e,t,ie,null,s,D);for(C in s)if(D=s[C],M=n[C],s.hasOwnProperty(C)&&D!==M&&(D!=null||M!=null))switch(C){case"children":case"dangerouslySetInnerHTML":if(D!=null)throw Error(o(137,t));break;default:Be(e,t,C,D,s,M)}return;default:if(hi(t)){for(var ke in n)D=n[ke],n.hasOwnProperty(ke)&&D!==void 0&&!s.hasOwnProperty(ke)&&Wc(e,t,ke,void 0,s,D);for(q in s)D=s[q],M=n[q],!s.hasOwnProperty(q)||D===M||D===void 0&&M===void 0||Wc(e,t,q,D,s,M);return}}for(var E in n)D=n[E],n.hasOwnProperty(E)&&D!=null&&!s.hasOwnProperty(E)&&Be(e,t,E,null,s,D);for(V in s)D=s[V],M=n[V],!s.hasOwnProperty(V)||D===M||D==null&&M==null||Be(e,t,V,D,s,M)}var Pc=null,Ic=null;function xr(e){return e.nodeType===9?e:e.ownerDocument}function vh(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function jh(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function eo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var to=null;function ib(){var e=window.event;return e&&e.type==="popstate"?e===to?!1:(to=e,!0):(to=null,!1)}var Nh=typeof setTimeout=="function"?setTimeout:void 0,cb=typeof clearTimeout=="function"?clearTimeout:void 0,Sh=typeof Promise=="function"?Promise:void 0,ob=typeof queueMicrotask=="function"?queueMicrotask:typeof Sh<"u"?function(e){return Sh.resolve(null).then(e).catch(ub)}:Nh;function ub(e){setTimeout(function(){throw e})}function Vn(e){return e==="head"}function wh(e,t){var n=t,s=0,i=0;do{var u=n.nextSibling;if(e.removeChild(n),u&&u.nodeType===8)if(n=u.data,n==="/$"){if(0<s&&8>s){n=s;var f=e.ownerDocument;if(n&1&&Fa(f.documentElement),n&2&&Fa(f.body),n&4)for(n=f.head,Fa(n),f=n.firstChild;f;){var p=f.nextSibling,v=f.nodeName;f[ua]||v==="SCRIPT"||v==="STYLE"||v==="LINK"&&f.rel.toLowerCase()==="stylesheet"||n.removeChild(f),f=p}}if(i===0){e.removeChild(u),sl(t);return}i--}else n==="$"||n==="$?"||n==="$!"?i++:s=n.charCodeAt(0)-48;else s=0;n=u}while(n);sl(t)}function no(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":no(n),ii(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function db(e,t,n,s){for(;e.nodeType===1;){var i=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!s&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(s){if(!e[ua])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(u=e.getAttribute("rel"),u==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(u!==i.rel||e.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||e.getAttribute("title")!==(i.title==null?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(u=e.getAttribute("src"),(u!==(i.src==null?null:i.src)||e.getAttribute("type")!==(i.type==null?null:i.type)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&u&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var u=i.name==null?null:""+i.name;if(i.type==="hidden"&&e.getAttribute("name")===u)return e}else return e;if(e=Jt(e.nextSibling),e===null)break}return null}function fb(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Jt(e.nextSibling),e===null))return null;return e}function so(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function hb(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var s=function(){t(),n.removeEventListener("DOMContentLoaded",s)};n.addEventListener("DOMContentLoaded",s),e._reactRetry=s}}function Jt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var ao=null;function Ah(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function Eh(e,t,n){switch(t=xr(n),e){case"html":if(e=t.documentElement,!e)throw Error(o(452));return e;case"head":if(e=t.head,!e)throw Error(o(453));return e;case"body":if(e=t.body,!e)throw Error(o(454));return e;default:throw Error(o(451))}}function Fa(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);ii(e)}var Xt=new Map,Th=new Set;function br(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Sn=G.d;G.d={f:mb,r:pb,D:xb,C:bb,L:gb,m:yb,X:jb,S:vb,M:Nb};function mb(){var e=Sn.f(),t=cr();return e||t}function pb(e){var t=vs(e);t!==null&&t.tag===5&&t.type==="form"?Zd(t):Sn.r(e)}var ea=typeof document>"u"?null:document;function _h(e,t,n){var s=ea;if(s&&typeof t=="string"&&t){var i=kt(t);i='link[rel="'+e+'"][href="'+i+'"]',typeof n=="string"&&(i+='[crossorigin="'+n+'"]'),Th.has(i)||(Th.add(i),e={rel:e,crossOrigin:n,href:t},s.querySelector(i)===null&&(t=s.createElement("link"),ut(t,"link",e),at(t),s.head.appendChild(t)))}}function xb(e){Sn.D(e),_h("dns-prefetch",e,null)}function bb(e,t){Sn.C(e,t),_h("preconnect",e,t)}function gb(e,t,n){Sn.L(e,t,n);var s=ea;if(s&&e&&t){var i='link[rel="preload"][as="'+kt(t)+'"]';t==="image"&&n&&n.imageSrcSet?(i+='[imagesrcset="'+kt(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(i+='[imagesizes="'+kt(n.imageSizes)+'"]')):i+='[href="'+kt(e)+'"]';var u=i;switch(t){case"style":u=ta(e);break;case"script":u=na(e)}Xt.has(u)||(e=y({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Xt.set(u,e),s.querySelector(i)!==null||t==="style"&&s.querySelector($a(u))||t==="script"&&s.querySelector(Wa(u))||(t=s.createElement("link"),ut(t,"link",e),at(t),s.head.appendChild(t)))}}function yb(e,t){Sn.m(e,t);var n=ea;if(n&&e){var s=t&&typeof t.as=="string"?t.as:"script",i='link[rel="modulepreload"][as="'+kt(s)+'"][href="'+kt(e)+'"]',u=i;switch(s){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=na(e)}if(!Xt.has(u)&&(e=y({rel:"modulepreload",href:e},t),Xt.set(u,e),n.querySelector(i)===null)){switch(s){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Wa(u)))return}s=n.createElement("link"),ut(s,"link",e),at(s),n.head.appendChild(s)}}}function vb(e,t,n){Sn.S(e,t,n);var s=ea;if(s&&e){var i=js(s).hoistableStyles,u=ta(e);t=t||"default";var f=i.get(u);if(!f){var p={loading:0,preload:null};if(f=s.querySelector($a(u)))p.loading=5;else{e=y({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Xt.get(u))&&lo(e,n);var v=f=s.createElement("link");at(v),ut(v,"link",e),v._p=new Promise(function(C,q){v.onload=C,v.onerror=q}),v.addEventListener("load",function(){p.loading|=1}),v.addEventListener("error",function(){p.loading|=2}),p.loading|=4,gr(f,t,s)}f={type:"stylesheet",instance:f,count:1,state:p},i.set(u,f)}}}function jb(e,t){Sn.X(e,t);var n=ea;if(n&&e){var s=js(n).hoistableScripts,i=na(e),u=s.get(i);u||(u=n.querySelector(Wa(i)),u||(e=y({src:e,async:!0},t),(t=Xt.get(i))&&ro(e,t),u=n.createElement("script"),at(u),ut(u,"link",e),n.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},s.set(i,u))}}function Nb(e,t){Sn.M(e,t);var n=ea;if(n&&e){var s=js(n).hoistableScripts,i=na(e),u=s.get(i);u||(u=n.querySelector(Wa(i)),u||(e=y({src:e,async:!0,type:"module"},t),(t=Xt.get(i))&&ro(e,t),u=n.createElement("script"),at(u),ut(u,"link",e),n.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},s.set(i,u))}}function Rh(e,t,n,s){var i=(i=I.current)?br(i):null;if(!i)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=ta(n.href),n=js(i).hoistableStyles,s=n.get(t),s||(s={type:"style",instance:null,count:0,state:null},n.set(t,s)),s):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=ta(n.href);var u=js(i).hoistableStyles,f=u.get(e);if(f||(i=i.ownerDocument||i,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,f),(u=i.querySelector($a(e)))&&!u._p&&(f.instance=u,f.state.loading=5),Xt.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Xt.set(e,n),u||Sb(i,e,n,f.state))),t&&s===null)throw Error(o(528,""));return f}if(t&&s!==null)throw Error(o(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=na(n),n=js(i).hoistableScripts,s=n.get(t),s||(s={type:"script",instance:null,count:0,state:null},n.set(t,s)),s):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function ta(e){return'href="'+kt(e)+'"'}function $a(e){return'link[rel="stylesheet"]['+e+"]"}function Ch(e){return y({},e,{"data-precedence":e.precedence,precedence:null})}function Sb(e,t,n,s){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?s.loading=1:(t=e.createElement("link"),s.preload=t,t.addEventListener("load",function(){return s.loading|=1}),t.addEventListener("error",function(){return s.loading|=2}),ut(t,"link",n),at(t),e.head.appendChild(t))}function na(e){return'[src="'+kt(e)+'"]'}function Wa(e){return"script[async]"+e}function Oh(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var s=e.querySelector('style[data-href~="'+kt(n.href)+'"]');if(s)return t.instance=s,at(s),s;var i=y({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return s=(e.ownerDocument||e).createElement("style"),at(s),ut(s,"style",i),gr(s,n.precedence,e),t.instance=s;case"stylesheet":i=ta(n.href);var u=e.querySelector($a(i));if(u)return t.state.loading|=4,t.instance=u,at(u),u;s=Ch(n),(i=Xt.get(i))&&lo(s,i),u=(e.ownerDocument||e).createElement("link"),at(u);var f=u;return f._p=new Promise(function(p,v){f.onload=p,f.onerror=v}),ut(u,"link",s),t.state.loading|=4,gr(u,n.precedence,e),t.instance=u;case"script":return u=na(n.src),(i=e.querySelector(Wa(u)))?(t.instance=i,at(i),i):(s=n,(i=Xt.get(u))&&(s=y({},n),ro(s,i)),e=e.ownerDocument||e,i=e.createElement("script"),at(i),ut(i,"link",s),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(o(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(s=t.instance,t.state.loading|=4,gr(s,n.precedence,e));return t.instance}function gr(e,t,n){for(var s=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=s.length?s[s.length-1]:null,u=i,f=0;f<s.length;f++){var p=s[f];if(p.dataset.precedence===t)u=p;else if(u!==i)break}u?u.parentNode.insertBefore(e,u.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function lo(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function ro(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var yr=null;function Dh(e,t,n){if(yr===null){var s=new Map,i=yr=new Map;i.set(n,s)}else i=yr,s=i.get(n),s||(s=new Map,i.set(n,s));if(s.has(e))return s;for(s.set(e,null),n=n.getElementsByTagName(e),i=0;i<n.length;i++){var u=n[i];if(!(u[ua]||u[dt]||e==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var f=u.getAttribute(t)||"";f=e+f;var p=s.get(f);p?p.push(u):s.set(f,[u])}}return s}function Uh(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function wb(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Mh(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Pa=null;function Ab(){}function Eb(e,t,n){if(Pa===null)throw Error(o(475));var s=Pa;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var i=ta(n.href),u=e.querySelector($a(i));if(u){e=u._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(s.count++,s=vr.bind(s),e.then(s,s)),t.state.loading|=4,t.instance=u,at(u);return}u=e.ownerDocument||e,n=Ch(n),(i=Xt.get(i))&&lo(n,i),u=u.createElement("link"),at(u);var f=u;f._p=new Promise(function(p,v){f.onload=p,f.onerror=v}),ut(u,"link",n),t.instance=u}s.stylesheets===null&&(s.stylesheets=new Map),s.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(s.count++,t=vr.bind(s),e.addEventListener("load",t),e.addEventListener("error",t))}}function Tb(){if(Pa===null)throw Error(o(475));var e=Pa;return e.stylesheets&&e.count===0&&io(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&io(e,e.stylesheets),e.unsuspend){var s=e.unsuspend;e.unsuspend=null,s()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function vr(){if(this.count--,this.count===0){if(this.stylesheets)io(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var jr=null;function io(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,jr=new Map,t.forEach(_b,e),jr=null,vr.call(e))}function _b(e,t){if(!(t.state.loading&4)){var n=jr.get(e);if(n)var s=n.get(null);else{n=new Map,jr.set(e,n);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<i.length;u++){var f=i[u];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(n.set(f.dataset.precedence,f),s=f)}s&&n.set(null,s)}i=t.instance,f=i.getAttribute("data-precedence"),u=n.get(f)||s,u===s&&n.set(null,i),n.set(f,i),this.count++,s=vr.bind(this),i.addEventListener("load",s),i.addEventListener("error",s),u?u.parentNode.insertBefore(i,u.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(i,e.firstChild)),t.state.loading|=4}}var Ia={$$typeof:F,Provider:null,Consumer:null,_currentValue:te,_currentValue2:te,_threadCount:0};function Rb(e,t,n,s,i,u,f,p){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=si(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=si(0),this.hiddenUpdates=si(null),this.identifierPrefix=s,this.onUncaughtError=i,this.onCaughtError=u,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=p,this.incompleteTransitions=new Map}function zh(e,t,n,s,i,u,f,p,v,C,q,V){return e=new Rb(e,t,n,f,p,v,C,V),t=1,u===!0&&(t|=24),u=_t(3,null,null,t),e.current=u,u.stateNode=e,t=Vi(),t.refCount++,e.pooledCache=t,t.refCount++,u.memoizedState={element:s,isDehydrated:n,cache:t},Zi(u),e}function Bh(e){return e?(e=Us,e):Us}function kh(e,t,n,s,i,u){i=Bh(i),s.context===null?s.context=i:s.pendingContext=i,s=Rn(t),s.payload={element:n},u=u===void 0?null:u,u!==null&&(s.callback=u),n=Cn(e,s,t),n!==null&&(Ut(n,e,t),Ra(n,e,t))}function qh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function co(e,t){qh(e,t),(e=e.alternate)&&qh(e,t)}function Lh(e){if(e.tag===13){var t=Ds(e,67108864);t!==null&&Ut(t,e,67108864),co(e,67108864)}}var Nr=!0;function Cb(e,t,n,s){var i=N.T;N.T=null;var u=G.p;try{G.p=2,oo(e,t,n,s)}finally{G.p=u,N.T=i}}function Ob(e,t,n,s){var i=N.T;N.T=null;var u=G.p;try{G.p=8,oo(e,t,n,s)}finally{G.p=u,N.T=i}}function oo(e,t,n,s){if(Nr){var i=uo(s);if(i===null)$c(e,t,s,Sr,n),Yh(e,s);else if(Ub(i,e,t,n,s))s.stopPropagation();else if(Yh(e,s),t&4&&-1<Db.indexOf(e)){for(;i!==null;){var u=vs(i);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var f=$n(u.pendingLanes);if(f!==0){var p=u;for(p.pendingLanes|=2,p.entangledLanes|=2;f;){var v=1<<31-Et(f);p.entanglements[1]|=v,f&=~v}tn(u),(Ue&6)===0&&(rr=Oe()+500,Za(0))}}break;case 13:p=Ds(u,2),p!==null&&Ut(p,u,2),cr(),co(u,2)}if(u=uo(s),u===null&&$c(e,t,s,Sr,n),u===i)break;i=u}i!==null&&s.stopPropagation()}else $c(e,t,s,null,n)}}function uo(e){return e=pi(e),fo(e)}var Sr=null;function fo(e){if(Sr=null,e=ys(e),e!==null){var t=h(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=m(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Sr=e,null}function Hh(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(zt()){case Io:return 2;case eu:return 8;case pl:case gp:return 32;case tu:return 268435456;default:return 32}default:return 32}}var ho=!1,Gn=null,Xn=null,Qn=null,el=new Map,tl=new Map,Zn=[],Db="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Yh(e,t){switch(e){case"focusin":case"focusout":Gn=null;break;case"dragenter":case"dragleave":Xn=null;break;case"mouseover":case"mouseout":Qn=null;break;case"pointerover":case"pointerout":el.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":tl.delete(t.pointerId)}}function nl(e,t,n,s,i,u){return e===null||e.nativeEvent!==u?(e={blockedOn:t,domEventName:n,eventSystemFlags:s,nativeEvent:u,targetContainers:[i]},t!==null&&(t=vs(t),t!==null&&Lh(t)),e):(e.eventSystemFlags|=s,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Ub(e,t,n,s,i){switch(t){case"focusin":return Gn=nl(Gn,e,t,n,s,i),!0;case"dragenter":return Xn=nl(Xn,e,t,n,s,i),!0;case"mouseover":return Qn=nl(Qn,e,t,n,s,i),!0;case"pointerover":var u=i.pointerId;return el.set(u,nl(el.get(u)||null,e,t,n,s,i)),!0;case"gotpointercapture":return u=i.pointerId,tl.set(u,nl(tl.get(u)||null,e,t,n,s,i)),!0}return!1}function Vh(e){var t=ys(e.target);if(t!==null){var n=h(t);if(n!==null){if(t=n.tag,t===13){if(t=m(n),t!==null){e.blockedOn=t,Ep(e.priority,function(){if(n.tag===13){var s=Dt();s=ai(s);var i=Ds(n,s);i!==null&&Ut(i,n,s),co(n,s)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function wr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=uo(e.nativeEvent);if(n===null){n=e.nativeEvent;var s=new n.constructor(n.type,n);mi=s,n.target.dispatchEvent(s),mi=null}else return t=vs(n),t!==null&&Lh(t),e.blockedOn=n,!1;t.shift()}return!0}function Gh(e,t,n){wr(e)&&n.delete(t)}function Mb(){ho=!1,Gn!==null&&wr(Gn)&&(Gn=null),Xn!==null&&wr(Xn)&&(Xn=null),Qn!==null&&wr(Qn)&&(Qn=null),el.forEach(Gh),tl.forEach(Gh)}function Ar(e,t){e.blockedOn===t&&(e.blockedOn=null,ho||(ho=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Mb)))}var Er=null;function Xh(e){Er!==e&&(Er=e,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){Er===e&&(Er=null);for(var t=0;t<e.length;t+=3){var n=e[t],s=e[t+1],i=e[t+2];if(typeof s!="function"){if(fo(s||n)===null)continue;break}var u=vs(n);u!==null&&(e.splice(t,3),t-=3,dc(u,{pending:!0,data:i,method:n.method,action:s},s,i))}}))}function sl(e){function t(v){return Ar(v,e)}Gn!==null&&Ar(Gn,e),Xn!==null&&Ar(Xn,e),Qn!==null&&Ar(Qn,e),el.forEach(t),tl.forEach(t);for(var n=0;n<Zn.length;n++){var s=Zn[n];s.blockedOn===e&&(s.blockedOn=null)}for(;0<Zn.length&&(n=Zn[0],n.blockedOn===null);)Vh(n),n.blockedOn===null&&Zn.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(s=0;s<n.length;s+=3){var i=n[s],u=n[s+1],f=i[bt]||null;if(typeof u=="function")f||Xh(n);else if(f){var p=null;if(u&&u.hasAttribute("formAction")){if(i=u,f=u[bt]||null)p=f.formAction;else if(fo(i)!==null)continue}else p=f.action;typeof p=="function"?n[s+1]=p:(n.splice(s,3),s-=3),Xh(n)}}}function mo(e){this._internalRoot=e}Tr.prototype.render=mo.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(o(409));var n=t.current,s=Dt();kh(n,s,e,t,null,null)},Tr.prototype.unmount=mo.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;kh(e.current,2,null,e,null,null),cr(),t[gs]=null}};function Tr(e){this._internalRoot=e}Tr.prototype.unstable_scheduleHydration=function(e){if(e){var t=ru();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Zn.length&&t!==0&&t<Zn[n].priority;n++);Zn.splice(n,0,e),n===0&&Vh(e)}};var Qh=r.version;if(Qh!=="19.1.1")throw Error(o(527,Qh,"19.1.1"));G.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(o(188)):(e=Object.keys(e).join(","),Error(o(268,e)));return e=g(t),e=e!==null?x(e):null,e=e===null?null:e.stateNode,e};var zb={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:N,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var _r=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!_r.isDisabled&&_r.supportsFiber)try{ia=_r.inject(zb),At=_r}catch{}}return ll.createRoot=function(e,t){if(!d(e))throw Error(o(299));var n=!1,s="",i=rf,u=cf,f=of,p=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(s=t.identifierPrefix),t.onUncaughtError!==void 0&&(i=t.onUncaughtError),t.onCaughtError!==void 0&&(u=t.onCaughtError),t.onRecoverableError!==void 0&&(f=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(p=t.unstable_transitionCallbacks)),t=zh(e,1,!1,null,null,n,s,i,u,f,p,null),e[gs]=t.current,Fc(e),new mo(t)},ll.hydrateRoot=function(e,t,n){if(!d(e))throw Error(o(299));var s=!1,i="",u=rf,f=cf,p=of,v=null,C=null;return n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onUncaughtError!==void 0&&(u=n.onUncaughtError),n.onCaughtError!==void 0&&(f=n.onCaughtError),n.onRecoverableError!==void 0&&(p=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(v=n.unstable_transitionCallbacks),n.formState!==void 0&&(C=n.formState)),t=zh(e,1,!0,t,n??null,s,i,u,f,p,v,C),t.context=Bh(null),n=t.current,s=Dt(),s=ai(s),i=Rn(s),i.callback=null,Cn(n,i,s),n=s,t.current.lanes=n,oa(t,n),tn(t),e[gs]=t.current,Fc(e),new Tr(t)},ll.version="19.1.1",ll}var tm;function Qb(){if(tm)return bo.exports;tm=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(r){console.error(r)}}return a(),bo.exports=Xb(),bo.exports}var Zb=Qb();const Kb=wm(Zb);var Te=(a=>(a.FOR="FOR",a.AGAINST="AGAINST",a))(Te||{}),ol=(a=>(a.SUPPORT="support",a.OPPOSE="oppose",a))(ol||{});function Em(a,r){return function(){return a.apply(r,arguments)}}const{toString:Jb}=Object.prototype,{getPrototypeOf:Qo}=Object,{iterator:Fr,toStringTag:Tm}=Symbol,$r=(a=>r=>{const c=Jb.call(r);return a[c]||(a[c]=c.slice(8,-1).toLowerCase())})(Object.create(null)),$t=a=>(a=a.toLowerCase(),r=>$r(r)===a),Wr=a=>r=>typeof r===a,{isArray:aa}=Array,ul=Wr("undefined");function dl(a){return a!==null&&!ul(a)&&a.constructor!==null&&!ul(a.constructor)&&St(a.constructor.isBuffer)&&a.constructor.isBuffer(a)}const _m=$t("ArrayBuffer");function Fb(a){let r;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?r=ArrayBuffer.isView(a):r=a&&a.buffer&&_m(a.buffer),r}const $b=Wr("string"),St=Wr("function"),Rm=Wr("number"),fl=a=>a!==null&&typeof a=="object",Wb=a=>a===!0||a===!1,Ur=a=>{if($r(a)!=="object")return!1;const r=Qo(a);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Tm in a)&&!(Fr in a)},Pb=a=>{if(!fl(a)||dl(a))return!1;try{return Object.keys(a).length===0&&Object.getPrototypeOf(a)===Object.prototype}catch{return!1}},Ib=$t("Date"),eg=$t("File"),tg=$t("Blob"),ng=$t("FileList"),sg=a=>fl(a)&&St(a.pipe),ag=a=>{let r;return a&&(typeof FormData=="function"&&a instanceof FormData||St(a.append)&&((r=$r(a))==="formdata"||r==="object"&&St(a.toString)&&a.toString()==="[object FormData]"))},lg=$t("URLSearchParams"),[rg,ig,cg,og]=["ReadableStream","Request","Response","Headers"].map($t),ug=a=>a.trim?a.trim():a.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function hl(a,r,{allOwnKeys:c=!1}={}){if(a===null||typeof a>"u")return;let o,d;if(typeof a!="object"&&(a=[a]),aa(a))for(o=0,d=a.length;o<d;o++)r.call(null,a[o],o,a);else{if(dl(a))return;const h=c?Object.getOwnPropertyNames(a):Object.keys(a),m=h.length;let b;for(o=0;o<m;o++)b=h[o],r.call(null,a[b],b,a)}}function Cm(a,r){if(dl(a))return null;r=r.toLowerCase();const c=Object.keys(a);let o=c.length,d;for(;o-- >0;)if(d=c[o],r===d.toLowerCase())return d;return null}const ms=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Om=a=>!ul(a)&&a!==ms;function _o(){const{caseless:a}=Om(this)&&this||{},r={},c=(o,d)=>{const h=a&&Cm(r,d)||d;Ur(r[h])&&Ur(o)?r[h]=_o(r[h],o):Ur(o)?r[h]=_o({},o):aa(o)?r[h]=o.slice():r[h]=o};for(let o=0,d=arguments.length;o<d;o++)arguments[o]&&hl(arguments[o],c);return r}const dg=(a,r,c,{allOwnKeys:o}={})=>(hl(r,(d,h)=>{c&&St(d)?a[h]=Em(d,c):a[h]=d},{allOwnKeys:o}),a),fg=a=>(a.charCodeAt(0)===65279&&(a=a.slice(1)),a),hg=(a,r,c,o)=>{a.prototype=Object.create(r.prototype,o),a.prototype.constructor=a,Object.defineProperty(a,"super",{value:r.prototype}),c&&Object.assign(a.prototype,c)},mg=(a,r,c,o)=>{let d,h,m;const b={};if(r=r||{},a==null)return r;do{for(d=Object.getOwnPropertyNames(a),h=d.length;h-- >0;)m=d[h],(!o||o(m,a,r))&&!b[m]&&(r[m]=a[m],b[m]=!0);a=c!==!1&&Qo(a)}while(a&&(!c||c(a,r))&&a!==Object.prototype);return r},pg=(a,r,c)=>{a=String(a),(c===void 0||c>a.length)&&(c=a.length),c-=r.length;const o=a.indexOf(r,c);return o!==-1&&o===c},xg=a=>{if(!a)return null;if(aa(a))return a;let r=a.length;if(!Rm(r))return null;const c=new Array(r);for(;r-- >0;)c[r]=a[r];return c},bg=(a=>r=>a&&r instanceof a)(typeof Uint8Array<"u"&&Qo(Uint8Array)),gg=(a,r)=>{const o=(a&&a[Fr]).call(a);let d;for(;(d=o.next())&&!d.done;){const h=d.value;r.call(a,h[0],h[1])}},yg=(a,r)=>{let c;const o=[];for(;(c=a.exec(r))!==null;)o.push(c);return o},vg=$t("HTMLFormElement"),jg=a=>a.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(c,o,d){return o.toUpperCase()+d}),nm=(({hasOwnProperty:a})=>(r,c)=>a.call(r,c))(Object.prototype),Ng=$t("RegExp"),Dm=(a,r)=>{const c=Object.getOwnPropertyDescriptors(a),o={};hl(c,(d,h)=>{let m;(m=r(d,h,a))!==!1&&(o[h]=m||d)}),Object.defineProperties(a,o)},Sg=a=>{Dm(a,(r,c)=>{if(St(a)&&["arguments","caller","callee"].indexOf(c)!==-1)return!1;const o=a[c];if(St(o)){if(r.enumerable=!1,"writable"in r){r.writable=!1;return}r.set||(r.set=()=>{throw Error("Can not rewrite read-only method '"+c+"'")})}})},wg=(a,r)=>{const c={},o=d=>{d.forEach(h=>{c[h]=!0})};return aa(a)?o(a):o(String(a).split(r)),c},Ag=()=>{},Eg=(a,r)=>a!=null&&Number.isFinite(a=+a)?a:r;function Tg(a){return!!(a&&St(a.append)&&a[Tm]==="FormData"&&a[Fr])}const _g=a=>{const r=new Array(10),c=(o,d)=>{if(fl(o)){if(r.indexOf(o)>=0)return;if(dl(o))return o;if(!("toJSON"in o)){r[d]=o;const h=aa(o)?[]:{};return hl(o,(m,b)=>{const g=c(m,d+1);!ul(g)&&(h[b]=g)}),r[d]=void 0,h}}return o};return c(a,0)},Rg=$t("AsyncFunction"),Cg=a=>a&&(fl(a)||St(a))&&St(a.then)&&St(a.catch),Um=((a,r)=>a?setImmediate:r?((c,o)=>(ms.addEventListener("message",({source:d,data:h})=>{d===ms&&h===c&&o.length&&o.shift()()},!1),d=>{o.push(d),ms.postMessage(c,"*")}))(`axios@${Math.random()}`,[]):c=>setTimeout(c))(typeof setImmediate=="function",St(ms.postMessage)),Og=typeof queueMicrotask<"u"?queueMicrotask.bind(ms):typeof process<"u"&&process.nextTick||Um,Dg=a=>a!=null&&St(a[Fr]),z={isArray:aa,isArrayBuffer:_m,isBuffer:dl,isFormData:ag,isArrayBufferView:Fb,isString:$b,isNumber:Rm,isBoolean:Wb,isObject:fl,isPlainObject:Ur,isEmptyObject:Pb,isReadableStream:rg,isRequest:ig,isResponse:cg,isHeaders:og,isUndefined:ul,isDate:Ib,isFile:eg,isBlob:tg,isRegExp:Ng,isFunction:St,isStream:sg,isURLSearchParams:lg,isTypedArray:bg,isFileList:ng,forEach:hl,merge:_o,extend:dg,trim:ug,stripBOM:fg,inherits:hg,toFlatObject:mg,kindOf:$r,kindOfTest:$t,endsWith:pg,toArray:xg,forEachEntry:gg,matchAll:yg,isHTMLForm:vg,hasOwnProperty:nm,hasOwnProp:nm,reduceDescriptors:Dm,freezeMethods:Sg,toObjectSet:wg,toCamelCase:jg,noop:Ag,toFiniteNumber:Eg,findKey:Cm,global:ms,isContextDefined:Om,isSpecCompliantForm:Tg,toJSONObject:_g,isAsyncFn:Rg,isThenable:Cg,setImmediate:Um,asap:Og,isIterable:Dg};function fe(a,r,c,o,d){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=a,this.name="AxiosError",r&&(this.code=r),c&&(this.config=c),o&&(this.request=o),d&&(this.response=d,this.status=d.status?d.status:null)}z.inherits(fe,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:z.toJSONObject(this.config),code:this.code,status:this.status}}});const Mm=fe.prototype,zm={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(a=>{zm[a]={value:a}});Object.defineProperties(fe,zm);Object.defineProperty(Mm,"isAxiosError",{value:!0});fe.from=(a,r,c,o,d,h)=>{const m=Object.create(Mm);return z.toFlatObject(a,m,function(g){return g!==Error.prototype},b=>b!=="isAxiosError"),fe.call(m,a.message,r,c,o,d),m.cause=a,m.name=a.name,h&&Object.assign(m,h),m};const Ug=null;function Ro(a){return z.isPlainObject(a)||z.isArray(a)}function Bm(a){return z.endsWith(a,"[]")?a.slice(0,-2):a}function sm(a,r,c){return a?a.concat(r).map(function(d,h){return d=Bm(d),!c&&h?"["+d+"]":d}).join(c?".":""):r}function Mg(a){return z.isArray(a)&&!a.some(Ro)}const zg=z.toFlatObject(z,{},null,function(r){return/^is[A-Z]/.test(r)});function Pr(a,r,c){if(!z.isObject(a))throw new TypeError("target must be an object");r=r||new FormData,c=z.toFlatObject(c,{metaTokens:!0,dots:!1,indexes:!1},!1,function(A,T){return!z.isUndefined(T[A])});const o=c.metaTokens,d=c.visitor||y,h=c.dots,m=c.indexes,g=(c.Blob||typeof Blob<"u"&&Blob)&&z.isSpecCompliantForm(r);if(!z.isFunction(d))throw new TypeError("visitor must be a function");function x(O){if(O===null)return"";if(z.isDate(O))return O.toISOString();if(z.isBoolean(O))return O.toString();if(!g&&z.isBlob(O))throw new fe("Blob is not supported. Use a Buffer instead.");return z.isArrayBuffer(O)||z.isTypedArray(O)?g&&typeof Blob=="function"?new Blob([O]):Buffer.from(O):O}function y(O,A,T){let X=O;if(O&&!T&&typeof O=="object"){if(z.endsWith(A,"{}"))A=o?A:A.slice(0,-2),O=JSON.stringify(O);else if(z.isArray(O)&&Mg(O)||(z.isFileList(O)||z.endsWith(A,"[]"))&&(X=z.toArray(O)))return A=Bm(A),X.forEach(function(F,ae){!(z.isUndefined(F)||F===null)&&r.append(m===!0?sm([A],ae,h):m===null?A:A+"[]",x(F))}),!1}return Ro(O)?!0:(r.append(sm(T,A,h),x(O)),!1)}const S=[],U=Object.assign(zg,{defaultVisitor:y,convertValue:x,isVisitable:Ro});function B(O,A){if(!z.isUndefined(O)){if(S.indexOf(O)!==-1)throw Error("Circular reference detected in "+A.join("."));S.push(O),z.forEach(O,function(X,L){(!(z.isUndefined(X)||X===null)&&d.call(r,X,z.isString(L)?L.trim():L,A,U))===!0&&B(X,A?A.concat(L):[L])}),S.pop()}}if(!z.isObject(a))throw new TypeError("data must be an object");return B(a),r}function am(a){const r={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(a).replace(/[!'()~]|%20|%00/g,function(o){return r[o]})}function Zo(a,r){this._pairs=[],a&&Pr(a,this,r)}const km=Zo.prototype;km.append=function(r,c){this._pairs.push([r,c])};km.toString=function(r){const c=r?function(o){return r.call(this,o,am)}:am;return this._pairs.map(function(d){return c(d[0])+"="+c(d[1])},"").join("&")};function Bg(a){return encodeURIComponent(a).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function qm(a,r,c){if(!r)return a;const o=c&&c.encode||Bg;z.isFunction(c)&&(c={serialize:c});const d=c&&c.serialize;let h;if(d?h=d(r,c):h=z.isURLSearchParams(r)?r.toString():new Zo(r,c).toString(o),h){const m=a.indexOf("#");m!==-1&&(a=a.slice(0,m)),a+=(a.indexOf("?")===-1?"?":"&")+h}return a}class lm{constructor(){this.handlers=[]}use(r,c,o){return this.handlers.push({fulfilled:r,rejected:c,synchronous:o?o.synchronous:!1,runWhen:o?o.runWhen:null}),this.handlers.length-1}eject(r){this.handlers[r]&&(this.handlers[r]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(r){z.forEach(this.handlers,function(o){o!==null&&r(o)})}}const Lm={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},kg=typeof URLSearchParams<"u"?URLSearchParams:Zo,qg=typeof FormData<"u"?FormData:null,Lg=typeof Blob<"u"?Blob:null,Hg={isBrowser:!0,classes:{URLSearchParams:kg,FormData:qg,Blob:Lg},protocols:["http","https","file","blob","url","data"]},Ko=typeof window<"u"&&typeof document<"u",Co=typeof navigator=="object"&&navigator||void 0,Yg=Ko&&(!Co||["ReactNative","NativeScript","NS"].indexOf(Co.product)<0),Vg=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Gg=Ko&&window.location.href||"http://localhost",Xg=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ko,hasStandardBrowserEnv:Yg,hasStandardBrowserWebWorkerEnv:Vg,navigator:Co,origin:Gg},Symbol.toStringTag,{value:"Module"})),mt={...Xg,...Hg};function Qg(a,r){return Pr(a,new mt.classes.URLSearchParams,{visitor:function(c,o,d,h){return mt.isNode&&z.isBuffer(c)?(this.append(o,c.toString("base64")),!1):h.defaultVisitor.apply(this,arguments)},...r})}function Zg(a){return z.matchAll(/\w+|\[(\w*)]/g,a).map(r=>r[0]==="[]"?"":r[1]||r[0])}function Kg(a){const r={},c=Object.keys(a);let o;const d=c.length;let h;for(o=0;o<d;o++)h=c[o],r[h]=a[h];return r}function Hm(a){function r(c,o,d,h){let m=c[h++];if(m==="__proto__")return!0;const b=Number.isFinite(+m),g=h>=c.length;return m=!m&&z.isArray(d)?d.length:m,g?(z.hasOwnProp(d,m)?d[m]=[d[m],o]:d[m]=o,!b):((!d[m]||!z.isObject(d[m]))&&(d[m]=[]),r(c,o,d[m],h)&&z.isArray(d[m])&&(d[m]=Kg(d[m])),!b)}if(z.isFormData(a)&&z.isFunction(a.entries)){const c={};return z.forEachEntry(a,(o,d)=>{r(Zg(o),d,c,0)}),c}return null}function Jg(a,r,c){if(z.isString(a))try{return(r||JSON.parse)(a),z.trim(a)}catch(o){if(o.name!=="SyntaxError")throw o}return(c||JSON.stringify)(a)}const ml={transitional:Lm,adapter:["xhr","http","fetch"],transformRequest:[function(r,c){const o=c.getContentType()||"",d=o.indexOf("application/json")>-1,h=z.isObject(r);if(h&&z.isHTMLForm(r)&&(r=new FormData(r)),z.isFormData(r))return d?JSON.stringify(Hm(r)):r;if(z.isArrayBuffer(r)||z.isBuffer(r)||z.isStream(r)||z.isFile(r)||z.isBlob(r)||z.isReadableStream(r))return r;if(z.isArrayBufferView(r))return r.buffer;if(z.isURLSearchParams(r))return c.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),r.toString();let b;if(h){if(o.indexOf("application/x-www-form-urlencoded")>-1)return Qg(r,this.formSerializer).toString();if((b=z.isFileList(r))||o.indexOf("multipart/form-data")>-1){const g=this.env&&this.env.FormData;return Pr(b?{"files[]":r}:r,g&&new g,this.formSerializer)}}return h||d?(c.setContentType("application/json",!1),Jg(r)):r}],transformResponse:[function(r){const c=this.transitional||ml.transitional,o=c&&c.forcedJSONParsing,d=this.responseType==="json";if(z.isResponse(r)||z.isReadableStream(r))return r;if(r&&z.isString(r)&&(o&&!this.responseType||d)){const m=!(c&&c.silentJSONParsing)&&d;try{return JSON.parse(r)}catch(b){if(m)throw b.name==="SyntaxError"?fe.from(b,fe.ERR_BAD_RESPONSE,this,null,this.response):b}}return r}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:mt.classes.FormData,Blob:mt.classes.Blob},validateStatus:function(r){return r>=200&&r<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};z.forEach(["delete","get","head","post","put","patch"],a=>{ml.headers[a]={}});const Fg=z.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),$g=a=>{const r={};let c,o,d;return a&&a.split(`
`).forEach(function(m){d=m.indexOf(":"),c=m.substring(0,d).trim().toLowerCase(),o=m.substring(d+1).trim(),!(!c||r[c]&&Fg[c])&&(c==="set-cookie"?r[c]?r[c].push(o):r[c]=[o]:r[c]=r[c]?r[c]+", "+o:o)}),r},rm=Symbol("internals");function rl(a){return a&&String(a).trim().toLowerCase()}function Mr(a){return a===!1||a==null?a:z.isArray(a)?a.map(Mr):String(a)}function Wg(a){const r=Object.create(null),c=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let o;for(;o=c.exec(a);)r[o[1]]=o[2];return r}const Pg=a=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(a.trim());function jo(a,r,c,o,d){if(z.isFunction(o))return o.call(this,r,c);if(d&&(r=c),!!z.isString(r)){if(z.isString(o))return r.indexOf(o)!==-1;if(z.isRegExp(o))return o.test(r)}}function Ig(a){return a.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(r,c,o)=>c.toUpperCase()+o)}function ey(a,r){const c=z.toCamelCase(" "+r);["get","set","has"].forEach(o=>{Object.defineProperty(a,o+c,{value:function(d,h,m){return this[o].call(this,r,d,h,m)},configurable:!0})})}let wt=class{constructor(r){r&&this.set(r)}set(r,c,o){const d=this;function h(b,g,x){const y=rl(g);if(!y)throw new Error("header name must be a non-empty string");const S=z.findKey(d,y);(!S||d[S]===void 0||x===!0||x===void 0&&d[S]!==!1)&&(d[S||g]=Mr(b))}const m=(b,g)=>z.forEach(b,(x,y)=>h(x,y,g));if(z.isPlainObject(r)||r instanceof this.constructor)m(r,c);else if(z.isString(r)&&(r=r.trim())&&!Pg(r))m($g(r),c);else if(z.isObject(r)&&z.isIterable(r)){let b={},g,x;for(const y of r){if(!z.isArray(y))throw TypeError("Object iterator must return a key-value pair");b[x=y[0]]=(g=b[x])?z.isArray(g)?[...g,y[1]]:[g,y[1]]:y[1]}m(b,c)}else r!=null&&h(c,r,o);return this}get(r,c){if(r=rl(r),r){const o=z.findKey(this,r);if(o){const d=this[o];if(!c)return d;if(c===!0)return Wg(d);if(z.isFunction(c))return c.call(this,d,o);if(z.isRegExp(c))return c.exec(d);throw new TypeError("parser must be boolean|regexp|function")}}}has(r,c){if(r=rl(r),r){const o=z.findKey(this,r);return!!(o&&this[o]!==void 0&&(!c||jo(this,this[o],o,c)))}return!1}delete(r,c){const o=this;let d=!1;function h(m){if(m=rl(m),m){const b=z.findKey(o,m);b&&(!c||jo(o,o[b],b,c))&&(delete o[b],d=!0)}}return z.isArray(r)?r.forEach(h):h(r),d}clear(r){const c=Object.keys(this);let o=c.length,d=!1;for(;o--;){const h=c[o];(!r||jo(this,this[h],h,r,!0))&&(delete this[h],d=!0)}return d}normalize(r){const c=this,o={};return z.forEach(this,(d,h)=>{const m=z.findKey(o,h);if(m){c[m]=Mr(d),delete c[h];return}const b=r?Ig(h):String(h).trim();b!==h&&delete c[h],c[b]=Mr(d),o[b]=!0}),this}concat(...r){return this.constructor.concat(this,...r)}toJSON(r){const c=Object.create(null);return z.forEach(this,(o,d)=>{o!=null&&o!==!1&&(c[d]=r&&z.isArray(o)?o.join(", "):o)}),c}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([r,c])=>r+": "+c).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(r){return r instanceof this?r:new this(r)}static concat(r,...c){const o=new this(r);return c.forEach(d=>o.set(d)),o}static accessor(r){const o=(this[rm]=this[rm]={accessors:{}}).accessors,d=this.prototype;function h(m){const b=rl(m);o[b]||(ey(d,m),o[b]=!0)}return z.isArray(r)?r.forEach(h):h(r),this}};wt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);z.reduceDescriptors(wt.prototype,({value:a},r)=>{let c=r[0].toUpperCase()+r.slice(1);return{get:()=>a,set(o){this[c]=o}}});z.freezeMethods(wt);function No(a,r){const c=this||ml,o=r||c,d=wt.from(o.headers);let h=o.data;return z.forEach(a,function(b){h=b.call(c,h,d.normalize(),r?r.status:void 0)}),d.normalize(),h}function Ym(a){return!!(a&&a.__CANCEL__)}function la(a,r,c){fe.call(this,a??"canceled",fe.ERR_CANCELED,r,c),this.name="CanceledError"}z.inherits(la,fe,{__CANCEL__:!0});function Vm(a,r,c){const o=c.config.validateStatus;!c.status||!o||o(c.status)?a(c):r(new fe("Request failed with status code "+c.status,[fe.ERR_BAD_REQUEST,fe.ERR_BAD_RESPONSE][Math.floor(c.status/100)-4],c.config,c.request,c))}function ty(a){const r=/^([-+\w]{1,25})(:?\/\/|:)/.exec(a);return r&&r[1]||""}function ny(a,r){a=a||10;const c=new Array(a),o=new Array(a);let d=0,h=0,m;return r=r!==void 0?r:1e3,function(g){const x=Date.now(),y=o[h];m||(m=x),c[d]=g,o[d]=x;let S=h,U=0;for(;S!==d;)U+=c[S++],S=S%a;if(d=(d+1)%a,d===h&&(h=(h+1)%a),x-m<r)return;const B=y&&x-y;return B?Math.round(U*1e3/B):void 0}}function sy(a,r){let c=0,o=1e3/r,d,h;const m=(x,y=Date.now())=>{c=y,d=null,h&&(clearTimeout(h),h=null),a(...x)};return[(...x)=>{const y=Date.now(),S=y-c;S>=o?m(x,y):(d=x,h||(h=setTimeout(()=>{h=null,m(d)},o-S)))},()=>d&&m(d)]}const Vr=(a,r,c=3)=>{let o=0;const d=ny(50,250);return sy(h=>{const m=h.loaded,b=h.lengthComputable?h.total:void 0,g=m-o,x=d(g),y=m<=b;o=m;const S={loaded:m,total:b,progress:b?m/b:void 0,bytes:g,rate:x||void 0,estimated:x&&b&&y?(b-m)/x:void 0,event:h,lengthComputable:b!=null,[r?"download":"upload"]:!0};a(S)},c)},im=(a,r)=>{const c=a!=null;return[o=>r[0]({lengthComputable:c,total:a,loaded:o}),r[1]]},cm=a=>(...r)=>z.asap(()=>a(...r)),ay=mt.hasStandardBrowserEnv?((a,r)=>c=>(c=new URL(c,mt.origin),a.protocol===c.protocol&&a.host===c.host&&(r||a.port===c.port)))(new URL(mt.origin),mt.navigator&&/(msie|trident)/i.test(mt.navigator.userAgent)):()=>!0,ly=mt.hasStandardBrowserEnv?{write(a,r,c,o,d,h){const m=[a+"="+encodeURIComponent(r)];z.isNumber(c)&&m.push("expires="+new Date(c).toGMTString()),z.isString(o)&&m.push("path="+o),z.isString(d)&&m.push("domain="+d),h===!0&&m.push("secure"),document.cookie=m.join("; ")},read(a){const r=document.cookie.match(new RegExp("(^|;\\s*)("+a+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove(a){this.write(a,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function ry(a){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(a)}function iy(a,r){return r?a.replace(/\/?\/$/,"")+"/"+r.replace(/^\/+/,""):a}function Gm(a,r,c){let o=!ry(r);return a&&(o||c==!1)?iy(a,r):r}const om=a=>a instanceof wt?{...a}:a;function xs(a,r){r=r||{};const c={};function o(x,y,S,U){return z.isPlainObject(x)&&z.isPlainObject(y)?z.merge.call({caseless:U},x,y):z.isPlainObject(y)?z.merge({},y):z.isArray(y)?y.slice():y}function d(x,y,S,U){if(z.isUndefined(y)){if(!z.isUndefined(x))return o(void 0,x,S,U)}else return o(x,y,S,U)}function h(x,y){if(!z.isUndefined(y))return o(void 0,y)}function m(x,y){if(z.isUndefined(y)){if(!z.isUndefined(x))return o(void 0,x)}else return o(void 0,y)}function b(x,y,S){if(S in r)return o(x,y);if(S in a)return o(void 0,x)}const g={url:h,method:h,data:h,baseURL:m,transformRequest:m,transformResponse:m,paramsSerializer:m,timeout:m,timeoutMessage:m,withCredentials:m,withXSRFToken:m,adapter:m,responseType:m,xsrfCookieName:m,xsrfHeaderName:m,onUploadProgress:m,onDownloadProgress:m,decompress:m,maxContentLength:m,maxBodyLength:m,beforeRedirect:m,transport:m,httpAgent:m,httpsAgent:m,cancelToken:m,socketPath:m,responseEncoding:m,validateStatus:b,headers:(x,y,S)=>d(om(x),om(y),S,!0)};return z.forEach(Object.keys({...a,...r}),function(y){const S=g[y]||d,U=S(a[y],r[y],y);z.isUndefined(U)&&S!==b||(c[y]=U)}),c}const Xm=a=>{const r=xs({},a);let{data:c,withXSRFToken:o,xsrfHeaderName:d,xsrfCookieName:h,headers:m,auth:b}=r;r.headers=m=wt.from(m),r.url=qm(Gm(r.baseURL,r.url,r.allowAbsoluteUrls),a.params,a.paramsSerializer),b&&m.set("Authorization","Basic "+btoa((b.username||"")+":"+(b.password?unescape(encodeURIComponent(b.password)):"")));let g;if(z.isFormData(c)){if(mt.hasStandardBrowserEnv||mt.hasStandardBrowserWebWorkerEnv)m.setContentType(void 0);else if((g=m.getContentType())!==!1){const[x,...y]=g?g.split(";").map(S=>S.trim()).filter(Boolean):[];m.setContentType([x||"multipart/form-data",...y].join("; "))}}if(mt.hasStandardBrowserEnv&&(o&&z.isFunction(o)&&(o=o(r)),o||o!==!1&&ay(r.url))){const x=d&&h&&ly.read(h);x&&m.set(d,x)}return r},cy=typeof XMLHttpRequest<"u",oy=cy&&function(a){return new Promise(function(c,o){const d=Xm(a);let h=d.data;const m=wt.from(d.headers).normalize();let{responseType:b,onUploadProgress:g,onDownloadProgress:x}=d,y,S,U,B,O;function A(){B&&B(),O&&O(),d.cancelToken&&d.cancelToken.unsubscribe(y),d.signal&&d.signal.removeEventListener("abort",y)}let T=new XMLHttpRequest;T.open(d.method.toUpperCase(),d.url,!0),T.timeout=d.timeout;function X(){if(!T)return;const F=wt.from("getAllResponseHeaders"in T&&T.getAllResponseHeaders()),P={data:!b||b==="text"||b==="json"?T.responseText:T.response,status:T.status,statusText:T.statusText,headers:F,config:a,request:T};Vm(function(le){c(le),A()},function(le){o(le),A()},P),T=null}"onloadend"in T?T.onloadend=X:T.onreadystatechange=function(){!T||T.readyState!==4||T.status===0&&!(T.responseURL&&T.responseURL.indexOf("file:")===0)||setTimeout(X)},T.onabort=function(){T&&(o(new fe("Request aborted",fe.ECONNABORTED,a,T)),T=null)},T.onerror=function(){o(new fe("Network Error",fe.ERR_NETWORK,a,T)),T=null},T.ontimeout=function(){let ae=d.timeout?"timeout of "+d.timeout+"ms exceeded":"timeout exceeded";const P=d.transitional||Lm;d.timeoutErrorMessage&&(ae=d.timeoutErrorMessage),o(new fe(ae,P.clarifyTimeoutError?fe.ETIMEDOUT:fe.ECONNABORTED,a,T)),T=null},h===void 0&&m.setContentType(null),"setRequestHeader"in T&&z.forEach(m.toJSON(),function(ae,P){T.setRequestHeader(P,ae)}),z.isUndefined(d.withCredentials)||(T.withCredentials=!!d.withCredentials),b&&b!=="json"&&(T.responseType=d.responseType),x&&([U,O]=Vr(x,!0),T.addEventListener("progress",U)),g&&T.upload&&([S,B]=Vr(g),T.upload.addEventListener("progress",S),T.upload.addEventListener("loadend",B)),(d.cancelToken||d.signal)&&(y=F=>{T&&(o(!F||F.type?new la(null,a,T):F),T.abort(),T=null)},d.cancelToken&&d.cancelToken.subscribe(y),d.signal&&(d.signal.aborted?y():d.signal.addEventListener("abort",y)));const L=ty(d.url);if(L&&mt.protocols.indexOf(L)===-1){o(new fe("Unsupported protocol "+L+":",fe.ERR_BAD_REQUEST,a));return}T.send(h||null)})},uy=(a,r)=>{const{length:c}=a=a?a.filter(Boolean):[];if(r||c){let o=new AbortController,d;const h=function(x){if(!d){d=!0,b();const y=x instanceof Error?x:this.reason;o.abort(y instanceof fe?y:new la(y instanceof Error?y.message:y))}};let m=r&&setTimeout(()=>{m=null,h(new fe(`timeout ${r} of ms exceeded`,fe.ETIMEDOUT))},r);const b=()=>{a&&(m&&clearTimeout(m),m=null,a.forEach(x=>{x.unsubscribe?x.unsubscribe(h):x.removeEventListener("abort",h)}),a=null)};a.forEach(x=>x.addEventListener("abort",h));const{signal:g}=o;return g.unsubscribe=()=>z.asap(b),g}},dy=function*(a,r){let c=a.byteLength;if(c<r){yield a;return}let o=0,d;for(;o<c;)d=o+r,yield a.slice(o,d),o=d},fy=async function*(a,r){for await(const c of hy(a))yield*dy(c,r)},hy=async function*(a){if(a[Symbol.asyncIterator]){yield*a;return}const r=a.getReader();try{for(;;){const{done:c,value:o}=await r.read();if(c)break;yield o}}finally{await r.cancel()}},um=(a,r,c,o)=>{const d=fy(a,r);let h=0,m,b=g=>{m||(m=!0,o&&o(g))};return new ReadableStream({async pull(g){try{const{done:x,value:y}=await d.next();if(x){b(),g.close();return}let S=y.byteLength;if(c){let U=h+=S;c(U)}g.enqueue(new Uint8Array(y))}catch(x){throw b(x),x}},cancel(g){return b(g),d.return()}},{highWaterMark:2})},Ir=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Qm=Ir&&typeof ReadableStream=="function",my=Ir&&(typeof TextEncoder=="function"?(a=>r=>a.encode(r))(new TextEncoder):async a=>new Uint8Array(await new Response(a).arrayBuffer())),Zm=(a,...r)=>{try{return!!a(...r)}catch{return!1}},py=Qm&&Zm(()=>{let a=!1;const r=new Request(mt.origin,{body:new ReadableStream,method:"POST",get duplex(){return a=!0,"half"}}).headers.has("Content-Type");return a&&!r}),dm=64*1024,Oo=Qm&&Zm(()=>z.isReadableStream(new Response("").body)),Gr={stream:Oo&&(a=>a.body)};Ir&&(a=>{["text","arrayBuffer","blob","formData","stream"].forEach(r=>{!Gr[r]&&(Gr[r]=z.isFunction(a[r])?c=>c[r]():(c,o)=>{throw new fe(`Response type '${r}' is not supported`,fe.ERR_NOT_SUPPORT,o)})})})(new Response);const xy=async a=>{if(a==null)return 0;if(z.isBlob(a))return a.size;if(z.isSpecCompliantForm(a))return(await new Request(mt.origin,{method:"POST",body:a}).arrayBuffer()).byteLength;if(z.isArrayBufferView(a)||z.isArrayBuffer(a))return a.byteLength;if(z.isURLSearchParams(a)&&(a=a+""),z.isString(a))return(await my(a)).byteLength},by=async(a,r)=>{const c=z.toFiniteNumber(a.getContentLength());return c??xy(r)},gy=Ir&&(async a=>{let{url:r,method:c,data:o,signal:d,cancelToken:h,timeout:m,onDownloadProgress:b,onUploadProgress:g,responseType:x,headers:y,withCredentials:S="same-origin",fetchOptions:U}=Xm(a);x=x?(x+"").toLowerCase():"text";let B=uy([d,h&&h.toAbortSignal()],m),O;const A=B&&B.unsubscribe&&(()=>{B.unsubscribe()});let T;try{if(g&&py&&c!=="get"&&c!=="head"&&(T=await by(y,o))!==0){let P=new Request(r,{method:"POST",body:o,duplex:"half"}),K;if(z.isFormData(o)&&(K=P.headers.get("content-type"))&&y.setContentType(K),P.body){const[le,Q]=im(T,Vr(cm(g)));o=um(P.body,dm,le,Q)}}z.isString(S)||(S=S?"include":"omit");const X="credentials"in Request.prototype;O=new Request(r,{...U,signal:B,method:c.toUpperCase(),headers:y.normalize().toJSON(),body:o,duplex:"half",credentials:X?S:void 0});let L=await fetch(O,U);const F=Oo&&(x==="stream"||x==="response");if(Oo&&(b||F&&A)){const P={};["status","statusText","headers"].forEach($=>{P[$]=L[$]});const K=z.toFiniteNumber(L.headers.get("content-length")),[le,Q]=b&&im(K,Vr(cm(b),!0))||[];L=new Response(um(L.body,dm,le,()=>{Q&&Q(),A&&A()}),P)}x=x||"text";let ae=await Gr[z.findKey(Gr,x)||"text"](L,a);return!F&&A&&A(),await new Promise((P,K)=>{Vm(P,K,{data:ae,headers:wt.from(L.headers),status:L.status,statusText:L.statusText,config:a,request:O})})}catch(X){throw A&&A(),X&&X.name==="TypeError"&&/Load failed|fetch/i.test(X.message)?Object.assign(new fe("Network Error",fe.ERR_NETWORK,a,O),{cause:X.cause||X}):fe.from(X,X&&X.code,a,O)}}),Do={http:Ug,xhr:oy,fetch:gy};z.forEach(Do,(a,r)=>{if(a){try{Object.defineProperty(a,"name",{value:r})}catch{}Object.defineProperty(a,"adapterName",{value:r})}});const fm=a=>`- ${a}`,yy=a=>z.isFunction(a)||a===null||a===!1,Km={getAdapter:a=>{a=z.isArray(a)?a:[a];const{length:r}=a;let c,o;const d={};for(let h=0;h<r;h++){c=a[h];let m;if(o=c,!yy(c)&&(o=Do[(m=String(c)).toLowerCase()],o===void 0))throw new fe(`Unknown adapter '${m}'`);if(o)break;d[m||"#"+h]=o}if(!o){const h=Object.entries(d).map(([b,g])=>`adapter ${b} `+(g===!1?"is not supported by the environment":"is not available in the build"));let m=r?h.length>1?`since :
`+h.map(fm).join(`
`):" "+fm(h[0]):"as no adapter specified";throw new fe("There is no suitable adapter to dispatch the request "+m,"ERR_NOT_SUPPORT")}return o},adapters:Do};function So(a){if(a.cancelToken&&a.cancelToken.throwIfRequested(),a.signal&&a.signal.aborted)throw new la(null,a)}function hm(a){return So(a),a.headers=wt.from(a.headers),a.data=No.call(a,a.transformRequest),["post","put","patch"].indexOf(a.method)!==-1&&a.headers.setContentType("application/x-www-form-urlencoded",!1),Km.getAdapter(a.adapter||ml.adapter)(a).then(function(o){return So(a),o.data=No.call(a,a.transformResponse,o),o.headers=wt.from(o.headers),o},function(o){return Ym(o)||(So(a),o&&o.response&&(o.response.data=No.call(a,a.transformResponse,o.response),o.response.headers=wt.from(o.response.headers))),Promise.reject(o)})}const Jm="1.11.0",ei={};["object","boolean","number","function","string","symbol"].forEach((a,r)=>{ei[a]=function(o){return typeof o===a||"a"+(r<1?"n ":" ")+a}});const mm={};ei.transitional=function(r,c,o){function d(h,m){return"[Axios v"+Jm+"] Transitional option '"+h+"'"+m+(o?". "+o:"")}return(h,m,b)=>{if(r===!1)throw new fe(d(m," has been removed"+(c?" in "+c:"")),fe.ERR_DEPRECATED);return c&&!mm[m]&&(mm[m]=!0,console.warn(d(m," has been deprecated since v"+c+" and will be removed in the near future"))),r?r(h,m,b):!0}};ei.spelling=function(r){return(c,o)=>(console.warn(`${o} is likely a misspelling of ${r}`),!0)};function vy(a,r,c){if(typeof a!="object")throw new fe("options must be an object",fe.ERR_BAD_OPTION_VALUE);const o=Object.keys(a);let d=o.length;for(;d-- >0;){const h=o[d],m=r[h];if(m){const b=a[h],g=b===void 0||m(b,h,a);if(g!==!0)throw new fe("option "+h+" must be "+g,fe.ERR_BAD_OPTION_VALUE);continue}if(c!==!0)throw new fe("Unknown option "+h,fe.ERR_BAD_OPTION)}}const zr={assertOptions:vy,validators:ei},nn=zr.validators;let ps=class{constructor(r){this.defaults=r||{},this.interceptors={request:new lm,response:new lm}}async request(r,c){try{return await this._request(r,c)}catch(o){if(o instanceof Error){let d={};Error.captureStackTrace?Error.captureStackTrace(d):d=new Error;const h=d.stack?d.stack.replace(/^.+\n/,""):"";try{o.stack?h&&!String(o.stack).endsWith(h.replace(/^.+\n.+\n/,""))&&(o.stack+=`
`+h):o.stack=h}catch{}}throw o}}_request(r,c){typeof r=="string"?(c=c||{},c.url=r):c=r||{},c=xs(this.defaults,c);const{transitional:o,paramsSerializer:d,headers:h}=c;o!==void 0&&zr.assertOptions(o,{silentJSONParsing:nn.transitional(nn.boolean),forcedJSONParsing:nn.transitional(nn.boolean),clarifyTimeoutError:nn.transitional(nn.boolean)},!1),d!=null&&(z.isFunction(d)?c.paramsSerializer={serialize:d}:zr.assertOptions(d,{encode:nn.function,serialize:nn.function},!0)),c.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?c.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:c.allowAbsoluteUrls=!0),zr.assertOptions(c,{baseUrl:nn.spelling("baseURL"),withXsrfToken:nn.spelling("withXSRFToken")},!0),c.method=(c.method||this.defaults.method||"get").toLowerCase();let m=h&&z.merge(h.common,h[c.method]);h&&z.forEach(["delete","get","head","post","put","patch","common"],O=>{delete h[O]}),c.headers=wt.concat(m,h);const b=[];let g=!0;this.interceptors.request.forEach(function(A){typeof A.runWhen=="function"&&A.runWhen(c)===!1||(g=g&&A.synchronous,b.unshift(A.fulfilled,A.rejected))});const x=[];this.interceptors.response.forEach(function(A){x.push(A.fulfilled,A.rejected)});let y,S=0,U;if(!g){const O=[hm.bind(this),void 0];for(O.unshift(...b),O.push(...x),U=O.length,y=Promise.resolve(c);S<U;)y=y.then(O[S++],O[S++]);return y}U=b.length;let B=c;for(S=0;S<U;){const O=b[S++],A=b[S++];try{B=O(B)}catch(T){A.call(this,T);break}}try{y=hm.call(this,B)}catch(O){return Promise.reject(O)}for(S=0,U=x.length;S<U;)y=y.then(x[S++],x[S++]);return y}getUri(r){r=xs(this.defaults,r);const c=Gm(r.baseURL,r.url,r.allowAbsoluteUrls);return qm(c,r.params,r.paramsSerializer)}};z.forEach(["delete","get","head","options"],function(r){ps.prototype[r]=function(c,o){return this.request(xs(o||{},{method:r,url:c,data:(o||{}).data}))}});z.forEach(["post","put","patch"],function(r){function c(o){return function(h,m,b){return this.request(xs(b||{},{method:r,headers:o?{"Content-Type":"multipart/form-data"}:{},url:h,data:m}))}}ps.prototype[r]=c(),ps.prototype[r+"Form"]=c(!0)});let jy=class Fm{constructor(r){if(typeof r!="function")throw new TypeError("executor must be a function.");let c;this.promise=new Promise(function(h){c=h});const o=this;this.promise.then(d=>{if(!o._listeners)return;let h=o._listeners.length;for(;h-- >0;)o._listeners[h](d);o._listeners=null}),this.promise.then=d=>{let h;const m=new Promise(b=>{o.subscribe(b),h=b}).then(d);return m.cancel=function(){o.unsubscribe(h)},m},r(function(h,m,b){o.reason||(o.reason=new la(h,m,b),c(o.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(r){if(this.reason){r(this.reason);return}this._listeners?this._listeners.push(r):this._listeners=[r]}unsubscribe(r){if(!this._listeners)return;const c=this._listeners.indexOf(r);c!==-1&&this._listeners.splice(c,1)}toAbortSignal(){const r=new AbortController,c=o=>{r.abort(o)};return this.subscribe(c),r.signal.unsubscribe=()=>this.unsubscribe(c),r.signal}static source(){let r;return{token:new Fm(function(d){r=d}),cancel:r}}};function Ny(a){return function(c){return a.apply(null,c)}}function Sy(a){return z.isObject(a)&&a.isAxiosError===!0}const Uo={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Uo).forEach(([a,r])=>{Uo[r]=a});function $m(a){const r=new ps(a),c=Em(ps.prototype.request,r);return z.extend(c,ps.prototype,r,{allOwnKeys:!0}),z.extend(c,r,null,{allOwnKeys:!0}),c.create=function(d){return $m(xs(a,d))},c}const We=$m(ml);We.Axios=ps;We.CanceledError=la;We.CancelToken=jy;We.isCancel=Ym;We.VERSION=Jm;We.toFormData=Pr;We.AxiosError=fe;We.Cancel=We.CanceledError;We.all=function(r){return Promise.all(r)};We.spread=Ny;We.isAxiosError=Sy;We.mergeConfig=xs;We.AxiosHeaders=wt;We.formToJSON=a=>Hm(z.isHTMLForm(a)?new FormData(a):a);We.getAdapter=Km.getAdapter;We.HttpStatusCode=Uo;We.default=We;const{Axios:lv,AxiosError:rv,CanceledError:iv,isCancel:cv,CancelToken:ov,VERSION:uv,all:dv,Cancel:fv,isAxiosError:hv,spread:mv,toFormData:pv,AxiosHeaders:xv,HttpStatusCode:bv,formToJSON:gv,getAdapter:yv,mergeConfig:vv}=We,Ve=We.create({baseURL:"http://localhost:5000/api",headers:{"Content-Type":"application/json"}});Ve.interceptors.request.use(a=>{const r=localStorage.getItem("token");return r&&(a.headers.Authorization=`Bearer ${r}`),a});Ve.interceptors.response.use(a=>a,a=>{var r,c,o,d,h,m;return((r=a.response)==null?void 0:r.status)===401&&(console.error("Authentication failed:",(c=a.config)==null?void 0:c.url),localStorage.removeItem("token"),localStorage.removeItem("user"),(!((d=(o=a.config)==null?void 0:o.url)!=null&&d.includes("/evidence/"))||!((m=(h=a.config)==null?void 0:h.url)!=null&&m.includes("/vote")))&&window.location.reload()),Promise.reject(a)});const Rr={register:async a=>(await Ve.post("/auth/register",a)).data,login:async a=>(await Ve.post("/auth/login",a)).data,getProfile:async()=>(await Ve.get("/auth/profile")).data,updateProfile:async a=>(await Ve.put("/auth/profile",a)).data},Xr={getUsers:async a=>(await Ve.get("/users",{params:a})).data,getUserById:async a=>(await Ve.get(`/users/${a}`)).data,getUserDebates:async a=>(await Ve.get(`/users/${a}/debates`)).data},Nt={getDebates:async()=>(await Ve.get("/debates")).data,getDebateById:async a=>(await Ve.get(`/debates/${a}`)).data,createDebate:async a=>(await Ve.post("/debates",a)).data,joinDebate:async a=>(await Ve.post(`/debates/${a}/join`)).data,voteOnDebate:async(a,r)=>(await Ve.post(`/debates/${a}/vote`,{position:r})).data,addArgument:async(a,r)=>(await Ve.post(`/debates/${a}/arguments`,r)).data,voteOnArgument:async(a,r,c)=>(await Ve.post(`/debates/${a}/arguments/${r}/vote`,{vote:c})).data,addSubArgument:async(a,r,c)=>(await Ve.post(`/debates/${a}/arguments/${r}/sub-arguments`,c)).data,addEvidence:async(a,r,c)=>(await Ve.post(`/debates/${a}/arguments/${r}/evidence`,c)).data,voteOnEvidence:async(a,r,c,o)=>(await Ve.post(`/debates/${a}/arguments/${r}/evidence/${c}/vote`,{vote:o})).data,reportContent:async(a,r,c)=>(await Ve.post("/debates/report",{contentId:a,contentType:r,reason:c})).data,getContentReports:async()=>(await Ve.get("/debates/reports")).data,addComment:async(a,r,c)=>(await Ve.post(`/debates/${a}/arguments/${r}/comments`,c)).data,voteOnComment:async(a,r,c,o)=>(await Ve.post(`/debates/${a}/arguments/${r}/comments/${c}/vote`,{vote:o})).data,reportComment:async(a,r,c)=>(await Ve.post(`/debates/${a}/arguments/${r}/comments/${c}/report`)).data},wy={user:null,token:null,isLoading:!1,isAuthenticated:!1,error:null},Ay=(a,r)=>{switch(r.type){case"AUTH_START":return{...a,isLoading:!0,error:null};case"AUTH_SUCCESS":return{...a,user:r.payload.user,token:r.payload.token,isLoading:!1,isAuthenticated:!0,error:null};case"AUTH_FAILURE":return{...a,user:null,token:null,isLoading:!1,isAuthenticated:!1,error:r.payload};case"AUTH_LOGOUT":return{...a,user:null,token:null,isAuthenticated:!1,error:null};case"CLEAR_ERROR":return{...a,error:null};case"UPDATE_USER":return{...a,user:r.payload};default:return a}},Wm=k.createContext(void 0),xt=()=>{const a=k.useContext(Wm);if(!a)throw new Error("useAuth must be used within an AuthProvider");return a},Ey=({children:a})=>{const[r,c]=k.useReducer(Ay,wy);k.useEffect(()=>{const x=localStorage.getItem("token"),y=localStorage.getItem("user");if(x&&y)try{const S=JSON.parse(y);c({type:"AUTH_SUCCESS",payload:{user:S,token:x}}),Rr.getProfile().catch(()=>{localStorage.removeItem("token"),localStorage.removeItem("user"),c({type:"AUTH_LOGOUT"})})}catch{localStorage.removeItem("token"),localStorage.removeItem("user")}},[]);const g={...r,login:async x=>{var y,S;c({type:"AUTH_START"});try{const U=await Rr.login(x);if(U.success){const{user:B,token:O}=U.data;localStorage.setItem("token",O),localStorage.setItem("user",JSON.stringify(B)),c({type:"AUTH_SUCCESS",payload:{user:B,token:O}})}else throw new Error(U.message||"Login failed")}catch(U){const B=((S=(y=U.response)==null?void 0:y.data)==null?void 0:S.message)||U.message||"Login failed";throw c({type:"AUTH_FAILURE",payload:B}),U}},register:async x=>{var y,S;c({type:"AUTH_START"});try{const U=await Rr.register(x);if(U.success){const{user:B,token:O}=U.data;localStorage.setItem("token",O),localStorage.setItem("user",JSON.stringify(B)),c({type:"AUTH_SUCCESS",payload:{user:B,token:O}})}else throw new Error(U.message||"Registration failed")}catch(U){const B=((S=(y=U.response)==null?void 0:y.data)==null?void 0:S.message)||U.message||"Registration failed";throw c({type:"AUTH_FAILURE",payload:B}),U}},logout:()=>{localStorage.removeItem("token"),localStorage.removeItem("user"),c({type:"AUTH_LOGOUT"})},clearError:()=>{c({type:"CLEAR_ERROR"})},updateUser:async x=>{var y,S;try{const U=await Rr.updateProfile(x);if(U.success){const B=U.data.user;localStorage.setItem("user",JSON.stringify(B)),c({type:"UPDATE_USER",payload:B})}}catch(U){const B=((S=(y=U.response)==null?void 0:y.data)==null?void 0:S.message)||U.message||"Update failed";throw c({type:"AUTH_FAILURE",payload:B}),U}}};return l.jsx(Wm.Provider,{value:g,children:a})},Qe={userA:{id:"u1",name:"Eleanor Vance",avatarUrl:"https://picsum.photos/seed/u1/100"},userB:{id:"u2",name:"Marcus Holloway",avatarUrl:"https://picsum.photos/seed/u2/100"},userC:{id:"u3",name:"Anya Sharma",avatarUrl:"https://picsum.photos/seed/u3/100"},userD:{id:"u4",name:"Kenji Tanaka",avatarUrl:"https://picsum.photos/seed/u4/100"}},Ty=[{id:"d1",title:"Is artificial intelligence a net positive for humanity?",description:"A deep-dive into the societal, economic, and ethical implications of advancing AI technology.",claim:{author:Qe.userA,text:"The continued development and integration of artificial intelligence will ultimately yield more benefits than harms, driving unprecedented progress in science, medicine, and human efficiency.",references:[{id:"r1",title:"Global AI Development Index 2023",url:"#"}]},proponent:Qe.userA,opponent:Qe.userB,participants:[Qe.userA,Qe.userB,Qe.userC],arguments:[{id:"a1",author:Qe.userA,text:"AI is accelerating drug discovery and helping to diagnose diseases earlier and more accurately than ever before.",side:Te.FOR,votes:128,upvotes:140,downvotes:12,references:[],evidence:[{id:"e1",type:"study",content:"A recent study by MIT showed that AI-powered drug discovery reduced the time to identify potential compounds by 75%.",source:{id:"r1",title:"MIT AI Drug Discovery Study 2023",url:"https://example.com/mit-study",type:"academic"},verificationStatus:"verified",addedBy:Qe.userA,addedAt:"2024-01-15T10:30:00Z",accurateVotes:8,inaccurateVotes:1,totalVotes:9,verificationScore:88.9,votedBy:[{userId:"user1",vote:"accurate"},{userId:"user2",vote:"accurate"},{userId:"user3",vote:"inaccurate"}]}],comments:[{id:"c1",author:Qe.userB,text:"This is a compelling point about AI in healthcare. Do you have more recent studies?",parentCommentId:void 0,replies:[{id:"c2",author:Qe.userA,text:"Yes, I can share some 2024 studies on AI drug discovery.",parentCommentId:"c1",replies:[],upvotes:3,downvotes:0,votes:3,createdAt:"2024-01-15T09:45:00Z",updatedAt:"2024-01-15T09:45:00Z"}],upvotes:5,downvotes:1,votes:4,createdAt:"2024-01-15T09:30:00Z",updatedAt:"2024-01-15T09:30:00Z"},{id:"c3",author:Qe.userC,text:"The diagnostic accuracy improvements are particularly impressive.",parentCommentId:void 0,replies:[],upvotes:8,downvotes:0,votes:8,createdAt:"2024-01-15T10:30:00Z",updatedAt:"2024-01-15T10:30:00Z"}],subArguments:[{id:"sub1",author:Qe.userC,text:"AI has already helped discover new antibiotics like halicin, which can kill drug-resistant bacteria.",side:Te.FOR,votes:15,upvotes:18,downvotes:3,references:[],evidence:[],parentArgumentId:"a1",comments:[],subArguments:[],depth:1,strengthScore:75,isChallenge:!1,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-15T10:00:00Z"},{id:"sub2",author:Qe.userB,text:"But AI drug discovery still requires extensive human validation and clinical trials, so the time savings may be overstated.",side:Te.AGAINST,votes:8,upvotes:12,downvotes:4,references:[],evidence:[],parentArgumentId:"a1",comments:[],subArguments:[],depth:1,strengthScore:60,isChallenge:!0,createdAt:"2024-01-15T10:15:00Z",updatedAt:"2024-01-15T10:15:00Z"}],depth:0,strengthScore:85,isChallenge:!1,createdAt:"2024-01-15T09:00:00Z",updatedAt:"2024-01-15T10:30:00Z"},{id:"a2",author:Qe.userB,text:"Unchecked AI development risks massive job displacement and could exacerbate economic inequality on a global scale.",side:Te.AGAINST,votes:95,upvotes:105,downvotes:10,references:[{id:"r2",title:"Economic Forum Report on Future of Jobs",url:"#",type:"organization"}],evidence:[{id:"e2",type:"statistic",content:"According to the World Economic Forum, AI could displace 85 million jobs by 2025, while creating only 97 million new ones.",source:{id:"r2",title:"Future of Jobs Report 2023",url:"https://example.com/wef-jobs",type:"organization"},verificationStatus:"pending",addedBy:Qe.userB,addedAt:"2024-01-15T10:45:00Z",accurateVotes:3,inaccurateVotes:2,totalVotes:5,verificationScore:60,votedBy:[{userId:"user4",vote:"accurate"},{userId:"user5",vote:"inaccurate"}]}],comments:[],subArguments:[],depth:0,strengthScore:72,isChallenge:!1,createdAt:"2024-01-15T09:15:00Z",updatedAt:"2024-01-15T09:15:00Z"},{id:"a3",author:Qe.userC,text:"The use of AI in creative fields is not replacing artists but providing them with powerful new tools for expression.",side:Te.FOR,votes:42,upvotes:48,downvotes:6,references:[{id:"r3",title:"Art & Machina: A Survey",url:"#",type:"other"}],evidence:[],comments:[],subArguments:[],depth:0,strengthScore:65,isChallenge:!1,createdAt:"2024-01-15T09:30:00Z",updatedAt:"2024-01-15T09:30:00Z"}],supportVotes:170,opposeVotes:95,userVotes:[],isLive:!0,type:"one-on-one",status:"active",createdAt:"2024-01-15T08:00:00Z",updatedAt:"2024-01-15T10:30:00Z"},{id:"d2",title:"Should space exploration be privatized?",description:"Examining the pros and cons of shifting the primary responsibility for space exploration from government agencies to private corporations.",claim:{author:Qe.userD,text:"The privatization of space exploration accelerates innovation, reduces costs, and opens up the cosmos for humanity in a way that public funding alone cannot.",references:[]},proponent:Qe.userD,opponent:Qe.userC,participants:[Qe.userD,Qe.userC,Qe.userA],arguments:[{id:"b1",author:Qe.userC,text:"Relying on private companies introduces a profit motive that could compromise scientific objectives and safety standards.",side:Te.AGAINST,votes:78,upvotes:85,downvotes:7,references:[],evidence:[],comments:[],subArguments:[],depth:0,strengthScore:68,isChallenge:!1,createdAt:"2024-01-15T11:00:00Z",updatedAt:"2024-01-15T11:00:00Z"},{id:"b2",author:Qe.userA,text:"Private competition has already driven down launch costs significantly, making space more accessible.",side:Te.FOR,votes:150,upvotes:160,downvotes:10,references:[{id:"r4",title:"Analysis of Launch Costs (2010-2024)",url:"#",type:"other"}],evidence:[],comments:[],subArguments:[],depth:0,strengthScore:82,isChallenge:!1,createdAt:"2024-01-15T11:15:00Z",updatedAt:"2024-01-15T11:15:00Z"}],supportVotes:150,opposeVotes:78,userVotes:[],isLive:!1,type:"one-on-one",status:"active",createdAt:"2024-01-15T11:00:00Z",updatedAt:"2024-01-15T11:15:00Z"}],_y=()=>{const[a,r]=k.useState([]),[c,o]=k.useState(null),[d,h]=k.useState(!0),[m,b]=k.useState(null),{isAuthenticated:g}=xt(),x=k.useCallback(async()=>{try{h(!0),b(null);const O=await Nt.getDebates();O.success?r(O.data):b(O.message||"Failed to load debates")}catch(O){b("Failed to load debates"),console.error("Error loading debates:",O),r(Ty)}finally{h(!1)}},[]);k.useEffect(()=>{x()},[x]);const y=k.useCallback(O=>{o(O)},[]),S=k.useCallback(async(O,A,T)=>{if(!g){b("You must be logged in to vote");return}try{if(T){const X=A===Te.FOR?"up":"down",L=await Nt.voteOnArgument(O,T,X);L.success&&r(F=>F.map(ae=>{if(ae.id===O){const P={...ae};return P.arguments=P.arguments.map(K=>K.id===T?{...K,votes:L.data.votes,upvotes:L.data.upvotes,downvotes:L.data.downvotes}:K),P}return ae}))}else{const X=A===Te.FOR?ol.SUPPORT:ol.OPPOSE,L=await Nt.voteOnDebate(O,X);L.success&&r(F=>F.map(ae=>ae.id===O?{...ae,supportVotes:L.data.supportVotes,opposeVotes:L.data.opposeVotes,userVotes:L.data.userVotes||ae.userVotes}:ae))}}catch(X){b("Failed to cast vote"),console.error("Error casting vote:",X)}},[g]),U=k.useCallback(async(O,A)=>{if(!g){b("You must be logged in to vote");return}try{const T=await Nt.voteOnDebate(O,A);T.success&&r(X=>X.map(L=>L.id===O?{...L,supportVotes:T.data.supportVotes,opposeVotes:T.data.opposeVotes,userVotes:T.data.userVotes||L.userVotes}:L))}catch(T){b("Failed to cast vote"),console.error("Error casting vote:",T)}},[g]),B=a.find(O=>O.id===c)||null;return{debates:a,selectedDebate:B,selectDebate:y,castVote:S,castDebateVote:U,loading:d,error:m,refetch:x}},Mt=({participant:a,size:r="md"})=>{const c={xs:"w-6 h-6",sm:"w-8 h-8",md:"w-10 h-10",lg:"w-16 h-16"};return l.jsx("img",{src:a.avatarUrl,alt:a.name,title:a.name,className:`${c[r]} rounded-full object-cover ring-2 ring-brand-surface-light`})},Qr=({debate:a,onSelect:r})=>l.jsxs("div",{className:"bg-brand-surface rounded-lg shadow-lg p-6 transition-all duration-300 hover:shadow-brand-primary/20 hover:ring-1 hover:ring-brand-primary/50",children:[l.jsxs("div",{className:"flex justify-between items-start",children:[l.jsx("h2",{className:"text-xl font-bold text-brand-text mb-2 max-w-md",children:a.title}),a.isLive&&l.jsxs("span",{className:"flex items-center gap-2 text-sm font-semibold bg-danger/20 text-danger px-3 py-1 rounded-full",children:[l.jsxs("span",{className:"relative flex h-2 w-2",children:[l.jsx("span",{className:"animate-ping absolute inline-flex h-full w-full rounded-full bg-danger opacity-75"}),l.jsx("span",{className:"relative inline-flex rounded-full h-2 w-2 bg-danger"})]}),"LIVE"]})]}),l.jsx("p",{className:"text-brand-text-light mb-4 text-sm",children:a.description}),l.jsxs("div",{className:"flex items-center justify-between mb-6",children:[l.jsxs("div",{className:"flex items-center",children:[a.participants.slice(0,4).map((c,o)=>l.jsx("div",{className:"-ml-3",style:{zIndex:4-o},children:l.jsx(Mt,{participant:c})},c.id)),a.participants.length>4&&l.jsxs("div",{className:"-ml-3 flex items-center justify-center w-10 h-10 rounded-full bg-brand-surface-light text-brand-text-light text-xs font-bold ring-2 ring-brand-surface",children:["+",a.participants.length-4]})]}),l.jsxs("div",{className:"flex gap-4 font-mono text-sm",children:[l.jsxs("span",{className:"text-success",children:["SUPPORT: ",a.supportVotes||0]}),l.jsxs("span",{className:"text-danger",children:["OPPOSE: ",a.opposeVotes||0]})]})]}),l.jsx("button",{onClick:()=>r(a.id),className:"w-full bg-brand-primary text-white font-bold py-2 px-4 rounded-lg hover:bg-brand-primary-hover transition-colors",children:"View Debate"})]}),Ry=({className:a})=>l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:a,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"})}),Cy=({className:a})=>l.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",className:a,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,children:[l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"}),l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 5l14 14"})]}),bs=({className:a})=>l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:a,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"})}),Zr=({className:a})=>l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:a,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),Oy=({className:a})=>l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:a,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 2H21l-3 6 3 6h-8.5l-1-2H5a2 2 0 00-2 2zm9-13.5V9"})}),Pm=({className:a})=>l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:a,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),Dy=({className:a})=>l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:a,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13 10V3L4 14h7v7l9-11h-7z"})}),pm=({className:a})=>l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:a,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19 9l-7 7-7-7"})}),Mo=({className:a})=>l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:a,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 5l7 7-7 7"})}),Fn=({className:a})=>l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:a,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4v16m8-8H4"})}),Kr=({className:a})=>l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:a,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18L18 6M6 6l12 12"})}),xm=({message:a,type:r,className:c=""})=>{const o=()=>{switch(r){case"error":return"bg-red-900/20 border-red-500/30 text-red-300";case"warning":return"bg-yellow-900/20 border-yellow-500/30 text-yellow-300";case"info":return"bg-blue-900/20 border-blue-500/30 text-blue-300";default:return"bg-gray-900/20 border-gray-500/30 text-gray-300"}},d=()=>{switch(r){case"error":return"⚠️";case"warning":return"⚡";case"info":return"ℹ️";default:return"📝"}};return l.jsx("div",{className:`border rounded-lg p-3 mb-3 ${o()} ${c}`,children:l.jsxs("div",{className:"flex items-start gap-2",children:[l.jsx("span",{className:"text-lg",children:d()}),l.jsx("div",{className:"flex-1",children:l.jsx("p",{className:"text-sm font-medium",children:a})})]})})},zo=({errors:a,warnings:r,className:c=""})=>a.length===0&&r.length===0?null:l.jsxs("div",{className:`space-y-2 ${c}`,children:[a.map((o,d)=>l.jsx(xm,{message:o,type:"error"},`error-${d}`)),r.map((o,d)=>l.jsx(xm,{message:o,type:"warning"},`warning-${d}`))]}),Bo=({className:a=""})=>l.jsxs("div",{className:`bg-brand-bg/30 rounded-lg p-4 ${a}`,children:[l.jsxs("h4",{className:"font-semibold text-brand-text mb-3 flex items-center gap-2",children:[l.jsx("span",{children:"📋"}),"Quality Guidelines"]}),l.jsxs("div",{className:"space-y-2 text-sm text-brand-text-secondary",children:[l.jsxs("div",{children:[l.jsx("strong",{className:"text-brand-text",children:"Arguments:"}),l.jsxs("ul",{className:"list-disc list-inside ml-2 mt-1 space-y-1",children:[l.jsx("li",{children:"Be respectful and constructive"}),l.jsx("li",{children:"Provide clear, logical reasoning"}),l.jsx("li",{children:"Support claims with evidence when possible"}),l.jsx("li",{children:"Avoid repetitive or inflammatory language"})]})]}),l.jsxs("div",{children:[l.jsx("strong",{className:"text-brand-text",children:"Evidence:"}),l.jsxs("ul",{className:"list-disc list-inside ml-2 mt-1 space-y-1",children:[l.jsx("li",{children:"Use credible, verifiable sources"}),l.jsx("li",{children:"Provide direct links (no URL shorteners)"}),l.jsx("li",{children:"Match evidence type to content"}),l.jsx("li",{children:"Include sufficient context and explanation"})]})]})]})]}),Im=()=>({validateArgument:c=>{const o=[],d=[];if(!c||c.trim().length===0)return o.push("Argument text is required"),{errors:o,warnings:d};const h=c.trim(),m=h.split(/\s+/).length;m<5&&o.push("Arguments must be at least 5 words long"),(c.match(/[A-Z]/g)||[]).length/c.length>.5&&c.length>20&&d.push("Consider using normal case instead of excessive capitalization");const g=h.toLowerCase().split(/\s+/);return new Set(g).size/g.length<.3&&g.length>10&&d.push("Your argument appears repetitive. Consider adding more varied content"),m<10&&d.push("Consider expanding your argument with more detail or reasoning"),{errors:o,warnings:d}},validateEvidence:(c,o,d)=>{const h=[],m=[];if((!c||c.trim().length===0)&&h.push("Evidence content is required"),!o||o.trim().length===0)h.push("Source URL is required");else try{const g=new URL(o);["bit.ly","tinyurl.com","goo.gl","t.co"].some(y=>g.hostname.includes(y))&&m.push("Consider using direct links instead of URL shorteners for better transparency")}catch{h.push("Please provide a valid URL")}return(!d||d.trim().length===0)&&h.push("Source title is required"),c.trim().split(/\s+/).length<5&&m.push("Consider providing more detailed evidence description"),{errors:h,warnings:m}}}),Uy=({evidence:a,onVoteOnEvidence:r,currentUserId:c})=>{const o=g=>{switch(g){case"statistic":return"📊";case"study":return"🔬";case"expert_opinion":return"👨‍🎓";case"case_study":return"📋";case"historical_fact":return"📚";default:return"📄"}},d=g=>{switch(g){case"verified":return"text-green-400";case"pending":return"text-yellow-400";case"disputed":return"text-red-400";default:return"text-gray-400"}},h=c&&a.votedBy?a.votedBy.find(g=>g.userId===c):null,m=!!h,b=r&&c;return l.jsxs("div",{className:"bg-brand-bg/30 rounded-lg p-3 border-l-2 border-brand-primary/30",children:[l.jsxs("div",{className:"flex items-start justify-between mb-2",children:[l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("span",{className:"text-lg",children:o(a.type)}),l.jsx("span",{className:"text-sm font-medium text-brand-text capitalize",children:a.type.replace("_"," ")})]}),l.jsx("span",{className:`text-xs font-medium ${d(a.verificationStatus)}`,children:a.verificationStatus})]}),l.jsx("p",{className:"text-sm text-brand-text-light mb-2",children:a.content}),l.jsxs("div",{className:"flex items-center justify-between mb-2",children:[l.jsxs("a",{href:a.source.url,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-brand-primary/80 hover:text-brand-primary text-xs transition-colors",children:[l.jsx(bs,{className:"w-3 h-3"}),l.jsx("span",{children:a.source.title})]}),l.jsxs("div",{className:"flex items-center gap-2 text-xs text-brand-text-secondary",children:[l.jsx(Mt,{participant:a.addedBy,size:"xs"}),l.jsx("span",{children:a.addedBy.name})]})]}),l.jsx("div",{className:"flex items-center justify-between pt-2 border-t border-brand-primary/10",children:l.jsxs("div",{className:"flex items-center gap-3",children:[r&&c&&l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsxs("button",{onClick:g=>{g.preventDefault(),g.stopPropagation(),r(a.id,"accurate")},disabled:!b,className:`flex items-center gap-1 px-2 py-1 rounded text-xs transition-colors ${(h==null?void 0:h.vote)==="accurate"?"bg-green-500/30 text-green-300 border border-green-500/50":b?"bg-green-500/10 text-green-400 hover:bg-green-500/20":"bg-gray-500/10 text-gray-500 cursor-not-allowed"}`,title:(h==null?void 0:h.vote)==="accurate"?"You voted accurate (click to change)":"Vote as accurate",type:"button",children:[l.jsx("span",{children:"✓"}),l.jsx("span",{children:a.accurateVotes||0})]}),l.jsxs("button",{onClick:g=>{g.preventDefault(),g.stopPropagation(),r(a.id,"inaccurate")},disabled:!b,className:`flex items-center gap-1 px-2 py-1 rounded text-xs transition-colors ${(h==null?void 0:h.vote)==="inaccurate"?"bg-red-500/30 text-red-300 border border-red-500/50":b?"bg-red-500/10 text-red-400 hover:bg-red-500/20":"bg-gray-500/10 text-gray-500 cursor-not-allowed"}`,title:(h==null?void 0:h.vote)==="inaccurate"?"You voted inaccurate (click to change)":"Vote as inaccurate",type:"button",children:[l.jsx("span",{children:"✗"}),l.jsx("span",{children:a.inaccurateVotes||0})]})]}),a.totalVotes>0&&l.jsxs("div",{className:"text-xs text-brand-text-secondary",children:[l.jsxs("span",{className:"font-medium",children:[Math.round(a.verificationScore||0),"%"]})," accurate",l.jsxs("span",{className:"ml-1",children:["(",a.totalVotes," vote",a.totalVotes!==1?"s":"",")"]})]}),m&&l.jsxs("div",{className:"text-xs text-brand-text-secondary",children:["You voted: ",l.jsx("span",{className:(h==null?void 0:h.vote)==="accurate"?"text-green-400":"text-red-400",children:h==null?void 0:h.vote})," ",l.jsx("span",{className:"text-gray-500",children:"(click to change)"})]})]})})]})},bm=({argument:a,onVote:r,onAddSubArgument:c,onAddEvidence:o,onVoteOnEvidence:d,onArgumentClick:h,onChatClick:m,currentUserId:b,depth:g=0,maxDepth:x=3})=>{const[y,S]=k.useState(g<2),[U,B]=k.useState(!1),[O,A]=k.useState(!1),[T,X]=k.useState(!1),[L,F]=k.useState(!1),[ae,P]=k.useState(!1),[K,le]=k.useState(""),[Q,$]=k.useState(!1),[J,xe]=k.useState({type:"other",content:"",sourceTitle:"",sourceUrl:""}),{validateArgument:de,validateEvidence:Ce}=Im(),Ge=a.side===Te.FOR,qe=Ge?"border-green-500/50":"border-red-500/50",N=a.isChallenge?"border-orange-500/50":qe,G=g>0?`ml-${Math.min(g*4,12)}`:"",te=a.subArguments&&a.subArguments.length>0,he=a.evidence&&a.evidence.length>0,j=()=>{K.trim()&&(c(a.id,K.trim(),Q),le(""),$(!1),B(!1))},Y=()=>{if(J.content.trim()&&J.sourceTitle.trim()&&J.sourceUrl.trim()){const R={type:J.type,content:J.content.trim(),source:{id:`ref-${Date.now()}`,title:J.sourceTitle.trim(),url:J.sourceUrl.trim(),type:"other"}};o(a.id,R),xe({type:"other",content:"",sourceTitle:"",sourceUrl:""}),A(!1)}},ee=R=>R>=80?"text-green-400":R>=60?"text-yellow-400":R>=40?"text-orange-400":"text-red-400",W=()=>{const R=Math.max(0,a.upvotes-a.downvotes),Z=a.upvotes+a.downvotes,I=Z>0?a.upvotes/Z:.5,Pe=Math.log(R+1)*2+(I-.5)*10;let De=0;a.evidence&&a.evidence.length>0&&(De=a.evidence.reduce((ye,Ee)=>{let be=1;switch(Ee.verificationStatus){case"verified":be=1.2;break;case"pending":be=1;break;case"disputed":be=.5;break;case"unverified":be=.8;break}return ye+5*be},0)/a.evidence.length);const ne=a.references&&a.references.length>0?Math.min(5,a.references.length*1.5):0;let Se=0;if(a.subArguments&&a.subArguments.length>0){const oe=a.subArguments.filter(Oe=>!Oe.isChallenge),ye=a.subArguments.filter(Oe=>Oe.isChallenge),Ee=oe.length>0?oe.reduce((Oe,zt)=>Oe+zt.strengthScore,0)/oe.length:0,be=ye.length>0?ye.reduce((Oe,zt)=>Oe+zt.strengthScore,0)/ye.length:0,Ye=Ee*Math.min(1,oe.length*.3)*.6,me=be*Math.min(1,ye.length*.25)*.4;Se=Ye-me}return{baseStrength:30,voteContribution:Math.round(Pe*.3),evidenceContribution:Math.round(De*4),subArgumentContribution:Math.round(Se),referenceBonus:Math.round(ne),total:a.strengthScore}};return l.jsxs("div",{className:`${G} mb-4`,children:[l.jsxs("div",{className:`bg-brand-surface rounded-lg p-4 border-l-4 ${N} shadow-md relative`,children:[l.jsxs("div",{className:"flex items-center justify-between mb-3",children:[l.jsxs("div",{className:"flex items-center gap-3",children:[l.jsx(Mt,{participant:a.author,size:"sm"}),l.jsx("span",{className:"font-semibold text-brand-text",children:a.author.name}),a.isChallenge&&l.jsx("span",{className:"text-xs bg-orange-500/20 text-orange-400 px-2 py-1 rounded-full",children:"Challenge"})]}),l.jsxs("div",{className:"flex items-center gap-4",children:[l.jsx("span",{className:`font-bold text-sm ${Ge?"text-green-400":"text-red-400"}`,children:Ge?"FOR":"AGAINST"}),l.jsx("div",{className:"flex items-center gap-2",children:(()=>{const R=b&&a.userVotes?a.userVotes.find(Z=>Z.userId===b):null;return l.jsxs(l.Fragment,{children:[l.jsx("button",{onClick:()=>r(a.id,"up"),className:`transition-colors w-6 h-6 flex items-center justify-center rounded ${(R==null?void 0:R.vote)==="up"?"text-green-300 bg-green-500/20":"text-green-400 hover:text-green-300"}`,title:(R==null?void 0:R.vote)==="up"?"You voted up (click to remove)":"Vote up",children:"▲"}),l.jsx("span",{className:"font-mono text-sm text-brand-text",children:a.upvotes-a.downvotes}),l.jsx("button",{onClick:()=>r(a.id,"down"),className:`transition-colors w-6 h-6 flex items-center justify-center rounded ${(R==null?void 0:R.vote)==="down"?"text-red-300 bg-red-500/20":"text-red-400 hover:text-red-300"}`,title:(R==null?void 0:R.vote)==="down"?"You voted down (click to remove)":"Vote down",children:"▼"})]})})()}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("span",{className:"text-xs text-brand-text-secondary",children:"Strength:"}),l.jsxs("button",{onClick:()=>X(!T),className:`font-bold text-sm ${ee(a.strengthScore)} hover:opacity-80 transition-opacity`,title:"Click to see strength breakdown",children:[Math.round(a.strengthScore),"%"]})]}),m&&l.jsx("button",{onClick:()=>m(a),className:"text-brand-text-secondary hover:text-brand-primary transition-colors p-1 rounded",title:"Open discussion thread",children:l.jsx(Zr,{className:"w-4 h-4"})})]})]}),l.jsx("p",{className:`text-brand-text-light mb-4 ${h?"cursor-pointer hover:text-brand-text transition-colors":""}`,onClick:()=>h==null?void 0:h(a),children:a.text}),T&&l.jsxs("div",{className:"bg-brand-bg/30 rounded-lg p-3 mb-3 text-sm",children:[l.jsx("h4",{className:"font-semibold text-brand-text mb-2",children:"Strength Score Breakdown"}),(()=>{var Z,I,Pe;const R=W();return l.jsxs("div",{className:"space-y-1",children:[l.jsxs("div",{className:"flex justify-between",children:[l.jsx("span",{className:"text-brand-text-secondary",children:"Base Strength:"}),l.jsx("span",{className:"text-brand-text",children:R.baseStrength})]}),l.jsxs("div",{className:"flex justify-between",children:[l.jsxs("span",{className:"text-brand-text-secondary",children:["Votes (",a.upvotes,"↑ ",a.downvotes,"↓):"]}),l.jsxs("span",{className:"text-brand-text",children:["+",R.voteContribution]})]}),l.jsxs("div",{className:"flex justify-between",children:[l.jsxs("span",{className:"text-brand-text-secondary",children:["Evidence (",((Z=a.evidence)==null?void 0:Z.length)||0,"):"]}),l.jsxs("span",{className:"text-brand-text",children:["+",R.evidenceContribution]})]}),l.jsxs("div",{className:"flex justify-between",children:[l.jsxs("span",{className:"text-brand-text-secondary",children:["Sub-Arguments (",((I=a.subArguments)==null?void 0:I.length)||0,"):"]}),l.jsxs("span",{className:R.subArgumentContribution>=0?"text-green-400":"text-red-400",children:[R.subArgumentContribution>=0?"+":"",R.subArgumentContribution]})]}),l.jsxs("div",{className:"flex justify-between",children:[l.jsxs("span",{className:"text-brand-text-secondary",children:["References (",((Pe=a.references)==null?void 0:Pe.length)||0,"):"]}),l.jsxs("span",{className:"text-brand-text",children:["+",R.referenceBonus]})]}),l.jsx("div",{className:"border-t border-brand-surface-light pt-1 mt-2",children:l.jsxs("div",{className:"flex justify-between font-semibold",children:[l.jsx("span",{className:"text-brand-text",children:"Total Strength:"}),l.jsxs("span",{className:ee(R.total),children:[R.total,"%"]})]})})]})})()]}),a.references.length>0&&l.jsxs("div",{className:"mb-4",children:[l.jsx("h4",{className:"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2",children:"References"}),l.jsx("ul",{className:"space-y-1",children:a.references.map(R=>l.jsx("li",{children:l.jsxs("a",{href:R.url,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2 text-brand-primary/80 hover:text-brand-primary text-sm transition-colors",children:[l.jsx(bs,{className:"w-4 h-4"}),l.jsx("span",{children:R.title}),R.type!=="other"&&l.jsxs("span",{className:"text-xs text-brand-text-secondary",children:["(",R.type,")"]})]})},R.id))})]}),he&&l.jsxs("div",{className:"mb-4",children:[l.jsxs("button",{onClick:()=>P(!ae),className:"flex items-center gap-2 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2 hover:text-gray-300 transition-colors",children:[ae?l.jsx(pm,{className:"w-3 h-3"}):l.jsx(Mo,{className:"w-3 h-3"}),"Evidence (",a.evidence.length,")"]}),ae?l.jsx("div",{className:"space-y-2",children:a.evidence.map(R=>l.jsx(Uy,{evidence:R,onVoteOnEvidence:d?(Z,I)=>d(a.id,Z,I):void 0,currentUserId:b},R.id))}):l.jsxs("div",{className:"text-xs text-brand-text-secondary pl-5",children:[a.evidence.slice(0,2).map((R,Z)=>l.jsxs("div",{className:"flex items-start gap-2 mb-2",children:[l.jsx("span",{className:"flex-shrink-0 mt-0.5",children:R.type==="statistic"?"📊":R.type==="study"?"🔬":R.type==="expert_opinion"?"👨‍🎓":R.type==="case_study"?"📋":R.type==="historical_fact"?"📚":"📄"}),l.jsx("span",{className:"flex-1 truncate whitespace-nowrap overflow-hidden",children:R.content})]},R.id)),a.evidence.length>2&&l.jsxs("div",{className:"text-brand-text-secondary/70 italic",children:["+",a.evidence.length-2," more..."]})]})]}),l.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[l.jsxs("button",{onClick:()=>B(!U),className:"flex items-center gap-1 text-brand-primary hover:text-brand-primary-light text-sm transition-colors",disabled:g>=x,children:[l.jsx(Fn,{className:"w-4 h-4"}),"Add Sub-Argument"]}),l.jsxs("button",{onClick:()=>A(!O),className:"flex items-center gap-1 text-brand-secondary hover:text-brand-secondary-light text-sm transition-colors",children:[l.jsx(Fn,{className:"w-4 h-4"}),"Add Evidence"]}),te&&l.jsxs("button",{onClick:()=>S(!y),className:"flex items-center gap-1 text-brand-text-secondary hover:text-white text-sm transition-colors",children:[y?l.jsx(pm,{className:"w-4 h-4"}):l.jsx(Mo,{className:"w-4 h-4"}),a.subArguments.length," Sub-Arguments"]})]}),U&&l.jsx("div",{className:"bg-brand-bg/50 rounded-lg p-3 mb-4",children:(()=>{const R=de(K);return l.jsxs(l.Fragment,{children:[l.jsx(zo,{errors:R.errors,warnings:R.warnings}),l.jsx("textarea",{value:K,onChange:Z=>le(Z.target.value),placeholder:"Enter your sub-argument...",className:"w-full bg-brand-surface border border-brand-surface-light rounded-lg p-3 text-brand-text resize-none",rows:3}),l.jsxs("div",{className:"flex items-center justify-between mt-2",children:[l.jsxs("div",{className:"flex items-center gap-4",children:[l.jsxs("label",{className:"flex items-center gap-2 text-sm text-brand-text",children:[l.jsx("input",{type:"checkbox",checked:Q,onChange:Z=>$(Z.target.checked),className:"rounded"}),"This challenges the parent argument"]}),l.jsx("button",{onClick:()=>F(!L),className:"text-xs text-brand-text-secondary hover:text-brand-text transition-colors",children:"📋 Guidelines"})]}),l.jsxs("div",{className:"flex gap-2",children:[l.jsx("button",{onClick:()=>B(!1),className:"px-3 py-1 text-sm text-brand-text-secondary hover:text-white transition-colors",children:"Cancel"}),l.jsx("button",{onClick:j,disabled:!K.trim()||R.errors.length>0,className:"px-3 py-1 bg-brand-primary hover:bg-brand-primary-dark text-white text-sm rounded transition-colors disabled:opacity-50",children:"Add"})]})]}),L&&l.jsx(Bo,{className:"mt-3"})]})})()}),O&&l.jsx("div",{className:"bg-brand-bg/50 rounded-lg p-3 mb-4",children:(()=>{const R=Ce(J.content,J.sourceUrl,J.sourceTitle);return l.jsxs(l.Fragment,{children:[l.jsx(zo,{errors:R.errors,warnings:R.warnings}),l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text mb-1",children:"Evidence Type"}),l.jsxs("select",{value:J.type,onChange:Z=>xe(I=>({...I,type:Z.target.value})),className:"w-full bg-brand-surface border border-brand-surface-light rounded-lg p-2 text-brand-text",children:[l.jsx("option",{value:"statistic",children:"Statistic"}),l.jsx("option",{value:"study",children:"Study"}),l.jsx("option",{value:"expert_opinion",children:"Expert Opinion"}),l.jsx("option",{value:"case_study",children:"Case Study"}),l.jsx("option",{value:"historical_fact",children:"Historical Fact"}),l.jsx("option",{value:"other",children:"Other"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text mb-1",children:"Evidence Content"}),l.jsx("textarea",{value:J.content,onChange:Z=>xe(I=>({...I,content:Z.target.value})),placeholder:"Describe the evidence...",className:"w-full bg-brand-surface border border-brand-surface-light rounded-lg p-3 text-brand-text resize-none",rows:3})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text mb-1",children:"Source Title"}),l.jsx("input",{type:"text",value:J.sourceTitle,onChange:Z=>xe(I=>({...I,sourceTitle:Z.target.value})),placeholder:"Source title...",className:"w-full bg-brand-surface border border-brand-surface-light rounded-lg p-2 text-brand-text"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text mb-1",children:"Source URL"}),l.jsx("input",{type:"url",value:J.sourceUrl,onChange:Z=>xe(I=>({...I,sourceUrl:Z.target.value})),placeholder:"https://...",className:"w-full bg-brand-surface border border-brand-surface-light rounded-lg p-2 text-brand-text"})]})]})]}),l.jsxs("div",{className:"flex justify-between items-center mt-4",children:[l.jsx("button",{onClick:()=>F(!L),className:"text-xs text-brand-text-secondary hover:text-brand-text transition-colors",children:"📋 Guidelines"}),l.jsxs("div",{className:"flex gap-2",children:[l.jsx("button",{onClick:()=>A(!1),className:"px-3 py-1 text-sm text-brand-text-secondary hover:text-white transition-colors",children:"Cancel"}),l.jsx("button",{onClick:Y,disabled:!J.content.trim()||!J.sourceTitle.trim()||!J.sourceUrl.trim()||R.errors.length>0,className:"px-3 py-1 bg-brand-secondary hover:bg-brand-secondary-dark text-white text-sm rounded transition-colors disabled:opacity-50",children:"Add Evidence"})]})]}),L&&l.jsx(Bo,{className:"mt-3"})]})})()})]}),y&&te&&g===0&&l.jsx("div",{className:"mt-4",children:l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 items-stretch",children:[l.jsxs("div",{className:"flex flex-col h-full",children:[l.jsxs("h5",{className:"text-sm font-medium text-success mb-2 flex items-center gap-2",children:[l.jsx("span",{className:"w-2 h-2 bg-success rounded-full"}),"Supporting (",a.subArguments.filter(R=>R.side===Te.FOR).length,")"]}),l.jsxs("div",{className:"flex-1 flex flex-col gap-2",children:[a.subArguments.filter(R=>R.side===Te.FOR).sort((R,Z)=>(Z.upvotes||0)-(Z.downvotes||0)-((R.upvotes||0)-(R.downvotes||0))).map(R=>l.jsxs("div",{className:"border border-brand-border rounded-lg p-3 hover:border-success/50 transition-colors bg-brand-bg hover:bg-success/5 flex flex-col h-full",children:[l.jsxs("div",{className:"flex items-start justify-between mb-1",children:[l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(Mt,{participant:R.author,size:"sm"}),l.jsx("span",{className:"text-sm font-medium text-brand-text",children:R.author.name})]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("div",{className:"flex items-center gap-1",children:(()=>{const Z=b&&R.userVotes?R.userVotes.find(I=>I.userId===b):null;return l.jsxs(l.Fragment,{children:[l.jsx("button",{onClick:I=>{I.stopPropagation(),r(R.id,"up")},className:`transition-colors text-sm w-5 h-5 flex items-center justify-center rounded ${(Z==null?void 0:Z.vote)==="up"?"text-green-300 bg-green-500/20":"text-green-400 hover:text-green-300"}`,title:(Z==null?void 0:Z.vote)==="up"?"You voted up (click to remove)":"Vote up",children:"▲"}),l.jsx("span",{className:"text-sm font-medium text-brand-text",children:(R.upvotes||0)-(R.downvotes||0)}),l.jsx("button",{onClick:I=>{I.stopPropagation(),r(R.id,"down")},className:`transition-colors text-sm w-5 h-5 flex items-center justify-center rounded ${(Z==null?void 0:Z.vote)==="down"?"text-red-300 bg-red-500/20":"text-red-400 hover:text-red-300"}`,title:(Z==null?void 0:Z.vote)==="down"?"You voted down (click to remove)":"Vote down",children:"▼"})]})})()}),l.jsx("button",{onClick:()=>h==null?void 0:h(R),className:"text-xs text-brand-text-secondary hover:text-brand-text transition-colors px-2 py-1 rounded",children:"View"})]})]}),l.jsx("p",{className:"text-sm text-brand-text-light flex-1",children:R.text}),R.subArguments&&R.subArguments.length>0&&l.jsxs("div",{className:"text-xs text-brand-text-secondary",children:[R.subArguments.length," sub-argument",R.subArguments.length!==1?"s":""]})]},R.id)),a.subArguments.filter(R=>R.side===Te.FOR).length===0&&l.jsx("div",{className:"text-center py-4 text-brand-text-light border border-dashed border-brand-border rounded-lg text-sm flex items-center justify-center h-full",children:"No supporting arguments yet"})]})]}),l.jsxs("div",{className:"flex flex-col h-full",children:[l.jsxs("h5",{className:"text-sm font-medium text-danger mb-2 flex items-center gap-2",children:[l.jsx("span",{className:"w-2 h-2 bg-danger rounded-full"}),"Challenging (",a.subArguments.filter(R=>R.side===Te.AGAINST).length,")"]}),l.jsxs("div",{className:"flex-1 flex flex-col gap-2",children:[a.subArguments.filter(R=>R.side===Te.AGAINST).sort((R,Z)=>(Z.upvotes||0)-(Z.downvotes||0)-((R.upvotes||0)-(R.downvotes||0))).map(R=>l.jsxs("div",{className:"border border-brand-border rounded-lg p-3 hover:border-danger/50 transition-colors bg-brand-bg hover:bg-danger/5 flex flex-col h-full",children:[l.jsxs("div",{className:"flex items-start justify-between mb-1",children:[l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(Mt,{participant:R.author,size:"sm"}),l.jsx("span",{className:"text-sm font-medium text-brand-text",children:R.author.name})]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("div",{className:"flex items-center gap-1",children:(()=>{const Z=b&&R.userVotes?R.userVotes.find(I=>I.userId===b):null;return l.jsxs(l.Fragment,{children:[l.jsx("button",{onClick:I=>{I.stopPropagation(),r(R.id,"up")},className:`transition-colors text-sm w-5 h-5 flex items-center justify-center rounded ${(Z==null?void 0:Z.vote)==="up"?"text-green-300 bg-green-500/20":"text-green-400 hover:text-green-300"}`,title:(Z==null?void 0:Z.vote)==="up"?"You voted up (click to remove)":"Vote up",children:"▲"}),l.jsx("span",{className:"text-sm font-medium text-brand-text",children:(R.upvotes||0)-(R.downvotes||0)}),l.jsx("button",{onClick:I=>{I.stopPropagation(),r(R.id,"down")},className:`transition-colors text-sm w-5 h-5 flex items-center justify-center rounded ${(Z==null?void 0:Z.vote)==="down"?"text-red-300 bg-red-500/20":"text-red-400 hover:text-red-300"}`,title:(Z==null?void 0:Z.vote)==="down"?"You voted down (click to remove)":"Vote down",children:"▼"})]})})()}),l.jsx("button",{onClick:()=>h==null?void 0:h(R),className:"text-xs text-brand-text-secondary hover:text-brand-text transition-colors px-2 py-1 rounded",children:"View"})]})]}),l.jsx("p",{className:"text-sm text-brand-text-light flex-1",children:R.text}),R.subArguments&&R.subArguments.length>0&&l.jsxs("div",{className:"text-xs text-brand-text-secondary",children:[R.subArguments.length," sub-argument",R.subArguments.length!==1?"s":""]})]},R.id)),a.subArguments.filter(R=>R.side===Te.AGAINST).length===0&&l.jsx("div",{className:"text-center py-4 text-brand-text-light border border-dashed border-brand-border rounded-lg text-sm flex items-center justify-center h-full",children:"No challenging arguments yet"})]})]})]})})]})},ko=({side:a,onSubmit:r,onCancel:c,isSubmitting:o=!1,allowSideSelection:d=!1})=>{const[h,m]=k.useState(""),[b,g]=k.useState(a),[x,y]=k.useState([]),[S,U]=k.useState(!1),[B,O]=k.useState(!1),[A,T]=k.useState({title:"",url:"",description:""}),X=h.trim().length>=10,L=h.trim().split(/\s+/).length,F=L>=5,ae=()=>{if(A.title.trim()&&A.url.trim()){const $={id:Date.now().toString(),title:A.title.trim(),url:A.url.trim(),description:A.description.trim()||void 0,type:"other"};y([...x,$]),T({title:"",url:"",description:""}),U(!1)}},P=$=>{y(x.filter(J=>J.id!==$))},K=async()=>{if(!(!X||!F))try{await r(h.trim(),b,x),m(""),y([]),c()}catch($){console.error("Error submitting argument:",$)}},le=b===Te.FOR?"success":"danger",Q=b===Te.FOR?"FOR":"AGAINST";return l.jsxs("div",{className:"bg-brand-surface rounded-lg p-6 border-l-4 border-brand-primary",children:[l.jsxs("div",{className:"flex items-center justify-between mb-4",children:[l.jsxs("h4",{className:`text-lg font-semibold text-${le}`,children:["Add Argument ",Q]}),l.jsx("button",{onClick:c,className:"text-brand-text-secondary hover:text-white transition-colors",children:l.jsx(Kr,{className:"w-5 h-5"})})]}),l.jsxs("div",{className:"space-y-4",children:[d&&l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text-light mb-2",children:"Position"}),l.jsxs("div",{className:"flex gap-3",children:[l.jsx("button",{type:"button",onClick:()=>g(Te.FOR),className:`flex-1 py-2 px-4 rounded-lg border transition-colors ${b===Te.FOR?"bg-success/20 border-success text-success":"bg-brand-bg border-brand-border text-brand-text-light hover:border-success/50"}`,children:"FOR (Supporting)"}),l.jsx("button",{type:"button",onClick:()=>g(Te.AGAINST),className:`flex-1 py-2 px-4 rounded-lg border transition-colors ${b===Te.AGAINST?"bg-danger/20 border-danger text-danger":"bg-brand-bg border-brand-border text-brand-text-light hover:border-danger/50"}`,children:"AGAINST (Challenging)"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text-light mb-2",children:"Your Argument"}),l.jsx("textarea",{value:h,onChange:$=>m($.target.value),placeholder:`Enter your argument ${Q.toLowerCase()} this position...`,className:"w-full p-3 bg-brand-bg border border-brand-border rounded-lg text-white placeholder-brand-text-secondary focus:border-brand-primary focus:outline-none resize-none",rows:4,maxLength:1e3}),l.jsxs("div",{className:"flex justify-between items-center mt-1",children:[l.jsxs("span",{className:"text-xs text-brand-text-secondary",children:[h.length,"/1000 characters • ",L," words"]}),!F&&h.length>0&&l.jsx("span",{className:"text-xs text-danger",children:"Minimum 5 words required"})]})]}),l.jsxs("div",{children:[l.jsxs("div",{className:"flex items-center justify-between mb-2",children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text-light",children:"Supporting References (Optional)"}),l.jsxs("button",{onClick:()=>U(!S),className:"flex items-center gap-1 text-brand-secondary hover:text-brand-secondary-light text-sm transition-colors",children:[l.jsx(Fn,{className:"w-4 h-4"}),"Add Reference"]})]}),x.length>0&&l.jsx("div",{className:"space-y-2 mb-3",children:x.map($=>l.jsxs("div",{className:"flex items-center gap-2 p-2 bg-brand-bg rounded border",children:[l.jsx(bs,{className:"w-4 h-4 text-brand-secondary flex-shrink-0"}),l.jsxs("div",{className:"flex-1 min-w-0",children:[l.jsx("div",{className:"text-sm font-medium text-white truncate",children:$.title}),l.jsx("div",{className:"text-xs text-brand-text-secondary truncate",children:$.url})]}),l.jsx("button",{onClick:()=>P($.id),className:"text-brand-text-secondary hover:text-danger transition-colors",children:l.jsx(Kr,{className:"w-4 h-4"})})]},$.id))}),S&&l.jsxs("div",{className:"space-y-3 p-3 bg-brand-bg rounded border",children:[l.jsx("input",{type:"text",value:A.title,onChange:$=>T({...A,title:$.target.value}),placeholder:"Reference title",className:"w-full p-2 bg-brand-surface border border-brand-border rounded text-white placeholder-brand-text-secondary focus:border-brand-primary focus:outline-none"}),l.jsx("input",{type:"url",value:A.url,onChange:$=>T({...A,url:$.target.value}),placeholder:"https://example.com",className:"w-full p-2 bg-brand-surface border border-brand-border rounded text-white placeholder-brand-text-secondary focus:border-brand-primary focus:outline-none"}),l.jsx("input",{type:"text",value:A.description,onChange:$=>T({...A,description:$.target.value}),placeholder:"Brief description (optional)",className:"w-full p-2 bg-brand-surface border border-brand-border rounded text-white placeholder-brand-text-secondary focus:border-brand-primary focus:outline-none"}),l.jsxs("div",{className:"flex gap-2",children:[l.jsx("button",{onClick:()=>U(!1),className:"px-3 py-1 text-sm text-brand-text-secondary hover:text-white transition-colors",children:"Cancel"}),l.jsx("button",{onClick:ae,disabled:!A.title.trim()||!A.url.trim(),className:"px-3 py-1 bg-brand-secondary hover:bg-brand-secondary-dark text-white text-sm rounded transition-colors disabled:opacity-50",children:"Add"})]})]})]}),l.jsxs("div",{className:"flex gap-3 pt-4",children:[l.jsx("button",{onClick:c,className:"px-4 py-2 text-brand-text-secondary hover:text-white transition-colors",children:"Cancel"}),l.jsx("button",{onClick:K,disabled:!X||!F||o,className:`px-6 py-2 bg-${le} hover:bg-${le}/80 text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed`,children:o?"Adding...":`Add Argument ${Q}`})]})]})]})},gm=({participant:a,isSpeaking:r,isMuted:c,isClash:o,onToggleSpeak:d})=>{const h="ring-4 transition-all duration-300";let m="ring-brand-surface-light";o?m="ring-warning":r?m="ring-success":c&&(m="ring-danger");let b="Ready to Speak",g="text-brand-text-light";return o?(b="CLASH!",g="text-warning"):r?(b="Speaking",g="text-success"):c&&(b="Muted",g="text-danger"),l.jsxs("div",{className:"flex flex-col items-center gap-4 p-6 bg-brand-surface rounded-xl w-64",children:[l.jsx("div",{className:`${h} ${m} rounded-full`,children:l.jsx(Mt,{participant:a,size:"lg"})}),l.jsx("h3",{className:"font-bold text-lg",children:a.name}),l.jsx("p",{className:`font-mono text-sm font-semibold ${g}`,children:b}),l.jsxs("button",{onClick:d,className:`w-full flex items-center justify-center gap-2 px-4 py-3 rounded-lg font-semibold transition-all duration-200
        ${r?"bg-success text-white shadow-lg":"bg-brand-surface-light hover:bg-brand-primary text-brand-text"}`,children:[r?l.jsx(Cy,{className:"w-6 h-6"}):l.jsx(Ry,{className:"w-6 h-6"}),l.jsx("span",{children:r?"Mute":"Speak"})]})]})},My=({proponent:a,opponent:r})=>{const[c,o]=k.useState("idle"),[d,h]=k.useState({isSpeaking:!1,isMuted:!1}),[m,b]=k.useState({isSpeaking:!1,isMuted:!1}),[g,x]=k.useState(!1),y=k.useCallback(async()=>{o("requesting");try{(await navigator.mediaDevices.getUserMedia({audio:!0})).getTracks().forEach(U=>U.stop()),o("granted")}catch(S){console.error("Microphone permission denied:",S),o("denied")}},[]);return k.useEffect(()=>{const S=d.isSpeaking,U=m.isSpeaking;S&&U?(x(!0),h(B=>({...B,isMuted:!0})),b(B=>({...B,isMuted:!0}))):S?(x(!1),h(B=>({...B,isMuted:!1})),b(B=>({...B,isMuted:!0}))):U?(x(!1),h(B=>({...B,isMuted:!0})),b(B=>({...B,isMuted:!1}))):(x(!1),h(B=>({...B,isMuted:!1})),b(B=>({...B,isMuted:!1})))},[d.isSpeaking,m.isSpeaking]),c==="denied"?l.jsxs("div",{className:"bg-brand-surface rounded-lg p-8 text-center border-2 border-dashed border-danger",children:[l.jsx("h2",{className:"text-xl font-bold text-danger mb-2",children:"Microphone Access Denied"}),l.jsx("p",{className:"text-brand-text-light",children:"Please enable microphone access in your browser settings to participate in the live debate."})]}):c!=="granted"?l.jsxs("div",{className:"bg-brand-surface rounded-lg p-8 text-center border-2 border-dashed border-brand-surface-light",children:[l.jsx("h2",{className:"text-xl font-bold mb-4",children:"Join the Live Stage"}),l.jsx("p",{className:"text-brand-text-light mb-6",children:"This feature requires microphone access to ensure fair speaking turns."}),l.jsx("button",{onClick:y,disabled:c==="requesting",className:"bg-brand-primary hover:bg-brand-primary-hover disabled:bg-gray-500 text-white font-bold py-3 px-6 rounded-lg transition-colors",children:c==="requesting"?"Requesting...":"Allow Microphone"})]}):l.jsxs("div",{className:"bg-brand-surface rounded-lg p-8 border-2 border-brand-primary/50",children:[l.jsx("h2",{className:"text-2xl font-bold text-center mb-2",children:"Live Debate Stage"}),l.jsx("p",{className:"text-brand-text-light text-center mb-8",children:"Only one person can speak at a time. If both speak, both are muted."}),l.jsxs("div",{className:"flex items-start justify-center gap-8 flex-wrap relative",children:[l.jsx(gm,{participant:a,isSpeaking:d.isSpeaking,isMuted:d.isMuted,isClash:g,onToggleSpeak:()=>h(S=>({...S,isSpeaking:!S.isSpeaking}))}),l.jsx("div",{className:`text-warning transition-opacity duration-300 ${g?"opacity-100":"opacity-0"}`,children:l.jsx(Dy,{className:"w-12 h-12 self-center mt-24"})}),l.jsx(gm,{participant:r,isSpeaking:m.isSpeaking,isMuted:m.isMuted,isClash:g,onToggleSpeak:()=>b(S=>({...S,isSpeaking:!S.isSpeaking}))})]})]})},ep=({contentId:a,contentType:r,className:c=""})=>{const[o,d]=k.useState(!1),[h,m]=k.useState(""),[b,g]=k.useState(!1),[x,y]=k.useState(!1),S=[{value:"inappropriate_language",label:"Inappropriate Language"},{value:"harassment",label:"Harassment or Bullying"},{value:"spam",label:"Spam or Repetitive Content"},{value:"misinformation",label:"Misinformation or False Claims"},{value:"off_topic",label:"Off-topic or Irrelevant"},{value:"personal_attack",label:"Personal Attack"},{value:"other",label:"Other"}],U=async()=>{if(h){g(!0);try{await Ve.reportContent(a,r,h),y(!0),d(!1)}catch(B){console.error("Failed to report content:",B)}finally{g(!1)}}};return x?l.jsx("span",{className:"text-xs text-gray-500",children:"✓ Reported"}):l.jsxs(l.Fragment,{children:[l.jsx("button",{onClick:()=>d(!0),className:`text-xs text-gray-400 hover:text-red-400 transition-colors ${c}`,title:"Report inappropriate content",children:"🚩 Report"}),o&&l.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:l.jsxs("div",{className:"bg-brand-surface rounded-lg p-6 max-w-md w-full mx-4",children:[l.jsx("h3",{className:"text-lg font-semibold text-brand-text mb-4",children:"Report Content"}),l.jsx("p",{className:"text-sm text-brand-text-secondary mb-4",children:"Help us maintain community standards by reporting inappropriate content."}),l.jsx("div",{className:"space-y-2 mb-6",children:S.map(B=>l.jsxs("label",{className:"flex items-center gap-2",children:[l.jsx("input",{type:"radio",name:"reason",value:B.value,checked:h===B.value,onChange:O=>m(O.target.value),className:"text-brand-primary"}),l.jsx("span",{className:"text-sm text-brand-text",children:B.label})]},B.value))}),l.jsxs("div",{className:"flex gap-3",children:[l.jsx("button",{onClick:()=>d(!1),className:"flex-1 px-4 py-2 text-sm border border-gray-600 rounded-lg hover:bg-gray-700 transition-colors",disabled:b,children:"Cancel"}),l.jsx("button",{onClick:U,disabled:!h||b,className:"flex-1 px-4 py-2 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:b?"Reporting...":"Report"})]})]})})]})},zy=({references:a})=>l.jsxs("div",{children:[l.jsx("h4",{className:"text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2",children:"References"}),l.jsx("ul",{className:"space-y-1",children:a.map(r=>l.jsx("li",{children:l.jsxs("a",{href:r.url,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2 text-brand-primary/80 hover:text-brand-primary text-sm transition-colors",children:[l.jsx(bs,{className:"w-4 h-4"}),l.jsx("span",{children:r.title})]})},r.id))})]}),By=({evidence:a,onVoteOnEvidence:r,currentUserId:c})=>{const o=g=>{switch(g){case"statistic":return"📊";case"study":return"🔬";case"expert_opinion":return"👨‍🎓";case"case_study":return"📋";case"historical_fact":return"📚";default:return"📄"}},d=g=>{switch(g){case"verified":return"text-green-400";case"pending":return"text-yellow-400";case"disputed":return"text-red-400";default:return"text-gray-400"}},h=c&&a.votedBy?a.votedBy.find(g=>g.userId===c):null,m=!!h,b=r&&c;return l.jsxs("div",{className:"bg-brand-bg/30 rounded-lg p-3 border-l-2 border-brand-primary/30",children:[l.jsxs("div",{className:"flex items-start justify-between mb-2",children:[l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("span",{className:"text-lg",children:o(a.type)}),l.jsx("span",{className:"text-sm font-medium text-brand-text capitalize",children:a.type.replace("_"," ")})]}),l.jsx("span",{className:`text-xs font-medium ${d(a.verificationStatus)}`,children:a.verificationStatus})]}),l.jsx("p",{className:"text-sm text-brand-text-light mb-2",children:a.content}),l.jsxs("div",{className:"flex items-center justify-between mb-2",children:[l.jsxs("a",{href:a.source.url,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-brand-primary/80 hover:text-brand-primary text-xs transition-colors",children:[l.jsx(bs,{className:"w-3 h-3"}),l.jsx("span",{children:a.source.title})]}),l.jsxs("div",{className:"flex items-center gap-2 text-xs text-brand-text-secondary",children:[l.jsx(Mt,{participant:a.addedBy,size:"sm"}),l.jsx("span",{children:a.addedBy.name}),l.jsx(ep,{contentId:a.id,contentType:"evidence",className:"ml-2"})]})]}),l.jsx("div",{className:"flex items-center justify-between pt-2 border-t border-brand-primary/10",children:l.jsxs("div",{className:"flex items-center gap-3",children:[r&&c&&l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsxs("button",{onClick:g=>{g.preventDefault(),g.stopPropagation(),r(a.id,"accurate")},disabled:!b,className:`flex items-center gap-1 px-2 py-1 rounded text-xs transition-colors ${(h==null?void 0:h.vote)==="accurate"?"bg-green-500/30 text-green-300 border border-green-500/50":b?"bg-green-500/10 text-green-400 hover:bg-green-500/20":"bg-gray-500/10 text-gray-500 cursor-not-allowed"}`,title:(h==null?void 0:h.vote)==="accurate"?"You voted accurate (click to change)":"Vote as accurate",type:"button",children:[l.jsx("span",{children:"✓"}),l.jsx("span",{children:a.accurateVotes||0})]}),l.jsxs("button",{onClick:g=>{g.preventDefault(),g.stopPropagation(),r(a.id,"inaccurate")},disabled:!b,className:`flex items-center gap-1 px-2 py-1 rounded text-xs transition-colors ${(h==null?void 0:h.vote)==="inaccurate"?"bg-red-500/30 text-red-300 border border-red-500/50":b?"bg-red-500/10 text-red-400 hover:bg-red-500/20":"bg-gray-500/10 text-gray-500 cursor-not-allowed"}`,title:(h==null?void 0:h.vote)==="inaccurate"?"You voted inaccurate (click to change)":"Vote as inaccurate",type:"button",children:[l.jsx("span",{children:"✗"}),l.jsx("span",{children:a.inaccurateVotes||0})]})]}),a.totalVotes>0&&l.jsxs("div",{className:"text-xs text-brand-text-secondary",children:[l.jsxs("span",{className:"font-medium",children:[Math.round(a.verificationScore||0),"%"]})," accurate",l.jsxs("span",{className:"ml-1",children:["(",a.totalVotes," vote",a.totalVotes!==1?"s":"",")"]})]}),m&&l.jsxs("div",{className:"text-xs text-brand-text-secondary",children:["You voted: ",l.jsx("span",{className:(h==null?void 0:h.vote)==="accurate"?"text-green-400":"text-red-400",children:h==null?void 0:h.vote})," ",l.jsx("span",{className:"text-gray-500",children:"(click to change)"})]})]})})]})},ky=({argument:a,onVote:r,onAddSubArgument:c,onAddEvidence:o,onVoteOnEvidence:d,onArgumentClick:h,canAddArguments:m,onRefresh:b,currentUserId:g,onChatClick:x})=>{const{user:y}=xt(),[S,U]=k.useState(!1),[B,O]=k.useState(!1),[A,T]=k.useState(!1),[X,L]=k.useState({type:"other",content:"",sourceTitle:"",sourceUrl:""}),F=a.side===Te.FOR,ae=F?"border-success":"border-danger",P=F?"text-success":"text-danger",K=async(Q,$,J)=>{T(!0);try{const xe=$===Te.AGAINST;await c(a.id,Q,xe),U(!1),await b(),console.log("Sub-argument added successfully!")}catch(xe){console.error("Error adding sub-argument:",xe),alert("Failed to add sub-argument. Please try again.")}finally{T(!1)}},le=async()=>{if(X.content.trim()&&X.sourceTitle.trim()&&X.sourceUrl.trim())try{const Q={type:X.type,content:X.content.trim(),source:{id:`ref-${Date.now()}`,title:X.sourceTitle.trim(),url:X.sourceUrl.trim(),type:"other"}};await o(a.id,Q),L({type:"other",content:"",sourceTitle:"",sourceUrl:""}),O(!1),await b()}catch(Q){console.error("Failed to add evidence:",Q),alert("Failed to add evidence. Please try again.")}};return l.jsxs("div",{className:"bg-brand-surface rounded-xl shadow-lg p-6 mb-8",children:[l.jsxs("div",{className:"flex items-center justify-between mb-4",children:[l.jsxs("div",{className:"flex items-center gap-3",children:[l.jsx(Mt,{participant:a.author}),l.jsxs("div",{children:[l.jsx("span",{className:"font-semibold text-brand-text",children:a.author.name}),l.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[l.jsx("span",{className:`font-bold text-sm ${P}`,children:F?"FOR":"AGAINST"}),a.isChallenge&&l.jsx("span",{className:"text-xs bg-orange-500/20 text-orange-400 px-2 py-1 rounded-full",children:"Challenge"})]})]})]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("button",{onClick:()=>r(a.id,"up"),className:"text-green-400 hover:text-green-300 transition-colors text-lg",children:"▲"}),l.jsx("span",{className:"font-mono text-lg text-brand-text font-bold",children:a.upvotes-a.downvotes}),l.jsx("button",{onClick:()=>r(a.id,"down"),className:"text-red-400 hover:text-red-300 transition-colors text-lg",children:"▼"}),l.jsxs("button",{onClick:()=>x(a),className:"ml-2 p-2 text-brand-text-secondary hover:text-brand-primary transition-colors rounded-lg hover:bg-brand-primary/10",title:"Open chat",children:[l.jsx(Zr,{className:"w-4 h-4"}),a.comments&&a.comments.length>0&&l.jsx("span",{className:"ml-1 text-xs bg-brand-primary/20 text-brand-primary px-1.5 py-0.5 rounded-full",children:a.comments.length})]})]})]}),l.jsxs("div",{className:`bg-brand-bg/50 rounded-lg p-6 border-l-4 ${ae}`,children:[l.jsxs("p",{className:"text-lg text-brand-text mb-4 italic",children:['"',a.text,'"']}),l.jsxs("div",{className:"flex justify-between items-end",children:[a.references.length>0&&l.jsx(zy,{references:a.references}),l.jsxs("div",{className:"text-sm text-brand-text-secondary",children:["Strength Score: ",l.jsxs("span",{className:"text-brand-text font-semibold",children:[a.strengthScore,"%"]})]})]})]}),console.log("FocusedArgumentView - Argument ID:",a.id),console.log("FocusedArgumentView - Argument evidence:",a.evidence),a.evidence&&a.evidence.length>0?l.jsxs("div",{className:"mt-6",children:[l.jsxs("h4",{className:"text-lg font-semibold text-brand-text mb-4",children:["Evidence (",a.evidence.length,")"]}),l.jsx("div",{className:"space-y-3",children:a.evidence.map(Q=>l.jsx(By,{evidence:Q,onVoteOnEvidence:d?($,J)=>d(a.id,$,J):void 0,currentUserId:g},Q.id))})]}):l.jsxs("div",{className:"mt-6",children:[l.jsx("h4",{className:"text-lg font-semibold text-brand-text mb-4",children:"Evidence (0)"}),l.jsx("p",{className:"text-brand-text-light italic",children:"No evidence has been added to this argument yet."})]}),m&&l.jsx("div",{className:"mt-6",children:B?l.jsxs("div",{className:"bg-brand-bg/50 rounded-lg p-4",children:[l.jsx("h4",{className:"text-lg font-semibold text-brand-text mb-4",children:"Add Evidence"}),l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text mb-1",children:"Evidence Type"}),l.jsxs("select",{value:X.type,onChange:Q=>L($=>({...$,type:Q.target.value})),className:"w-full bg-brand-surface border border-brand-surface-light rounded-lg p-2 text-brand-text",children:[l.jsx("option",{value:"statistic",children:"Statistic"}),l.jsx("option",{value:"study",children:"Study"}),l.jsx("option",{value:"expert_opinion",children:"Expert Opinion"}),l.jsx("option",{value:"case_study",children:"Case Study"}),l.jsx("option",{value:"historical_fact",children:"Historical Fact"}),l.jsx("option",{value:"other",children:"Other"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text mb-1",children:"Evidence Description"}),l.jsx("textarea",{value:X.content,onChange:Q=>L($=>({...$,content:Q.target.value})),placeholder:"Describe the evidence...",className:"w-full bg-brand-surface border border-brand-surface-light rounded-lg p-3 text-brand-text resize-none",rows:3})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text mb-1",children:"Source Title"}),l.jsx("input",{type:"text",value:X.sourceTitle,onChange:Q=>L($=>({...$,sourceTitle:Q.target.value})),placeholder:"Source title...",className:"w-full bg-brand-surface border border-brand-surface-light rounded-lg p-2 text-brand-text"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text mb-1",children:"Source URL"}),l.jsx("input",{type:"url",value:X.sourceUrl,onChange:Q=>L($=>({...$,sourceUrl:Q.target.value})),placeholder:"https://...",className:"w-full bg-brand-surface border border-brand-surface-light rounded-lg p-2 text-brand-text"})]})]}),l.jsxs("div",{className:"flex justify-end gap-2 mt-4",children:[l.jsx("button",{onClick:()=>O(!1),className:"px-3 py-1 text-sm text-brand-text-secondary hover:text-white transition-colors",children:"Cancel"}),l.jsx("button",{onClick:le,disabled:!X.content.trim()||!X.sourceTitle.trim()||!X.sourceUrl.trim(),className:"px-3 py-1 bg-brand-secondary hover:bg-brand-secondary-dark text-white text-sm rounded transition-colors disabled:opacity-50",children:"Add Evidence"})]})]})]}):l.jsxs("button",{onClick:()=>O(!0),className:"flex items-center gap-2 px-4 py-2 bg-brand-secondary/20 hover:bg-brand-secondary/30 text-brand-secondary border border-brand-secondary/50 rounded-lg transition-colors",children:[l.jsx(Fn,{className:"w-4 h-4"}),"Add Evidence"]})}),m&&l.jsx("div",{className:"mt-6",children:S?l.jsxs("div",{className:"space-y-4",children:[l.jsx("h4",{className:"text-lg font-semibold text-brand-text",children:"Add Supporting or Challenging Argument"}),l.jsx(ko,{side:a.side,onSubmit:K,onCancel:()=>U(!1),isSubmitting:A,allowSideSelection:!0})]}):l.jsxs("button",{onClick:()=>U(!0),className:"flex items-center gap-2 px-4 py-2 bg-brand-primary/20 hover:bg-brand-primary/30 text-brand-primary border border-brand-primary/50 rounded-lg transition-colors",children:[l.jsx(Fn,{className:"w-4 h-4"}),"Add Sub-Argument"]})}),a.subArguments&&a.subArguments.length>0&&l.jsxs("div",{className:"mt-8",children:[l.jsxs("h3",{className:"text-xl font-bold text-brand-text mb-4",children:["Sub-Arguments (",a.subArguments.length,")"]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 items-stretch",children:[l.jsxs("div",{className:"flex flex-col h-full",children:[l.jsxs("h4",{className:"text-lg font-medium text-success mb-3 flex items-center gap-2",children:[l.jsx("span",{className:"w-3 h-3 bg-success rounded-full"}),"Supporting Arguments"]}),l.jsxs("div",{className:"flex-1 flex flex-col gap-3",children:[a.subArguments.filter(Q=>Q.side===Te.FOR).sort((Q,$)=>($.upvotes||0)-($.downvotes||0)-((Q.upvotes||0)-(Q.downvotes||0))).map(Q=>l.jsxs("div",{className:"border border-brand-border rounded-lg p-3 hover:border-success/50 transition-colors cursor-pointer bg-brand-bg hover:bg-success/5 flex flex-col h-full",onClick:()=>h(Q),children:[l.jsxs("div",{className:"flex items-start justify-between mb-2",children:[l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(Mt,{participant:Q.author,size:"sm"}),l.jsx("span",{className:"text-sm font-medium text-brand-text",children:Q.author.name})]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx("button",{onClick:$=>{$.stopPropagation(),r(Q.id,"up")},className:"text-green-400 hover:text-green-300 transition-colors text-sm",children:"▲"}),l.jsx("span",{className:"text-sm font-medium text-brand-text",children:(Q.upvotes||0)-(Q.downvotes||0)}),l.jsx("button",{onClick:$=>{$.stopPropagation(),r(Q.id,"down")},className:"text-red-400 hover:text-red-300 transition-colors text-sm",children:"▼"})]}),l.jsx("button",{onClick:()=>h(Q),className:"text-xs text-brand-text-secondary hover:text-brand-text transition-colors px-2 py-1 rounded",children:"View"})]})]}),l.jsx("p",{className:"text-sm text-brand-text-light flex-1 mt-2",children:Q.text})]},Q.id)),a.subArguments.filter(Q=>Q.side===Te.FOR).length===0&&l.jsx("div",{className:"text-center py-6 text-brand-text-light border border-dashed border-brand-border rounded-lg flex items-center justify-center h-full",children:"No supporting arguments yet"})]})]}),l.jsxs("div",{className:"flex flex-col h-full",children:[l.jsxs("h4",{className:"text-lg font-medium text-danger mb-3 flex items-center gap-2",children:[l.jsx("span",{className:"w-3 h-3 bg-danger rounded-full"}),"Challenging Arguments"]}),l.jsxs("div",{className:"flex-1 flex flex-col gap-3",children:[a.subArguments.filter(Q=>Q.side===Te.AGAINST).sort((Q,$)=>($.upvotes||0)-($.downvotes||0)-((Q.upvotes||0)-(Q.downvotes||0))).map(Q=>l.jsxs("div",{className:"border border-brand-border rounded-lg p-3 hover:border-danger/50 transition-colors cursor-pointer bg-brand-bg hover:bg-danger/5 flex flex-col h-full",onClick:()=>h(Q),children:[l.jsxs("div",{className:"flex items-start justify-between mb-2",children:[l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(Mt,{participant:Q.author,size:"sm"}),l.jsx("span",{className:"text-sm font-medium text-brand-text",children:Q.author.name})]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx("button",{onClick:$=>{$.stopPropagation(),r(Q.id,"up")},className:"text-green-400 hover:text-green-300 transition-colors text-sm",children:"▲"}),l.jsx("span",{className:"text-sm font-medium text-brand-text",children:(Q.upvotes||0)-(Q.downvotes||0)}),l.jsx("button",{onClick:$=>{$.stopPropagation(),r(Q.id,"down")},className:"text-red-400 hover:text-red-300 transition-colors text-sm",children:"▼"})]}),l.jsx("button",{onClick:()=>h(Q),className:"text-xs text-brand-text-secondary hover:text-brand-text transition-colors px-2 py-1 rounded",children:"View"})]})]}),l.jsx("p",{className:"text-sm text-brand-text-light flex-1 mt-2",children:Q.text})]},Q.id)),a.subArguments.filter(Q=>Q.side===Te.AGAINST).length===0&&l.jsx("div",{className:"text-center py-6 text-brand-text-light border border-dashed border-brand-border rounded-lg flex items-center justify-center h-full",children:"No challenging arguments yet"})]})]})]})]}),l.jsx("div",{className:"mt-8 pt-4 border-t border-brand-border/30 flex justify-end",children:l.jsx(ep,{contentId:a.id,contentType:"argument",className:"text-xs text-brand-text-secondary/60 hover:text-brand-text-secondary"})})]})},qy=({breadcrumbPath:a,onNavigate:r})=>a.length<=1?null:l.jsx("div",{className:"bg-brand-surface/50 rounded-lg p-3 mb-6",children:l.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[l.jsx("span",{className:"text-brand-text-secondary",children:"Navigate:"}),a.map((c,o)=>l.jsxs(Am.Fragment,{children:[l.jsx("button",{onClick:()=>r(o),className:`px-2 py-1 rounded transition-colors ${o===a.length-1?"text-brand-text font-semibold bg-brand-primary/20":"text-brand-text-light hover:text-brand-text hover:bg-brand-surface"}`,children:c.type==="claim"?"Main Claim":`Argument: ${c.text.slice(0,30)}${c.text.length>30?"...":""}`}),o<a.length-1&&l.jsx(Mo,{className:"w-4 h-4 text-brand-text-secondary"})]},c.id))]})}),cn=Object.create(null);cn.open="0";cn.close="1";cn.ping="2";cn.pong="3";cn.message="4";cn.upgrade="5";cn.noop="6";const Br=Object.create(null);Object.keys(cn).forEach(a=>{Br[cn[a]]=a});const qo={type:"error",data:"parser error"},tp=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",np=typeof ArrayBuffer=="function",sp=a=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(a):a&&a.buffer instanceof ArrayBuffer,Jo=({type:a,data:r},c,o)=>tp&&r instanceof Blob?c?o(r):ym(r,o):np&&(r instanceof ArrayBuffer||sp(r))?c?o(r):ym(new Blob([r]),o):o(cn[a]+(r||"")),ym=(a,r)=>{const c=new FileReader;return c.onload=function(){const o=c.result.split(",")[1];r("b"+(o||""))},c.readAsDataURL(a)};function vm(a){return a instanceof Uint8Array?a:a instanceof ArrayBuffer?new Uint8Array(a):new Uint8Array(a.buffer,a.byteOffset,a.byteLength)}let wo;function Ly(a,r){if(tp&&a.data instanceof Blob)return a.data.arrayBuffer().then(vm).then(r);if(np&&(a.data instanceof ArrayBuffer||sp(a.data)))return r(vm(a.data));Jo(a,!1,c=>{wo||(wo=new TextEncoder),r(wo.encode(c))})}const jm="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",cl=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let a=0;a<jm.length;a++)cl[jm.charCodeAt(a)]=a;const Hy=a=>{let r=a.length*.75,c=a.length,o,d=0,h,m,b,g;a[a.length-1]==="="&&(r--,a[a.length-2]==="="&&r--);const x=new ArrayBuffer(r),y=new Uint8Array(x);for(o=0;o<c;o+=4)h=cl[a.charCodeAt(o)],m=cl[a.charCodeAt(o+1)],b=cl[a.charCodeAt(o+2)],g=cl[a.charCodeAt(o+3)],y[d++]=h<<2|m>>4,y[d++]=(m&15)<<4|b>>2,y[d++]=(b&3)<<6|g&63;return x},Yy=typeof ArrayBuffer=="function",Fo=(a,r)=>{if(typeof a!="string")return{type:"message",data:ap(a,r)};const c=a.charAt(0);return c==="b"?{type:"message",data:Vy(a.substring(1),r)}:Br[c]?a.length>1?{type:Br[c],data:a.substring(1)}:{type:Br[c]}:qo},Vy=(a,r)=>{if(Yy){const c=Hy(a);return ap(c,r)}else return{base64:!0,data:a}},ap=(a,r)=>{switch(r){case"blob":return a instanceof Blob?a:new Blob([a]);case"arraybuffer":default:return a instanceof ArrayBuffer?a:a.buffer}},lp="",Gy=(a,r)=>{const c=a.length,o=new Array(c);let d=0;a.forEach((h,m)=>{Jo(h,!1,b=>{o[m]=b,++d===c&&r(o.join(lp))})})},Xy=(a,r)=>{const c=a.split(lp),o=[];for(let d=0;d<c.length;d++){const h=Fo(c[d],r);if(o.push(h),h.type==="error")break}return o};function Qy(){return new TransformStream({transform(a,r){Ly(a,c=>{const o=c.length;let d;if(o<126)d=new Uint8Array(1),new DataView(d.buffer).setUint8(0,o);else if(o<65536){d=new Uint8Array(3);const h=new DataView(d.buffer);h.setUint8(0,126),h.setUint16(1,o)}else{d=new Uint8Array(9);const h=new DataView(d.buffer);h.setUint8(0,127),h.setBigUint64(1,BigInt(o))}a.data&&typeof a.data!="string"&&(d[0]|=128),r.enqueue(d),r.enqueue(c)})}})}let Ao;function Cr(a){return a.reduce((r,c)=>r+c.length,0)}function Or(a,r){if(a[0].length===r)return a.shift();const c=new Uint8Array(r);let o=0;for(let d=0;d<r;d++)c[d]=a[0][o++],o===a[0].length&&(a.shift(),o=0);return a.length&&o<a[0].length&&(a[0]=a[0].slice(o)),c}function Zy(a,r){Ao||(Ao=new TextDecoder);const c=[];let o=0,d=-1,h=!1;return new TransformStream({transform(m,b){for(c.push(m);;){if(o===0){if(Cr(c)<1)break;const g=Or(c,1);h=(g[0]&128)===128,d=g[0]&127,d<126?o=3:d===126?o=1:o=2}else if(o===1){if(Cr(c)<2)break;const g=Or(c,2);d=new DataView(g.buffer,g.byteOffset,g.length).getUint16(0),o=3}else if(o===2){if(Cr(c)<8)break;const g=Or(c,8),x=new DataView(g.buffer,g.byteOffset,g.length),y=x.getUint32(0);if(y>Math.pow(2,21)-1){b.enqueue(qo);break}d=y*Math.pow(2,32)+x.getUint32(4),o=3}else{if(Cr(c)<d)break;const g=Or(c,d);b.enqueue(Fo(h?g:Ao.decode(g),r)),o=0}if(d===0||d>a){b.enqueue(qo);break}}}})}const rp=4;function tt(a){if(a)return Ky(a)}function Ky(a){for(var r in tt.prototype)a[r]=tt.prototype[r];return a}tt.prototype.on=tt.prototype.addEventListener=function(a,r){return this._callbacks=this._callbacks||{},(this._callbacks["$"+a]=this._callbacks["$"+a]||[]).push(r),this};tt.prototype.once=function(a,r){function c(){this.off(a,c),r.apply(this,arguments)}return c.fn=r,this.on(a,c),this};tt.prototype.off=tt.prototype.removeListener=tt.prototype.removeAllListeners=tt.prototype.removeEventListener=function(a,r){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var c=this._callbacks["$"+a];if(!c)return this;if(arguments.length==1)return delete this._callbacks["$"+a],this;for(var o,d=0;d<c.length;d++)if(o=c[d],o===r||o.fn===r){c.splice(d,1);break}return c.length===0&&delete this._callbacks["$"+a],this};tt.prototype.emit=function(a){this._callbacks=this._callbacks||{};for(var r=new Array(arguments.length-1),c=this._callbacks["$"+a],o=1;o<arguments.length;o++)r[o-1]=arguments[o];if(c){c=c.slice(0);for(var o=0,d=c.length;o<d;++o)c[o].apply(this,r)}return this};tt.prototype.emitReserved=tt.prototype.emit;tt.prototype.listeners=function(a){return this._callbacks=this._callbacks||{},this._callbacks["$"+a]||[]};tt.prototype.hasListeners=function(a){return!!this.listeners(a).length};const ti=typeof Promise=="function"&&typeof Promise.resolve=="function"?r=>Promise.resolve().then(r):(r,c)=>c(r,0),Qt=typeof self<"u"?self:typeof window<"u"?window:Function("return this")(),Jy="arraybuffer";function ip(a,...r){return r.reduce((c,o)=>(a.hasOwnProperty(o)&&(c[o]=a[o]),c),{})}const Fy=Qt.setTimeout,$y=Qt.clearTimeout;function ni(a,r){r.useNativeTimers?(a.setTimeoutFn=Fy.bind(Qt),a.clearTimeoutFn=$y.bind(Qt)):(a.setTimeoutFn=Qt.setTimeout.bind(Qt),a.clearTimeoutFn=Qt.clearTimeout.bind(Qt))}const Wy=1.33;function Py(a){return typeof a=="string"?Iy(a):Math.ceil((a.byteLength||a.size)*Wy)}function Iy(a){let r=0,c=0;for(let o=0,d=a.length;o<d;o++)r=a.charCodeAt(o),r<128?c+=1:r<2048?c+=2:r<55296||r>=57344?c+=3:(o++,c+=4);return c}function cp(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function e0(a){let r="";for(let c in a)a.hasOwnProperty(c)&&(r.length&&(r+="&"),r+=encodeURIComponent(c)+"="+encodeURIComponent(a[c]));return r}function t0(a){let r={},c=a.split("&");for(let o=0,d=c.length;o<d;o++){let h=c[o].split("=");r[decodeURIComponent(h[0])]=decodeURIComponent(h[1])}return r}class n0 extends Error{constructor(r,c,o){super(r),this.description=c,this.context=o,this.type="TransportError"}}class $o extends tt{constructor(r){super(),this.writable=!1,ni(this,r),this.opts=r,this.query=r.query,this.socket=r.socket,this.supportsBinary=!r.forceBase64}onError(r,c,o){return super.emitReserved("error",new n0(r,c,o)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(r){this.readyState==="open"&&this.write(r)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(r){const c=Fo(r,this.socket.binaryType);this.onPacket(c)}onPacket(r){super.emitReserved("packet",r)}onClose(r){this.readyState="closed",super.emitReserved("close",r)}pause(r){}createUri(r,c={}){return r+"://"+this._hostname()+this._port()+this.opts.path+this._query(c)}_hostname(){const r=this.opts.hostname;return r.indexOf(":")===-1?r:"["+r+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(r){const c=e0(r);return c.length?"?"+c:""}}class s0 extends $o{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(r){this.readyState="pausing";const c=()=>{this.readyState="paused",r()};if(this._polling||!this.writable){let o=0;this._polling&&(o++,this.once("pollComplete",function(){--o||c()})),this.writable||(o++,this.once("drain",function(){--o||c()}))}else c()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(r){const c=o=>{if(this.readyState==="opening"&&o.type==="open"&&this.onOpen(),o.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(o)};Xy(r,this.socket.binaryType).forEach(c),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const r=()=>{this.write([{type:"close"}])};this.readyState==="open"?r():this.once("open",r)}write(r){this.writable=!1,Gy(r,c=>{this.doWrite(c,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const r=this.opts.secure?"https":"http",c=this.query||{};return this.opts.timestampRequests!==!1&&(c[this.opts.timestampParam]=cp()),!this.supportsBinary&&!c.sid&&(c.b64=1),this.createUri(r,c)}}let op=!1;try{op=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const a0=op;function l0(){}class r0 extends s0{constructor(r){if(super(r),typeof location<"u"){const c=location.protocol==="https:";let o=location.port;o||(o=c?"443":"80"),this.xd=typeof location<"u"&&r.hostname!==location.hostname||o!==r.port}}doWrite(r,c){const o=this.request({method:"POST",data:r});o.on("success",c),o.on("error",(d,h)=>{this.onError("xhr post error",d,h)})}doPoll(){const r=this.request();r.on("data",this.onData.bind(this)),r.on("error",(c,o)=>{this.onError("xhr poll error",c,o)}),this.pollXhr=r}}let sa=class kr extends tt{constructor(r,c,o){super(),this.createRequest=r,ni(this,o),this._opts=o,this._method=o.method||"GET",this._uri=c,this._data=o.data!==void 0?o.data:null,this._create()}_create(){var r;const c=ip(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");c.xdomain=!!this._opts.xd;const o=this._xhr=this.createRequest(c);try{o.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){o.setDisableHeaderCheck&&o.setDisableHeaderCheck(!0);for(let d in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(d)&&o.setRequestHeader(d,this._opts.extraHeaders[d])}}catch{}if(this._method==="POST")try{o.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{o.setRequestHeader("Accept","*/*")}catch{}(r=this._opts.cookieJar)===null||r===void 0||r.addCookies(o),"withCredentials"in o&&(o.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(o.timeout=this._opts.requestTimeout),o.onreadystatechange=()=>{var d;o.readyState===3&&((d=this._opts.cookieJar)===null||d===void 0||d.parseCookies(o.getResponseHeader("set-cookie"))),o.readyState===4&&(o.status===200||o.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof o.status=="number"?o.status:0)},0))},o.send(this._data)}catch(d){this.setTimeoutFn(()=>{this._onError(d)},0);return}typeof document<"u"&&(this._index=kr.requestsCount++,kr.requests[this._index]=this)}_onError(r){this.emitReserved("error",r,this._xhr),this._cleanup(!0)}_cleanup(r){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=l0,r)try{this._xhr.abort()}catch{}typeof document<"u"&&delete kr.requests[this._index],this._xhr=null}}_onLoad(){const r=this._xhr.responseText;r!==null&&(this.emitReserved("data",r),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}};sa.requestsCount=0;sa.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",Nm);else if(typeof addEventListener=="function"){const a="onpagehide"in Qt?"pagehide":"unload";addEventListener(a,Nm,!1)}}function Nm(){for(let a in sa.requests)sa.requests.hasOwnProperty(a)&&sa.requests[a].abort()}const i0=function(){const a=up({xdomain:!1});return a&&a.responseType!==null}();class c0 extends r0{constructor(r){super(r);const c=r&&r.forceBase64;this.supportsBinary=i0&&!c}request(r={}){return Object.assign(r,{xd:this.xd},this.opts),new sa(up,this.uri(),r)}}function up(a){const r=a.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!r||a0))return new XMLHttpRequest}catch{}if(!r)try{return new Qt[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const dp=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class o0 extends $o{get name(){return"websocket"}doOpen(){const r=this.uri(),c=this.opts.protocols,o=dp?{}:ip(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(o.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(r,c,o)}catch(d){return this.emitReserved("error",d)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=r=>this.onClose({description:"websocket connection closed",context:r}),this.ws.onmessage=r=>this.onData(r.data),this.ws.onerror=r=>this.onError("websocket error",r)}write(r){this.writable=!1;for(let c=0;c<r.length;c++){const o=r[c],d=c===r.length-1;Jo(o,this.supportsBinary,h=>{try{this.doWrite(o,h)}catch{}d&&ti(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const r=this.opts.secure?"wss":"ws",c=this.query||{};return this.opts.timestampRequests&&(c[this.opts.timestampParam]=cp()),this.supportsBinary||(c.b64=1),this.createUri(r,c)}}const Eo=Qt.WebSocket||Qt.MozWebSocket;class u0 extends o0{createSocket(r,c,o){return dp?new Eo(r,c,o):c?new Eo(r,c):new Eo(r)}doWrite(r,c){this.ws.send(c)}}class d0 extends $o{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(r){return this.emitReserved("error",r)}this._transport.closed.then(()=>{this.onClose()}).catch(r=>{this.onError("webtransport error",r)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(r=>{const c=Zy(Number.MAX_SAFE_INTEGER,this.socket.binaryType),o=r.readable.pipeThrough(c).getReader(),d=Qy();d.readable.pipeTo(r.writable),this._writer=d.writable.getWriter();const h=()=>{o.read().then(({done:b,value:g})=>{b||(this.onPacket(g),h())}).catch(b=>{})};h();const m={type:"open"};this.query.sid&&(m.data=`{"sid":"${this.query.sid}"}`),this._writer.write(m).then(()=>this.onOpen())})})}write(r){this.writable=!1;for(let c=0;c<r.length;c++){const o=r[c],d=c===r.length-1;this._writer.write(o).then(()=>{d&&ti(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var r;(r=this._transport)===null||r===void 0||r.close()}}const f0={websocket:u0,webtransport:d0,polling:c0},h0=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,m0=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function Lo(a){if(a.length>8e3)throw"URI too long";const r=a,c=a.indexOf("["),o=a.indexOf("]");c!=-1&&o!=-1&&(a=a.substring(0,c)+a.substring(c,o).replace(/:/g,";")+a.substring(o,a.length));let d=h0.exec(a||""),h={},m=14;for(;m--;)h[m0[m]]=d[m]||"";return c!=-1&&o!=-1&&(h.source=r,h.host=h.host.substring(1,h.host.length-1).replace(/;/g,":"),h.authority=h.authority.replace("[","").replace("]","").replace(/;/g,":"),h.ipv6uri=!0),h.pathNames=p0(h,h.path),h.queryKey=x0(h,h.query),h}function p0(a,r){const c=/\/{2,9}/g,o=r.replace(c,"/").split("/");return(r.slice(0,1)=="/"||r.length===0)&&o.splice(0,1),r.slice(-1)=="/"&&o.splice(o.length-1,1),o}function x0(a,r){const c={};return r.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(o,d,h){d&&(c[d]=h)}),c}const Ho=typeof addEventListener=="function"&&typeof removeEventListener=="function",qr=[];Ho&&addEventListener("offline",()=>{qr.forEach(a=>a())},!1);class Jn extends tt{constructor(r,c){if(super(),this.binaryType=Jy,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,r&&typeof r=="object"&&(c=r,r=null),r){const o=Lo(r);c.hostname=o.host,c.secure=o.protocol==="https"||o.protocol==="wss",c.port=o.port,o.query&&(c.query=o.query)}else c.host&&(c.hostname=Lo(c.host).host);ni(this,c),this.secure=c.secure!=null?c.secure:typeof location<"u"&&location.protocol==="https:",c.hostname&&!c.port&&(c.port=this.secure?"443":"80"),this.hostname=c.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=c.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},c.transports.forEach(o=>{const d=o.prototype.name;this.transports.push(d),this._transportsByName[d]=o}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},c),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=t0(this.opts.query)),Ho&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},qr.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(r){const c=Object.assign({},this.opts.query);c.EIO=rp,c.transport=r,this.id&&(c.sid=this.id);const o=Object.assign({},this.opts,{query:c,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[r]);return new this._transportsByName[r](o)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const r=this.opts.rememberUpgrade&&Jn.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const c=this.createTransport(r);c.open(),this.setTransport(c)}setTransport(r){this.transport&&this.transport.removeAllListeners(),this.transport=r,r.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",c=>this._onClose("transport close",c))}onOpen(){this.readyState="open",Jn.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(r){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",r),this.emitReserved("heartbeat"),r.type){case"open":this.onHandshake(JSON.parse(r.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const c=new Error("server error");c.code=r.data,this._onError(c);break;case"message":this.emitReserved("data",r.data),this.emitReserved("message",r.data);break}}onHandshake(r){this.emitReserved("handshake",r),this.id=r.sid,this.transport.query.sid=r.sid,this._pingInterval=r.pingInterval,this._pingTimeout=r.pingTimeout,this._maxPayload=r.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const r=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+r,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},r),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const r=this._getWritablePackets();this.transport.send(r),this._prevBufferLen=r.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let c=1;for(let o=0;o<this.writeBuffer.length;o++){const d=this.writeBuffer[o].data;if(d&&(c+=Py(d)),o>0&&c>this._maxPayload)return this.writeBuffer.slice(0,o);c+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const r=Date.now()>this._pingTimeoutTime;return r&&(this._pingTimeoutTime=0,ti(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),r}write(r,c,o){return this._sendPacket("message",r,c,o),this}send(r,c,o){return this._sendPacket("message",r,c,o),this}_sendPacket(r,c,o,d){if(typeof c=="function"&&(d=c,c=void 0),typeof o=="function"&&(d=o,o=null),this.readyState==="closing"||this.readyState==="closed")return;o=o||{},o.compress=o.compress!==!1;const h={type:r,data:c,options:o};this.emitReserved("packetCreate",h),this.writeBuffer.push(h),d&&this.once("flush",d),this.flush()}close(){const r=()=>{this._onClose("forced close"),this.transport.close()},c=()=>{this.off("upgrade",c),this.off("upgradeError",c),r()},o=()=>{this.once("upgrade",c),this.once("upgradeError",c)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?o():r()}):this.upgrading?o():r()),this}_onError(r){if(Jn.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",r),this._onClose("transport error",r)}_onClose(r,c){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),Ho&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const o=qr.indexOf(this._offlineEventListener);o!==-1&&qr.splice(o,1)}this.readyState="closed",this.id=null,this.emitReserved("close",r,c),this.writeBuffer=[],this._prevBufferLen=0}}}Jn.protocol=rp;class b0 extends Jn{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let r=0;r<this._upgrades.length;r++)this._probe(this._upgrades[r])}_probe(r){let c=this.createTransport(r),o=!1;Jn.priorWebsocketSuccess=!1;const d=()=>{o||(c.send([{type:"ping",data:"probe"}]),c.once("packet",S=>{if(!o)if(S.type==="pong"&&S.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",c),!c)return;Jn.priorWebsocketSuccess=c.name==="websocket",this.transport.pause(()=>{o||this.readyState!=="closed"&&(y(),this.setTransport(c),c.send([{type:"upgrade"}]),this.emitReserved("upgrade",c),c=null,this.upgrading=!1,this.flush())})}else{const U=new Error("probe error");U.transport=c.name,this.emitReserved("upgradeError",U)}}))};function h(){o||(o=!0,y(),c.close(),c=null)}const m=S=>{const U=new Error("probe error: "+S);U.transport=c.name,h(),this.emitReserved("upgradeError",U)};function b(){m("transport closed")}function g(){m("socket closed")}function x(S){c&&S.name!==c.name&&h()}const y=()=>{c.removeListener("open",d),c.removeListener("error",m),c.removeListener("close",b),this.off("close",g),this.off("upgrading",x)};c.once("open",d),c.once("error",m),c.once("close",b),this.once("close",g),this.once("upgrading",x),this._upgrades.indexOf("webtransport")!==-1&&r!=="webtransport"?this.setTimeoutFn(()=>{o||c.open()},200):c.open()}onHandshake(r){this._upgrades=this._filterUpgrades(r.upgrades),super.onHandshake(r)}_filterUpgrades(r){const c=[];for(let o=0;o<r.length;o++)~this.transports.indexOf(r[o])&&c.push(r[o]);return c}}let g0=class extends b0{constructor(r,c={}){const o=typeof r=="object"?r:c;(!o.transports||o.transports&&typeof o.transports[0]=="string")&&(o.transports=(o.transports||["polling","websocket","webtransport"]).map(d=>f0[d]).filter(d=>!!d)),super(r,o)}};function y0(a,r="",c){let o=a;c=c||typeof location<"u"&&location,a==null&&(a=c.protocol+"//"+c.host),typeof a=="string"&&(a.charAt(0)==="/"&&(a.charAt(1)==="/"?a=c.protocol+a:a=c.host+a),/^(https?|wss?):\/\//.test(a)||(typeof c<"u"?a=c.protocol+"//"+a:a="https://"+a),o=Lo(a)),o.port||(/^(http|ws)$/.test(o.protocol)?o.port="80":/^(http|ws)s$/.test(o.protocol)&&(o.port="443")),o.path=o.path||"/";const h=o.host.indexOf(":")!==-1?"["+o.host+"]":o.host;return o.id=o.protocol+"://"+h+":"+o.port+r,o.href=o.protocol+"://"+h+(c&&c.port===o.port?"":":"+o.port),o}const v0=typeof ArrayBuffer=="function",j0=a=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(a):a.buffer instanceof ArrayBuffer,fp=Object.prototype.toString,N0=typeof Blob=="function"||typeof Blob<"u"&&fp.call(Blob)==="[object BlobConstructor]",S0=typeof File=="function"||typeof File<"u"&&fp.call(File)==="[object FileConstructor]";function Wo(a){return v0&&(a instanceof ArrayBuffer||j0(a))||N0&&a instanceof Blob||S0&&a instanceof File}function Lr(a,r){if(!a||typeof a!="object")return!1;if(Array.isArray(a)){for(let c=0,o=a.length;c<o;c++)if(Lr(a[c]))return!0;return!1}if(Wo(a))return!0;if(a.toJSON&&typeof a.toJSON=="function"&&arguments.length===1)return Lr(a.toJSON(),!0);for(const c in a)if(Object.prototype.hasOwnProperty.call(a,c)&&Lr(a[c]))return!0;return!1}function w0(a){const r=[],c=a.data,o=a;return o.data=Yo(c,r),o.attachments=r.length,{packet:o,buffers:r}}function Yo(a,r){if(!a)return a;if(Wo(a)){const c={_placeholder:!0,num:r.length};return r.push(a),c}else if(Array.isArray(a)){const c=new Array(a.length);for(let o=0;o<a.length;o++)c[o]=Yo(a[o],r);return c}else if(typeof a=="object"&&!(a instanceof Date)){const c={};for(const o in a)Object.prototype.hasOwnProperty.call(a,o)&&(c[o]=Yo(a[o],r));return c}return a}function A0(a,r){return a.data=Vo(a.data,r),delete a.attachments,a}function Vo(a,r){if(!a)return a;if(a&&a._placeholder===!0){if(typeof a.num=="number"&&a.num>=0&&a.num<r.length)return r[a.num];throw new Error("illegal attachments")}else if(Array.isArray(a))for(let c=0;c<a.length;c++)a[c]=Vo(a[c],r);else if(typeof a=="object")for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&(a[c]=Vo(a[c],r));return a}const E0=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],T0=5;var we;(function(a){a[a.CONNECT=0]="CONNECT",a[a.DISCONNECT=1]="DISCONNECT",a[a.EVENT=2]="EVENT",a[a.ACK=3]="ACK",a[a.CONNECT_ERROR=4]="CONNECT_ERROR",a[a.BINARY_EVENT=5]="BINARY_EVENT",a[a.BINARY_ACK=6]="BINARY_ACK"})(we||(we={}));class _0{constructor(r){this.replacer=r}encode(r){return(r.type===we.EVENT||r.type===we.ACK)&&Lr(r)?this.encodeAsBinary({type:r.type===we.EVENT?we.BINARY_EVENT:we.BINARY_ACK,nsp:r.nsp,data:r.data,id:r.id}):[this.encodeAsString(r)]}encodeAsString(r){let c=""+r.type;return(r.type===we.BINARY_EVENT||r.type===we.BINARY_ACK)&&(c+=r.attachments+"-"),r.nsp&&r.nsp!=="/"&&(c+=r.nsp+","),r.id!=null&&(c+=r.id),r.data!=null&&(c+=JSON.stringify(r.data,this.replacer)),c}encodeAsBinary(r){const c=w0(r),o=this.encodeAsString(c.packet),d=c.buffers;return d.unshift(o),d}}function Sm(a){return Object.prototype.toString.call(a)==="[object Object]"}class Po extends tt{constructor(r){super(),this.reviver=r}add(r){let c;if(typeof r=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");c=this.decodeString(r);const o=c.type===we.BINARY_EVENT;o||c.type===we.BINARY_ACK?(c.type=o?we.EVENT:we.ACK,this.reconstructor=new R0(c),c.attachments===0&&super.emitReserved("decoded",c)):super.emitReserved("decoded",c)}else if(Wo(r)||r.base64)if(this.reconstructor)c=this.reconstructor.takeBinaryData(r),c&&(this.reconstructor=null,super.emitReserved("decoded",c));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+r)}decodeString(r){let c=0;const o={type:Number(r.charAt(0))};if(we[o.type]===void 0)throw new Error("unknown packet type "+o.type);if(o.type===we.BINARY_EVENT||o.type===we.BINARY_ACK){const h=c+1;for(;r.charAt(++c)!=="-"&&c!=r.length;);const m=r.substring(h,c);if(m!=Number(m)||r.charAt(c)!=="-")throw new Error("Illegal attachments");o.attachments=Number(m)}if(r.charAt(c+1)==="/"){const h=c+1;for(;++c&&!(r.charAt(c)===","||c===r.length););o.nsp=r.substring(h,c)}else o.nsp="/";const d=r.charAt(c+1);if(d!==""&&Number(d)==d){const h=c+1;for(;++c;){const m=r.charAt(c);if(m==null||Number(m)!=m){--c;break}if(c===r.length)break}o.id=Number(r.substring(h,c+1))}if(r.charAt(++c)){const h=this.tryParse(r.substr(c));if(Po.isPayloadValid(o.type,h))o.data=h;else throw new Error("invalid payload")}return o}tryParse(r){try{return JSON.parse(r,this.reviver)}catch{return!1}}static isPayloadValid(r,c){switch(r){case we.CONNECT:return Sm(c);case we.DISCONNECT:return c===void 0;case we.CONNECT_ERROR:return typeof c=="string"||Sm(c);case we.EVENT:case we.BINARY_EVENT:return Array.isArray(c)&&(typeof c[0]=="number"||typeof c[0]=="string"&&E0.indexOf(c[0])===-1);case we.ACK:case we.BINARY_ACK:return Array.isArray(c)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class R0{constructor(r){this.packet=r,this.buffers=[],this.reconPack=r}takeBinaryData(r){if(this.buffers.push(r),this.buffers.length===this.reconPack.attachments){const c=A0(this.reconPack,this.buffers);return this.finishedReconstruction(),c}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const C0=Object.freeze(Object.defineProperty({__proto__:null,Decoder:Po,Encoder:_0,get PacketType(){return we},protocol:T0},Symbol.toStringTag,{value:"Module"}));function Ft(a,r,c){return a.on(r,c),function(){a.off(r,c)}}const O0=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class hp extends tt{constructor(r,c,o){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=r,this.nsp=c,o&&o.auth&&(this.auth=o.auth),this._opts=Object.assign({},o),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const r=this.io;this.subs=[Ft(r,"open",this.onopen.bind(this)),Ft(r,"packet",this.onpacket.bind(this)),Ft(r,"error",this.onerror.bind(this)),Ft(r,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...r){return r.unshift("message"),this.emit.apply(this,r),this}emit(r,...c){var o,d,h;if(O0.hasOwnProperty(r))throw new Error('"'+r.toString()+'" is a reserved event name');if(c.unshift(r),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(c),this;const m={type:we.EVENT,data:c};if(m.options={},m.options.compress=this.flags.compress!==!1,typeof c[c.length-1]=="function"){const y=this.ids++,S=c.pop();this._registerAckCallback(y,S),m.id=y}const b=(d=(o=this.io.engine)===null||o===void 0?void 0:o.transport)===null||d===void 0?void 0:d.writable,g=this.connected&&!(!((h=this.io.engine)===null||h===void 0)&&h._hasPingExpired());return this.flags.volatile&&!b||(g?(this.notifyOutgoingListeners(m),this.packet(m)):this.sendBuffer.push(m)),this.flags={},this}_registerAckCallback(r,c){var o;const d=(o=this.flags.timeout)!==null&&o!==void 0?o:this._opts.ackTimeout;if(d===void 0){this.acks[r]=c;return}const h=this.io.setTimeoutFn(()=>{delete this.acks[r];for(let b=0;b<this.sendBuffer.length;b++)this.sendBuffer[b].id===r&&this.sendBuffer.splice(b,1);c.call(this,new Error("operation has timed out"))},d),m=(...b)=>{this.io.clearTimeoutFn(h),c.apply(this,b)};m.withError=!0,this.acks[r]=m}emitWithAck(r,...c){return new Promise((o,d)=>{const h=(m,b)=>m?d(m):o(b);h.withError=!0,c.push(h),this.emit(r,...c)})}_addToQueue(r){let c;typeof r[r.length-1]=="function"&&(c=r.pop());const o={id:this._queueSeq++,tryCount:0,pending:!1,args:r,flags:Object.assign({fromQueue:!0},this.flags)};r.push((d,...h)=>o!==this._queue[0]?void 0:(d!==null?o.tryCount>this._opts.retries&&(this._queue.shift(),c&&c(d)):(this._queue.shift(),c&&c(null,...h)),o.pending=!1,this._drainQueue())),this._queue.push(o),this._drainQueue()}_drainQueue(r=!1){if(!this.connected||this._queue.length===0)return;const c=this._queue[0];c.pending&&!r||(c.pending=!0,c.tryCount++,this.flags=c.flags,this.emit.apply(this,c.args))}packet(r){r.nsp=this.nsp,this.io._packet(r)}onopen(){typeof this.auth=="function"?this.auth(r=>{this._sendConnectPacket(r)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(r){this.packet({type:we.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},r):r})}onerror(r){this.connected||this.emitReserved("connect_error",r)}onclose(r,c){this.connected=!1,delete this.id,this.emitReserved("disconnect",r,c),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(r=>{if(!this.sendBuffer.some(o=>String(o.id)===r)){const o=this.acks[r];delete this.acks[r],o.withError&&o.call(this,new Error("socket has been disconnected"))}})}onpacket(r){if(r.nsp===this.nsp)switch(r.type){case we.CONNECT:r.data&&r.data.sid?this.onconnect(r.data.sid,r.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case we.EVENT:case we.BINARY_EVENT:this.onevent(r);break;case we.ACK:case we.BINARY_ACK:this.onack(r);break;case we.DISCONNECT:this.ondisconnect();break;case we.CONNECT_ERROR:this.destroy();const o=new Error(r.data.message);o.data=r.data.data,this.emitReserved("connect_error",o);break}}onevent(r){const c=r.data||[];r.id!=null&&c.push(this.ack(r.id)),this.connected?this.emitEvent(c):this.receiveBuffer.push(Object.freeze(c))}emitEvent(r){if(this._anyListeners&&this._anyListeners.length){const c=this._anyListeners.slice();for(const o of c)o.apply(this,r)}super.emit.apply(this,r),this._pid&&r.length&&typeof r[r.length-1]=="string"&&(this._lastOffset=r[r.length-1])}ack(r){const c=this;let o=!1;return function(...d){o||(o=!0,c.packet({type:we.ACK,id:r,data:d}))}}onack(r){const c=this.acks[r.id];typeof c=="function"&&(delete this.acks[r.id],c.withError&&r.data.unshift(null),c.apply(this,r.data))}onconnect(r,c){this.id=r,this.recovered=c&&this._pid===c,this._pid=c,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(r=>this.emitEvent(r)),this.receiveBuffer=[],this.sendBuffer.forEach(r=>{this.notifyOutgoingListeners(r),this.packet(r)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(r=>r()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:we.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(r){return this.flags.compress=r,this}get volatile(){return this.flags.volatile=!0,this}timeout(r){return this.flags.timeout=r,this}onAny(r){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(r),this}prependAny(r){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(r),this}offAny(r){if(!this._anyListeners)return this;if(r){const c=this._anyListeners;for(let o=0;o<c.length;o++)if(r===c[o])return c.splice(o,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(r){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(r),this}prependAnyOutgoing(r){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(r),this}offAnyOutgoing(r){if(!this._anyOutgoingListeners)return this;if(r){const c=this._anyOutgoingListeners;for(let o=0;o<c.length;o++)if(r===c[o])return c.splice(o,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(r){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const c=this._anyOutgoingListeners.slice();for(const o of c)o.apply(this,r.data)}}}function ra(a){a=a||{},this.ms=a.min||100,this.max=a.max||1e4,this.factor=a.factor||2,this.jitter=a.jitter>0&&a.jitter<=1?a.jitter:0,this.attempts=0}ra.prototype.duration=function(){var a=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var r=Math.random(),c=Math.floor(r*this.jitter*a);a=(Math.floor(r*10)&1)==0?a-c:a+c}return Math.min(a,this.max)|0};ra.prototype.reset=function(){this.attempts=0};ra.prototype.setMin=function(a){this.ms=a};ra.prototype.setMax=function(a){this.max=a};ra.prototype.setJitter=function(a){this.jitter=a};class Go extends tt{constructor(r,c){var o;super(),this.nsps={},this.subs=[],r&&typeof r=="object"&&(c=r,r=void 0),c=c||{},c.path=c.path||"/socket.io",this.opts=c,ni(this,c),this.reconnection(c.reconnection!==!1),this.reconnectionAttempts(c.reconnectionAttempts||1/0),this.reconnectionDelay(c.reconnectionDelay||1e3),this.reconnectionDelayMax(c.reconnectionDelayMax||5e3),this.randomizationFactor((o=c.randomizationFactor)!==null&&o!==void 0?o:.5),this.backoff=new ra({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(c.timeout==null?2e4:c.timeout),this._readyState="closed",this.uri=r;const d=c.parser||C0;this.encoder=new d.Encoder,this.decoder=new d.Decoder,this._autoConnect=c.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(r){return arguments.length?(this._reconnection=!!r,r||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(r){return r===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=r,this)}reconnectionDelay(r){var c;return r===void 0?this._reconnectionDelay:(this._reconnectionDelay=r,(c=this.backoff)===null||c===void 0||c.setMin(r),this)}randomizationFactor(r){var c;return r===void 0?this._randomizationFactor:(this._randomizationFactor=r,(c=this.backoff)===null||c===void 0||c.setJitter(r),this)}reconnectionDelayMax(r){var c;return r===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=r,(c=this.backoff)===null||c===void 0||c.setMax(r),this)}timeout(r){return arguments.length?(this._timeout=r,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(r){if(~this._readyState.indexOf("open"))return this;this.engine=new g0(this.uri,this.opts);const c=this.engine,o=this;this._readyState="opening",this.skipReconnect=!1;const d=Ft(c,"open",function(){o.onopen(),r&&r()}),h=b=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",b),r?r(b):this.maybeReconnectOnOpen()},m=Ft(c,"error",h);if(this._timeout!==!1){const b=this._timeout,g=this.setTimeoutFn(()=>{d(),h(new Error("timeout")),c.close()},b);this.opts.autoUnref&&g.unref(),this.subs.push(()=>{this.clearTimeoutFn(g)})}return this.subs.push(d),this.subs.push(m),this}connect(r){return this.open(r)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const r=this.engine;this.subs.push(Ft(r,"ping",this.onping.bind(this)),Ft(r,"data",this.ondata.bind(this)),Ft(r,"error",this.onerror.bind(this)),Ft(r,"close",this.onclose.bind(this)),Ft(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(r){try{this.decoder.add(r)}catch(c){this.onclose("parse error",c)}}ondecoded(r){ti(()=>{this.emitReserved("packet",r)},this.setTimeoutFn)}onerror(r){this.emitReserved("error",r)}socket(r,c){let o=this.nsps[r];return o?this._autoConnect&&!o.active&&o.connect():(o=new hp(this,r,c),this.nsps[r]=o),o}_destroy(r){const c=Object.keys(this.nsps);for(const o of c)if(this.nsps[o].active)return;this._close()}_packet(r){const c=this.encoder.encode(r);for(let o=0;o<c.length;o++)this.engine.write(c[o],r.options)}cleanup(){this.subs.forEach(r=>r()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(r,c){var o;this.cleanup(),(o=this.engine)===null||o===void 0||o.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",r,c),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const r=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const c=this.backoff.duration();this._reconnecting=!0;const o=this.setTimeoutFn(()=>{r.skipReconnect||(this.emitReserved("reconnect_attempt",r.backoff.attempts),!r.skipReconnect&&r.open(d=>{d?(r._reconnecting=!1,r.reconnect(),this.emitReserved("reconnect_error",d)):r.onreconnect()}))},c);this.opts.autoUnref&&o.unref(),this.subs.push(()=>{this.clearTimeoutFn(o)})}}onreconnect(){const r=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",r)}}const il={};function Hr(a,r){typeof a=="object"&&(r=a,a=void 0),r=r||{};const c=y0(a,r.path||"/socket.io"),o=c.source,d=c.id,h=c.path,m=il[d]&&h in il[d].nsps,b=r.forceNew||r["force new connection"]||r.multiplex===!1||m;let g;return b?g=new Go(o,r):(il[d]||(il[d]=new Go(o,r)),g=il[d]),c.query&&!r.query&&(r.query=c.queryKey),g.socket(c.path,r)}Object.assign(Hr,{Manager:Go,Socket:hp,io:Hr,connect:Hr});var D0={};const mp=k.createContext(void 0),U0=({children:a})=>{const[r,c]=k.useState(null),[o,d]=k.useState(!1),{user:h}=xt();k.useEffect(()=>{if(h){const S=Hr(D0.REACT_APP_API_URL||"http://localhost:5000",{withCredentials:!0,transports:["websocket","polling"]});return S.on("connect",()=>{console.log("Connected to WebSocket server"),d(!0)}),S.on("disconnect",()=>{console.log("Disconnected from WebSocket server"),d(!1)}),S.on("connect_error",U=>{console.error("WebSocket connection error:",U),d(!1)}),c(S),()=>{S.close(),c(null),d(!1)}}else r&&(r.close(),c(null),d(!1))},[h]);const y={socket:r,isConnected:o,joinArgumentChat:(S,U)=>{r&&o&&r.emit("join-argument-chat",{debateId:S,argumentId:U})},leaveArgumentChat:(S,U)=>{r&&o&&r.emit("leave-argument-chat",{debateId:S,argumentId:U})},joinDebate:S=>{r&&o&&r.emit("join-debate",S)},leaveDebate:S=>{r&&o&&r.emit("leave-debate",S)}};return l.jsx(mp.Provider,{value:y,children:a})},pp=()=>{const a=k.useContext(mp);if(a===void 0)throw new Error("useSocket must be used within a SocketProvider");return a},xp=({comment:a,onReply:r,onVote:c,onReport:o,currentUserId:d,depth:h=0})=>{var U,B,O;const b=h>0,g=(B=(U=a.userVotes)==null?void 0:U.find(A=>A.userId===d))==null?void 0:B.vote,x=g==="up",y=g==="down",S=((O=a.reportedBy)==null?void 0:O.includes(d||""))||!1;return l.jsx("div",{className:`${b?"ml-4 border-l border-brand-border/30 pl-4":""} mb-3`,children:l.jsxs("div",{className:"flex gap-3",children:[l.jsx(Mt,{participant:a.author,size:"xs"}),l.jsxs("div",{className:"flex-1 min-w-0",children:[l.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[l.jsx("span",{className:"font-medium text-brand-text text-sm",children:a.author.name}),l.jsx("span",{className:"text-xs text-brand-text-secondary",children:new Date(a.createdAt).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]}),l.jsxs("div",{className:"bg-brand-surface/50 rounded-lg p-3 mb-2 relative",children:[l.jsx("p",{className:"text-brand-text text-sm leading-relaxed pr-6",children:a.text}),l.jsx("button",{onClick:()=>o(a.id),disabled:S,className:`absolute bottom-2 right-2 transition-colors ${S?"text-red-500 opacity-100 cursor-not-allowed":"text-brand-text-secondary hover:text-red-400 opacity-60 hover:opacity-100"}`,title:S?"Reported":"Report this message",children:l.jsx(Oy,{className:`w-3 h-3 ${S?"fill-current":""}`})})]}),l.jsxs("div",{className:"flex items-center gap-4 text-xs",children:[l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx("button",{onClick:()=>c(a.id,"up"),className:`transition-colors ${x?"text-green-400":"text-brand-text-secondary hover:text-green-400"}`,title:x?"Remove upvote":"Upvote",children:"▲"}),l.jsx("span",{className:"text-brand-text-secondary min-w-[20px] text-center",children:(a.upvotes||0)-(a.downvotes||0)}),l.jsx("button",{onClick:()=>c(a.id,"down"),className:`transition-colors ${y?"text-red-400":"text-brand-text-secondary hover:text-red-400"}`,title:y?"Remove downvote":"Downvote",children:"▼"})]}),h<3&&l.jsx("button",{onClick:()=>r(a.id),className:"text-brand-text-secondary hover:text-brand-primary transition-colors",children:"Reply"})]}),a.replies&&a.replies.length>0&&l.jsx("div",{className:"mt-3",children:a.replies.map(A=>l.jsx(xp,{comment:A,onReply:r,onVote:c,onReport:o,currentUserId:d,depth:h+1},A.id))})]})]})})},M0=({argument:a,debateId:r,onBack:c,onAddComment:o,onVoteOnComment:d,onReportComment:h,onArgumentUpdate:m})=>{const{user:b}=xt(),{socket:g,joinArgumentChat:x,leaveArgumentChat:y}=pp(),[S,U]=k.useState(""),[B,O]=k.useState(null),[A,T]=k.useState(!1),X=k.useRef(null),L=k.useRef(null),F=()=>{var J;(J=X.current)==null||J.scrollIntoView({behavior:"smooth"})};k.useEffect(()=>{F()},[a.comments]),k.useEffect(()=>{L.current&&L.current.focus()},[B]),k.useEffect(()=>{if(g&&a.id)return x(r,a.id),()=>{y(r,a.id)}},[g,a.id,r,x,y]),k.useEffect(()=>{if(!g)return;const J=de=>{if(de.argumentId===a.id){if(de.comment.author.id===(b==null?void 0:b.id))return;const Ce={...a};if(Ce.comments||(Ce.comments=[]),de.parentCommentId){const Ge=qe=>{for(const N of qe){if(N.id===de.parentCommentId)return N.replies||(N.replies=[]),N.replies.push(de.comment),!0;if(N.replies&&Ge(N.replies))return!0}return!1};Ge(Ce.comments)}else Ce.comments.push(de.comment);m(Ce)}},xe=de=>{if(de.argumentId===a.id){if(de.voterId===(b==null?void 0:b.id))return;const Ce={...a},Ge=qe=>{for(const N of qe){if(N.id===de.commentId)return N.upvotes=de.upvotes,N.downvotes=de.downvotes,N.votes=de.votes,!0;if(N.replies&&Ge(N.replies))return!0}return!1};Ce.comments&&(Ge(Ce.comments),m(Ce))}};return g.on("comment-added",J),g.on("comment-vote-updated",xe),()=>{g.off("comment-added",J),g.off("comment-vote-updated",xe)}},[g,a,m,b]);const ae=async J=>{if(J.preventDefault(),!(!S.trim()||A)){T(!0);try{await o(a.id,S.trim(),B||void 0),U(""),O(null)}catch(xe){console.error("Failed to send message:",xe)}finally{T(!1)}}},P=J=>{O(J),L.current&&L.current.focus()},K=(J,xe)=>{d(a.id,J,xe)},le=J=>{h(a.id,J)},Q=(J,xe)=>{for(const de of xe){if(de.id===J)return de;if(de.replies){const Ce=Q(J,de.replies);if(Ce)return Ce}}return null},$=B?Q(B,a.comments||[]):null;return l.jsxs("div",{className:"fixed inset-y-0 right-0 w-96 bg-brand-background border-l border-brand-border shadow-2xl z-50 flex flex-col transform transition-transform duration-300 ease-in-out",children:[l.jsxs("div",{className:"flex items-center gap-3 p-4 border-b border-brand-border bg-brand-surface",children:[l.jsx("button",{onClick:c,className:"text-brand-text-secondary hover:text-brand-text transition-colors p-1 rounded",title:"Close chat",children:l.jsx(Pm,{className:"w-5 h-5"})}),l.jsxs("div",{className:"flex items-center gap-2 flex-1 min-w-0",children:[l.jsx(Zr,{className:"w-5 h-5 text-brand-primary flex-shrink-0"}),l.jsxs("div",{className:"min-w-0 flex-1",children:[l.jsx("h1",{className:"text-lg font-semibold text-brand-text truncate",children:"Discussion"}),l.jsxs("p",{className:"text-xs text-brand-text-secondary truncate",children:[a.author.name," • ",a.side==="for"?"Supporting":"Challenging"]})]})]})]}),l.jsxs("div",{className:"bg-brand-surface/50 p-3 border-b border-brand-border",children:[l.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[l.jsx(Mt,{participant:a.author,size:"xs"}),l.jsxs("div",{className:"min-w-0 flex-1",children:[l.jsx("span",{className:"font-medium text-brand-text text-sm",children:a.author.name}),l.jsx("span",{className:`ml-2 text-xs px-1.5 py-0.5 rounded ${a.side==="for"?"bg-green-500/20 text-green-400":"bg-red-500/20 text-red-400"}`,children:a.side==="for"?"FOR":"AGAINST"})]})]}),l.jsx("p",{className:"text-brand-text text-sm leading-relaxed line-clamp-3",children:a.text})]}),l.jsxs("div",{className:"flex-1 overflow-y-auto p-3 space-y-2",children:[a.comments&&a.comments.length>0?a.comments.map(J=>l.jsx(xp,{comment:J,onReply:P,onVote:K,onReport:le,currentUserId:b==null?void 0:b.id},J.id)):l.jsxs("div",{className:"text-center py-8 text-brand-text-secondary",children:[l.jsx(Zr,{className:"w-8 h-8 mx-auto mb-2 opacity-50"}),l.jsx("p",{className:"text-sm",children:"No messages yet. Start the conversation!"})]}),l.jsx("div",{ref:X})]}),l.jsxs("div",{className:"border-t border-brand-border p-3 bg-brand-surface/30",children:[B&&$&&l.jsxs("div",{className:"bg-brand-surface/50 rounded-lg p-3 mb-3 border-l-4 border-brand-primary",children:[l.jsxs("div",{className:"flex items-center justify-between mb-1",children:[l.jsxs("span",{className:"text-xs text-brand-text-secondary",children:["Replying to ",$.author.name]}),l.jsx("button",{onClick:()=>O(null),className:"text-brand-text-secondary hover:text-brand-text text-xs",children:"Cancel"})]}),l.jsx("p",{className:"text-sm text-brand-text-light truncate",children:$.text})]}),l.jsx("form",{onSubmit:ae,className:"flex gap-3",children:l.jsxs("div",{className:"flex-1",children:[l.jsx("textarea",{ref:L,value:S,onChange:J=>U(J.target.value),placeholder:B?"Write a reply...":"Write a message...",className:"w-full bg-brand-surface border border-brand-surface-light rounded-lg p-3 text-brand-text resize-none focus:outline-none focus:ring-2 focus:ring-brand-primary/50",rows:2,maxLength:500,disabled:A}),l.jsxs("div",{className:"flex items-center justify-between mt-2",children:[l.jsxs("span",{className:"text-xs text-brand-text-secondary",children:[S.length,"/500"]}),l.jsx("button",{type:"submit",disabled:!S.trim()||A,className:"px-4 py-2 bg-brand-primary hover:bg-brand-primary/80 disabled:bg-brand-primary/30 text-white rounded-lg transition-colors text-sm font-medium",children:A?"Sending...":"Send"})]})]})})]})]})},z0=({references:a})=>l.jsxs("div",{children:[l.jsx("h4",{className:"text-sm font-semibold text-gray-400 uppercase tracking-wider mb-2",children:"Evidence"}),l.jsx("ul",{className:"space-y-1",children:a.map(r=>l.jsx("li",{children:l.jsxs("a",{href:r.url,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2 text-brand-secondary/80 hover:text-brand-secondary text-sm transition-colors",children:[l.jsx(bs,{className:"w-4 h-4"}),l.jsx("span",{children:r.title})]})},r.id))})]}),B0=({debate:a,onBack:r,onVote:c,onDebateVote:o,onAddSubArgument:d,onAddEvidence:h,onRefresh:m})=>{const{user:b}=xt(),{joinDebate:g,leaveDebate:x}=pp(),[y,S]=k.useState(!1),[U,B]=k.useState(!1),[O,A]=k.useState(!1),[T,X]=k.useState(null),[L,F]=k.useState(null),[ae,P]=k.useState([{id:"main-claim",text:a.claim.text,type:"claim"}]),K=a.supportVotes||0,le=a.opposeVotes||0,Q=K+le;let $=50,J=50;Q>0&&($=K/Q*100,J=le/Q*100);const xe=a.arguments.filter(ne=>ne.side===Te.FOR),de=a.arguments.filter(ne=>ne.side===Te.AGAINST),Ce=b&&(a.type==="open"||a.participants.some(ne=>ne.id===b.id));k.useEffect(()=>{if(b&&a.id)return g(a.id),()=>{x(a.id)}},[b,a.id,g,x]),console.log("DebateView - User authentication status:",{isAuthenticated:!!b,username:b==null?void 0:b.username,canAddArguments:Ce});const Ge=(ne,Se=a.arguments)=>{for(const oe of Se){if(oe.id===ne)return oe;if(oe.subArguments){const ye=Ge(ne,oe.subArguments);if(ye)return ye}}return null},qe=ne=>{const Se=[{id:"main-claim",text:a.claim.text,type:"claim"}],oe=(ye,Ee,be)=>{for(const Ye of ye){const me=[...be,{id:Ye.id,text:Ye.text,type:"argument"}];if(Ye.id===Ee.id)return Se.push(...me.slice(1)),!0;if(Ye.subArguments&&oe(Ye.subArguments,Ee,me))return!0}return!1};return oe(a.arguments,ne,[]),Se},N=ne=>{X(ne),P(qe(ne))},G=ne=>{if(ne===0)X(null),P([{id:"main-claim",text:a.claim.text,type:"claim"}]);else{const Se=ae[ne];if(Se.type==="argument"){const oe=Ge(Se.id);oe&&(X(oe),P(ae.slice(0,ne+1)))}}},te=async(ne,Se)=>{try{await Nt.voteOnArgument(a.id,ne,Se),m()}catch(oe){console.error("Error voting on argument:",oe)}},he=async(ne,Se,oe)=>{try{await Nt.addSubArgument(a.id,ne,{text:Se,isChallenge:oe}),m()}catch(ye){console.error("Error adding sub-argument:",ye)}},j=async(ne,Se)=>{var oe;try{if(console.log("Adding evidence:"),console.log("- debateId:",a.id),console.log("- argumentId:",ne),console.log("- evidence:",JSON.stringify(Se,null,2)),await Nt.addEvidence(a.id,ne,Se),await m(),T&&T.id===ne){const ye=Ge(ne);ye&&X(ye)}}catch(ye){console.error("Error adding evidence:",ye),console.error("Error details:",(oe=ye.response)==null?void 0:oe.data)}},Y=async(ne,Se,oe)=>{var ye,Ee,be,Ye;if(!b){console.error("User not authenticated"),alert("You must be logged in to vote on evidence");return}try{console.log("Voting on evidence:"),console.log("- debateId:",a.id),console.log("- argumentId:",ne),console.log("- evidenceId:",Se),console.log("- vote:",oe),console.log("- user:",b.username);const me=await Nt.voteOnEvidence(a.id,ne,Se,oe);if(console.log("Vote result:",me),await m(),T&&T.id===ne){const Oe=Ge(ne);Oe&&X(Oe)}}catch(me){console.error("Error voting on evidence:",me),console.error("Error details:",(ye=me.response)==null?void 0:ye.data),((Ee=me.response)==null?void 0:Ee.status)===401?alert("Authentication failed. Please log in again."):((be=me.response)==null?void 0:be.status)===400?alert(((Ye=me.response.data)==null?void 0:Ye.message)||"Invalid vote request"):alert("Failed to vote on evidence. Please try again.")}},ee=async(ne,Se,oe)=>{var ye;if(!b){console.error("User not authenticated"),alert("You must be logged in to comment");return}try{const Ee=await Nt.addComment(a.id,ne,{text:Se,parentCommentId:oe});if(T&&T.id===ne){const be=Ee.data,Ye={...T};if(Ye.comments||(Ye.comments=[]),oe){const me=Oe=>{for(const zt of Oe){if(zt.id===oe)return zt.replies||(zt.replies=[]),zt.replies.push(be),!0;if(zt.replies&&me(zt.replies))return!0}return!1};me(Ye.comments)}else Ye.comments.push(be);X(Ye)}if(L&&L.id===ne){const be={...L};if(be.comments||(be.comments=[]),oe){const Ye=me=>{for(const Oe of me){if(Oe.id===oe)return Oe.replies||(Oe.replies=[]),Oe.replies.push(Ee.data),!0;if(Oe.replies&&Ye(Oe.replies))return!0}return!1};Ye(be.comments)}else be.comments.push(Ee.data);F(be)}m()}catch(Ee){console.error("Error adding comment:",Ee),((ye=Ee.response)==null?void 0:ye.status)===401?alert("Authentication failed. Please log in again."):alert("Failed to add comment. Please try again.")}},W=async(ne,Se,oe)=>{var ye;if(!b){console.error("User not authenticated"),alert("You must be logged in to vote on comments");return}try{if(await Nt.voteOnComment(a.id,ne,Se,oe),T&&T.id===ne){const Ee={...T},be=Ye=>{for(const me of Ye){if(me.id===Se)return oe==="up"?me.upvotes=(me.upvotes||0)+1:me.downvotes=(me.downvotes||0)+1,me.votes=(me.upvotes||0)-(me.downvotes||0),!0;if(me.replies&&be(me.replies))return!0}return!1};Ee.comments&&(be(Ee.comments),X(Ee))}if(L&&L.id===ne){const Ee={...L},be=Ye=>{for(const me of Ye){if(me.id===Se)return oe==="up"?me.upvotes=(me.upvotes||0)+1:me.downvotes=(me.downvotes||0)+1,me.votes=(me.upvotes||0)-(me.downvotes||0),!0;if(me.replies&&be(me.replies))return!0}return!1};Ee.comments&&(be(Ee.comments),F(Ee))}m()}catch(Ee){console.error("Error voting on comment:",Ee),((ye=Ee.response)==null?void 0:ye.status)===401?alert("Authentication failed. Please log in again."):alert("Failed to vote on comment. Please try again.")}},R=async(ne,Se,oe)=>{A(!0);try{await Nt.addArgument(a.id,{text:ne,side:Se,references:oe}),m(),S(!1),B(!1)}catch(ye){throw console.error("Error adding argument:",ye),ye}finally{A(!1)}},Z=ne=>{F(ne)},I=()=>{F(null)},Pe=ne=>{F(ne)},De=async(ne,Se)=>{if(!b){console.error("User not authenticated");return}if(L&&L.id===ne){const oe={...L},ye=Ee=>{for(const be of Ee){if(be.id===Se)return be.reportedBy||(be.reportedBy=[]),be.reportedBy.includes(b.id)||be.reportedBy.push(b.id),!0;if(be.replies&&ye(be.replies))return!0}return!1};oe.comments&&(ye(oe.comments),F(oe))}try{await Nt.reportComment(a.id,ne,Se)}catch(oe){console.error("Failed to report comment:",oe)}};return l.jsxs("div",{className:"w-full p-4 md:p-8 relative",children:[l.jsxs("button",{onClick:r,className:"flex items-center gap-2 text-brand-text-light hover:text-white mb-8 transition-colors",children:[l.jsx(Pm,{className:"w-5 h-5"}),"Back to All Debates"]}),l.jsxs("div",{className:"bg-brand-surface rounded-xl shadow-lg p-6 mb-8",children:[l.jsx("h1",{className:"text-3xl font-extrabold text-white mb-2",children:a.title}),l.jsx("p",{className:"text-brand-text-light mb-6",children:a.description}),l.jsxs("div",{className:"bg-brand-bg/50 rounded-lg p-6",children:[l.jsxs("p",{className:"text-lg text-brand-text mb-4 italic",children:['"',a.claim.text,'"']}),l.jsxs("div",{className:"flex justify-between items-end",children:[a.claim.references.length>0&&l.jsx(z0,{references:a.claim.references}),l.jsxs("div",{className:"flex flex-col items-end",children:[l.jsx(Mt,{participant:a.claim.author}),l.jsx("span",{className:"text-sm font-semibold mt-2 text-brand-text-light",children:a.claim.author.name})]})]})]})]}),l.jsxs("div",{className:"mb-8",children:[l.jsx("h2",{className:"text-xl font-bold text-center mb-4",children:"Overall Vote"}),l.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[l.jsxs("div",{className:"flex flex-col items-center",children:[l.jsx("button",{onClick:()=>o(ol.SUPPORT),className:"bg-success/80 hover:bg-success text-white font-bold py-3 px-6 rounded-lg transition-colors w-32 text-center mb-2",children:"SUPPORT"}),l.jsx("span",{className:"text-success font-bold text-lg",children:K})]}),l.jsxs("div",{className:"w-full bg-brand-surface rounded-full h-6 flex overflow-hidden",children:[l.jsx("div",{className:"bg-success h-6 transition-all duration-300",style:{width:`${$}%`}}),l.jsx("div",{className:"bg-danger h-6 transition-all duration-300",style:{width:`${J}%`}})]}),l.jsxs("div",{className:"flex flex-col items-center",children:[l.jsx("button",{onClick:()=>o(ol.OPPOSE),className:"bg-danger/80 hover:bg-danger text-white font-bold py-3 px-6 rounded-lg transition-colors w-32 text-center mb-2",children:"OPPOSE"}),l.jsx("span",{className:"text-danger font-bold text-lg",children:le})]})]})]}),a.isLive&&l.jsx("div",{className:"mb-12",children:l.jsx(My,{proponent:a.proponent,opponent:a.opponent})}),l.jsx(qy,{breadcrumbPath:ae,onNavigate:G}),T?l.jsx(ky,{argument:T,onVote:te,onAddSubArgument:he,onAddEvidence:j,onVoteOnEvidence:Y,onArgumentClick:N,canAddArguments:Ce,onRefresh:m,currentUserId:b==null?void 0:b.id,onChatClick:Z}):l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 items-stretch",children:[l.jsxs("div",{className:"flex flex-col h-full",children:[l.jsxs("div",{className:"flex items-center justify-between mb-4",children:[l.jsx("h3",{className:"text-2xl font-bold border-l-4 border-success pl-3 text-success",children:"Arguments For"}),Ce&&!y&&l.jsxs("button",{onClick:()=>S(!0),className:"flex items-center gap-2 px-4 py-2 bg-success/20 hover:bg-success/30 text-success border border-success/50 rounded-lg transition-colors",children:[l.jsx(Fn,{className:"w-4 h-4"}),"Add Argument"]})]}),l.jsxs("div",{className:"space-y-4",children:[y&&l.jsx(ko,{side:Te.FOR,onSubmit:R,onCancel:()=>S(!1),isSubmitting:O}),xe.length>0?xe.map(ne=>l.jsx(bm,{argument:ne,onVote:te,onAddSubArgument:he,onAddEvidence:j,onVoteOnEvidence:Y,onArgumentClick:N,onChatClick:Z,currentUserId:b==null?void 0:b.id},ne.id)):!y&&l.jsx("p",{className:"text-brand-text-light italic",children:"No arguments for this side yet."})]})]}),l.jsxs("div",{className:"flex flex-col h-full",children:[l.jsxs("div",{className:"flex items-center justify-between mb-4",children:[l.jsx("h3",{className:"text-2xl font-bold border-l-4 border-danger pl-3 text-danger",children:"Arguments Against"}),Ce&&!U&&l.jsxs("button",{onClick:()=>B(!0),className:"flex items-center gap-2 px-4 py-2 bg-danger/20 hover:bg-danger/30 text-danger border border-danger/50 rounded-lg transition-colors",children:[l.jsx(Fn,{className:"w-4 h-4"}),"Add Argument"]})]}),l.jsxs("div",{className:"space-y-4",children:[U&&l.jsx(ko,{side:Te.AGAINST,onSubmit:R,onCancel:()=>B(!1),isSubmitting:O}),de.length>0?de.map(ne=>l.jsx(bm,{argument:ne,onVote:te,onAddSubArgument:he,onAddEvidence:j,onVoteOnEvidence:Y,onArgumentClick:N,onChatClick:Z,currentUserId:b==null?void 0:b.id},ne.id)):!U&&l.jsx("p",{className:"text-brand-text-light italic",children:"No arguments for this side yet."})]})]})]}),L&&l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"fixed inset-0 bg-black/20 z-40",onClick:I}),l.jsx(M0,{argument:L,debateId:a.id,onBack:I,onAddComment:ee,onVoteOnComment:W,onReportComment:De,onArgumentUpdate:Pe})]})]})},Jr=[{id:"politics",name:"Politics",description:"Political discussions and policy debates",icon:"🏛️",color:"bg-blue-600"},{id:"technology",name:"Technology",description:"Tech trends, AI, and digital innovation",icon:"💻",color:"bg-purple-600"},{id:"science",name:"Science",description:"Scientific theories and discoveries",icon:"🔬",color:"bg-green-600"},{id:"philosophy",name:"Philosophy",description:"Philosophical questions and ethics",icon:"🤔",color:"bg-indigo-600"},{id:"economics",name:"Economics",description:"Economic policies and market discussions",icon:"💰",color:"bg-yellow-600"},{id:"environment",name:"Environment",description:"Climate change and environmental issues",icon:"🌍",color:"bg-emerald-600"},{id:"health",name:"Health",description:"Healthcare, medicine, and wellness",icon:"🏥",color:"bg-red-600"},{id:"education",name:"Education",description:"Educational systems and learning",icon:"📚",color:"bg-orange-600"},{id:"sports",name:"Sports",description:"Sports debates and athletic discussions",icon:"⚽",color:"bg-teal-600"},{id:"entertainment",name:"Entertainment",description:"Movies, music, and pop culture",icon:"🎬",color:"bg-pink-600"}];Jr.map(a=>a.id);const k0=({onSuccess:a,onCancel:r})=>{const{user:c}=xt(),{validateArgument:o}=Im(),[d,h]=k.useState({title:"",category:"",type:"open",claim:{text:"",references:[]},opponent:{id:"",name:"",avatarUrl:""}}),[m,b]=k.useState(!1),[g,x]=k.useState(null),[y,S]=k.useState(null),[U,B]=k.useState(!1),[O,A]=k.useState(""),[T,X]=k.useState([]),[L,F]=k.useState(!1),[ae,P]=k.useState(!1),[K,le]=k.useState({url:"",title:"",description:"",type:"other"}),Q=async N=>{if(N.length<2){X([]);return}F(!0);try{const G=await Xr.getUsers({search:N,limit:10});if(G.success){const te=G.data.filter(he=>he.id!==(c==null?void 0:c.id));X(te)}}catch(G){console.error("Error searching opponents:",G)}finally{F(!1)}};k.useEffect(()=>{const N=setTimeout(()=>{O&&Q(O)},300);return()=>clearTimeout(N)},[O]);const $=(N,G)=>{if(N.startsWith("claim.")){const te=N.split(".")[1];h(he=>({...he,claim:{...he.claim,[te]:G}}))}else h(te=>({...te,[N]:G}))},J=N=>{h(G=>({...G,opponent:{id:N.id,name:N.name,avatarUrl:N.avatarUrl||`https://picsum.photos/seed/${N.username}/40`}})),B(!1),A(""),X([])},xe=()=>{if(!K.url||!K.title)return;const N={id:Date.now().toString(),url:K.url,title:K.title,description:K.description,type:K.type,credibilityScore:5};h(G=>({...G,claim:{...G.claim,references:[...G.claim.references,N]}})),le({url:"",title:"",description:"",type:"other"}),P(!1)},de=N=>{h(G=>({...G,claim:{...G.claim,references:G.claim.references.filter(te=>te.id!==N)}}))},Ce=()=>{const N=[];return d.title.length<5&&N.push("Title must be at least 5 characters long"),d.title.length>200&&N.push("Title must be less than 200 characters"),d.category||N.push("Please select a category for your debate"),d.claim.text.length<20&&N.push("Main position must be at least 20 characters long"),d.claim.text.length>800&&N.push("Main position must be less than 800 characters"),d.type==="one-on-one"&&!d.opponent.id&&N.push("Please select an opponent for one-on-one debates"),N},Ge=async N=>{var te,he;N.preventDefault();const G=Ce();if(G.length>0){x(G.join(", "));return}b(!0),x(null);try{const j={title:d.title,description:d.claim.text,category:d.category,type:d.type,claim:d.claim,opponent:d.type==="one-on-one"?d.opponent:void 0},Y=await Nt.createDebate(j);Y.success?(S("Debate created successfully!"),setTimeout(()=>{a(Y.data.id)},1e3)):x(Y.message||"Failed to create debate")}catch(j){x(((he=(te=j.response)==null?void 0:te.data)==null?void 0:he.message)||"Failed to create debate")}finally{b(!1)}},qe=o(d.claim.text);return l.jsx("div",{className:"w-full p-4 md:p-8",children:l.jsxs("div",{className:"bg-brand-surface rounded-xl shadow-lg p-6",children:[l.jsxs("div",{className:"flex items-center justify-between mb-6",children:[l.jsx("h2",{className:"text-3xl font-bold text-white",children:"Create New Debate"}),l.jsx("button",{onClick:r,className:"text-brand-text-secondary hover:text-white transition-colors",children:l.jsx(Kr,{className:"w-6 h-6"})})]}),g&&l.jsx("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6",children:l.jsx("p",{className:"text-red-400",children:g})}),y&&l.jsx("div",{className:"bg-green-500/10 border border-green-500/20 rounded-lg p-4 mb-6",children:l.jsx("p",{className:"text-green-400",children:y})}),l.jsxs("form",{onSubmit:Ge,className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text mb-2",children:"Debate Title *"}),l.jsx("input",{type:"text",value:d.title,onChange:N=>$("title",N.target.value),className:"w-full px-4 py-3 bg-brand-bg border border-gray-600 rounded-lg text-brand-text placeholder-brand-text-secondary focus:outline-none focus:ring-2 focus:ring-brand-primary focus:border-transparent",placeholder:"Enter a compelling debate title...",maxLength:200,required:!0}),l.jsxs("div",{className:"flex justify-between mt-1",children:[l.jsx("span",{className:"text-xs text-brand-text-secondary",children:d.title.length<5?`Need ${5-d.title.length} more characters`:"Good length"}),l.jsxs("span",{className:"text-xs text-brand-text-secondary",children:[d.title.length,"/200"]})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text mb-2",children:"Category *"}),l.jsxs("select",{value:d.category,onChange:N=>$("category",N.target.value),className:"w-full px-4 py-3 bg-brand-bg border border-gray-600 rounded-lg text-brand-text focus:outline-none focus:ring-2 focus:ring-brand-primary focus:border-transparent",required:!0,children:[l.jsx("option",{value:"",children:"Select a category"}),Jr.map(N=>l.jsxs("option",{value:N.id,children:[N.icon," ",N.name," - ",N.description]},N.id))]}),l.jsx("p",{className:"text-xs text-brand-text-secondary mt-1",children:"Categories help organize debates and make them easier to discover"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text mb-2",children:"Main Position *"}),l.jsx(zo,{errors:qe.errors,warnings:qe.warnings}),l.jsx("textarea",{value:d.claim.text,onChange:N=>$("claim.text",N.target.value),className:"w-full px-4 py-3 bg-brand-bg border border-gray-600 rounded-lg text-brand-text placeholder-brand-text-secondary focus:outline-none focus:ring-2 focus:ring-brand-primary focus:border-transparent",placeholder:"State your main position or claim clearly. This is what the debate will be about...",rows:4,maxLength:800,required:!0}),l.jsxs("div",{className:"flex justify-between mt-1",children:[l.jsx("span",{className:"text-xs text-brand-text-secondary",children:d.claim.text.length<20?`Need ${20-d.claim.text.length} more characters`:"Good length"}),l.jsxs("span",{className:"text-xs text-brand-text-secondary",children:[d.claim.text.length,"/800"]})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text mb-2",children:"Debate Type *"}),l.jsxs("div",{className:"space-y-3",children:[l.jsxs("label",{className:"flex items-start gap-3 cursor-pointer",children:[l.jsx("input",{type:"radio",name:"debateType",value:"open",checked:d.type==="open",onChange:N=>{$("type",N.target.value),N.target.value==="open"&&(h(G=>({...G,opponent:{id:"",name:"",avatarUrl:""}})),A(""),B(!1))},className:"mt-1 text-brand-primary focus:ring-brand-primary"}),l.jsxs("div",{children:[l.jsx("div",{className:"font-medium text-brand-text",children:"Open Debate"}),l.jsx("div",{className:"text-sm text-brand-text-secondary",children:"Anyone can join and add arguments to support either side. Great for exploring different perspectives on a topic."})]})]}),l.jsxs("label",{className:"flex items-start gap-3 cursor-pointer",children:[l.jsx("input",{type:"radio",name:"debateType",value:"one-on-one",checked:d.type==="one-on-one",onChange:N=>$("type",N.target.value),className:"mt-1 text-brand-primary focus:ring-brand-primary"}),l.jsxs("div",{children:[l.jsx("div",{className:"font-medium text-brand-text",children:"One-on-One Debate"}),l.jsx("div",{className:"text-sm text-brand-text-secondary",children:"A structured debate between you and a specific opponent. Only participants can add arguments, others can vote."})]})]})]})]}),l.jsxs("div",{children:[l.jsxs("div",{className:"flex items-center justify-between mb-3",children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text",children:"Supporting References"}),l.jsxs("button",{type:"button",onClick:()=>P(!0),className:"flex items-center gap-1 text-brand-primary hover:text-brand-primary-light text-sm transition-colors",children:[l.jsx(Fn,{className:"w-4 h-4"}),"Add Reference"]})]}),d.claim.references.length>0&&l.jsx("div",{className:"space-y-2 mb-4",children:d.claim.references.map(N=>l.jsxs("div",{className:"bg-brand-bg/50 rounded-lg p-3 flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(bs,{className:"w-4 h-4 text-brand-primary"}),l.jsxs("div",{children:[l.jsx("p",{className:"text-sm font-medium text-brand-text",children:N.title}),l.jsx("p",{className:"text-xs text-brand-text-secondary",children:N.url})]})]}),l.jsx("button",{type:"button",onClick:()=>de(N.id),className:"text-red-400 hover:text-red-300 transition-colors",children:l.jsx(Kr,{className:"w-4 h-4"})})]},N.id))}),ae&&l.jsx("div",{className:"bg-brand-bg/30 rounded-lg p-4 mb-4",children:l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-xs font-medium text-brand-text mb-1",children:"URL *"}),l.jsx("input",{type:"url",value:K.url,onChange:N=>le(G=>({...G,url:N.target.value})),className:"w-full px-3 py-2 bg-brand-bg border border-gray-600 rounded text-brand-text text-sm focus:outline-none focus:ring-1 focus:ring-brand-primary",placeholder:"https://example.com/source",required:!0})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-xs font-medium text-brand-text mb-1",children:"Title *"}),l.jsx("input",{type:"text",value:K.title,onChange:N=>le(G=>({...G,title:N.target.value})),className:"w-full px-3 py-2 bg-brand-bg border border-gray-600 rounded text-brand-text text-sm focus:outline-none focus:ring-1 focus:ring-brand-primary",placeholder:"Reference title",required:!0})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-xs font-medium text-brand-text mb-1",children:"Type"}),l.jsxs("select",{value:K.type,onChange:N=>le(G=>({...G,type:N.target.value})),className:"w-full px-3 py-2 bg-brand-bg border border-gray-600 rounded text-brand-text text-sm focus:outline-none focus:ring-1 focus:ring-brand-primary",children:[l.jsx("option",{value:"academic",children:"Academic"}),l.jsx("option",{value:"news",children:"News"}),l.jsx("option",{value:"government",children:"Government"}),l.jsx("option",{value:"organization",children:"Organization"}),l.jsx("option",{value:"other",children:"Other"})]})]}),l.jsxs("div",{className:"flex gap-2",children:[l.jsx("button",{type:"button",onClick:xe,disabled:!K.url||!K.title,className:"px-3 py-1 bg-brand-primary text-white rounded text-sm hover:bg-brand-primary-dark disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"Add"}),l.jsx("button",{type:"button",onClick:()=>P(!1),className:"px-3 py-1 border border-gray-600 text-brand-text rounded text-sm hover:bg-gray-700 transition-colors",children:"Cancel"})]})]})})]}),d.type==="one-on-one"&&l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text mb-2",children:"Select Opponent *"}),d.opponent.id?l.jsxs("div",{className:"bg-brand-bg/50 rounded-lg p-4 flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center gap-3",children:[l.jsx("img",{src:d.opponent.avatarUrl,alt:d.opponent.name,className:"w-10 h-10 rounded-full"}),l.jsxs("div",{children:[l.jsx("p",{className:"font-medium text-brand-text",children:d.opponent.name}),l.jsx("p",{className:"text-sm text-brand-text-secondary",children:"Selected opponent"})]})]}),l.jsx("button",{type:"button",onClick:()=>{h(N=>({...N,opponent:{id:"",name:"",avatarUrl:""}})),B(!0)},className:"text-brand-primary hover:text-brand-primary-light text-sm transition-colors",children:"Change"})]}):l.jsxs("div",{className:"relative",children:[l.jsx("input",{type:"text",value:O,onChange:N=>{A(N.target.value),B(!0)},onFocus:()=>B(!0),className:"w-full px-4 py-3 bg-brand-bg border border-gray-600 rounded-lg text-brand-text placeholder-brand-text-secondary focus:outline-none focus:ring-2 focus:ring-brand-primary focus:border-transparent",placeholder:"Search for users to debate with..."}),U&&l.jsx("div",{className:"absolute top-full left-0 right-0 mt-1 bg-brand-surface border border-gray-600 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto",children:L?l.jsx("div",{className:"p-4 text-center text-brand-text-secondary",children:"Searching..."}):T.length>0?T.map(N=>l.jsxs("button",{type:"button",onClick:()=>J(N),className:"w-full p-3 text-left hover:bg-brand-bg/50 transition-colors flex items-center gap-3",children:[l.jsx("img",{src:N.avatarUrl||`https://picsum.photos/seed/${N.username}/40`,alt:N.name,className:"w-8 h-8 rounded-full"}),l.jsxs("div",{children:[l.jsx("p",{className:"font-medium text-brand-text",children:N.name}),l.jsxs("p",{className:"text-sm text-brand-text-secondary",children:["@",N.username]})]})]},N.id)):O.length>=2?l.jsxs("div",{className:"p-4 text-center text-brand-text-secondary",children:['No users found matching "',O,'"']}):l.jsx("div",{className:"p-4 text-center text-brand-text-secondary",children:"Type at least 2 characters to search"})})]})]}),l.jsx(Bo,{}),l.jsxs("div",{className:"flex gap-4 pt-6",children:[l.jsx("button",{type:"button",onClick:r,className:"flex-1 px-6 py-3 border border-gray-600 text-brand-text rounded-lg hover:bg-gray-700 transition-colors",disabled:m,children:"Cancel"}),l.jsx("button",{type:"submit",disabled:m||Ce().length>0,className:"flex-1 px-6 py-3 bg-brand-primary text-white rounded-lg hover:bg-brand-primary-dark disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:m?"Creating Debate...":"Create Debate"})]})]})]})})},q0=({debates:a,onSelect:r})=>{const[c,o]=k.useState(0),[d,h]=k.useState(!0),m=a.slice().sort((y,S)=>{var O,A;const U=(y.supportVotes||0)+(y.opposeVotes||0)+(((O=y.arguments)==null?void 0:O.length)||0);return(S.supportVotes||0)+(S.opposeVotes||0)+(((A=S.arguments)==null?void 0:A.length)||0)-U}).slice(0,5);k.useEffect(()=>{if(!d||m.length<=1)return;const y=setInterval(()=>{o(S=>(S+1)%m.length)},5e3);return()=>clearInterval(y)},[d,m.length]);const b=y=>{o(y),h(!1),setTimeout(()=>h(!0),1e4)},g=()=>{o(y=>(y-1+m.length)%m.length),h(!1),setTimeout(()=>h(!0),1e4)},x=()=>{o(y=>(y+1)%m.length),h(!1),setTimeout(()=>h(!0),1e4)};return m.length===0?null:l.jsxs("div",{className:"mb-8",children:[l.jsxs("div",{className:"flex items-center justify-between mb-4",children:[l.jsxs("h2",{className:"text-2xl font-bold text-white flex items-center gap-2",children:[l.jsx("span",{className:"text-yellow-400",children:"⭐"}),"Featured Debates"]}),l.jsxs("div",{className:"flex items-center gap-2 text-sm text-brand-text-secondary",children:[l.jsx("span",{className:`w-2 h-2 rounded-full ${d?"bg-green-400":"bg-gray-400"}`}),d?"Auto-playing":"Paused"]})]}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"overflow-hidden rounded-xl",children:l.jsx("div",{className:"flex transition-transform duration-500 ease-in-out",style:{transform:`translateX(-${c*100}%)`},children:m.map((y,S)=>{var U;return l.jsx("div",{className:"w-full flex-shrink-0",children:l.jsxs("div",{className:"relative",children:[l.jsx(Qr,{debate:y,onSelect:r}),l.jsxs("div",{className:"absolute top-4 right-4 bg-yellow-500 text-black px-3 py-1 rounded-full text-sm font-bold flex items-center gap-1",children:[l.jsx("span",{children:"⭐"}),"Featured"]}),l.jsxs("div",{className:"absolute bottom-4 left-4 bg-black/70 text-white px-3 py-1 rounded-lg text-sm",children:[(y.supportVotes||0)+(y.opposeVotes||0)," votes • ",((U=y.arguments)==null?void 0:U.length)||0," arguments"]})]})},y.id)})})}),m.length>1&&l.jsxs(l.Fragment,{children:[l.jsx("button",{onClick:g,className:"absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors z-10","aria-label":"Previous debate",children:l.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),l.jsx("button",{onClick:x,className:"absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors z-10","aria-label":"Next debate",children:l.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),m.length>1&&l.jsx("div",{className:"flex justify-center mt-4 gap-2",children:m.map((y,S)=>l.jsx("button",{onClick:()=>b(S),className:`w-3 h-3 rounded-full transition-colors ${S===c?"bg-brand-primary":"bg-gray-600 hover:bg-gray-500"}`,"aria-label":`Go to slide ${S+1}`},S))})]})]})},L0=({onCategorySelect:a,selectedCategory:r})=>{const[c,o]=k.useState([]),[d,h]=k.useState(!0),[m,b]=k.useState(null);k.useEffect(()=>{g()},[]);const g=async()=>{try{h(!0);const x=await Promise.all(Jr.map(async y=>{var S;try{const U=await Nt.getDebates({category:y.id,limit:1});return{...y,debateCount:((S=U.pagination)==null?void 0:S.total)||0}}catch{return{...y,debateCount:0}}}));o(x)}catch{b("Failed to load categories"),o(Jr.map(y=>({...y,debateCount:0})))}finally{h(!1)}};return d?l.jsxs("div",{className:"mb-8",children:[l.jsx("h2",{className:"text-2xl font-bold text-white mb-4",children:"Browse Categories"}),l.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4",children:[...Array(10)].map((x,y)=>l.jsxs("div",{className:"bg-brand-surface rounded-lg p-4 animate-pulse",children:[l.jsx("div",{className:"w-12 h-12 bg-gray-600 rounded-lg mb-3"}),l.jsx("div",{className:"h-4 bg-gray-600 rounded mb-2"}),l.jsx("div",{className:"h-3 bg-gray-700 rounded"})]},y))})]}):m?l.jsx("div",{className:"mb-8",children:l.jsx("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4",children:l.jsx("p",{className:"text-red-400",children:m})})}):l.jsxs("div",{className:"mb-8",children:[l.jsxs("div",{className:"flex items-center justify-between mb-4",children:[l.jsx("h2",{className:"text-2xl font-bold text-white",children:"Browse Categories"}),r&&l.jsx("button",{onClick:()=>a(null),className:"text-brand-primary hover:text-brand-primary-light text-sm font-medium",children:"Clear Filter"})]}),l.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4",children:c.map(x=>l.jsxs("button",{onClick:()=>a(x.id),className:`
              group relative overflow-hidden rounded-lg p-4 text-left transition-all duration-200
              ${r===x.id?"ring-2 ring-brand-primary bg-brand-primary/10":"bg-brand-surface hover:bg-brand-surface-light hover:scale-105"}
            `,children:[l.jsx("div",{className:`absolute inset-0 ${x.color} opacity-10 group-hover:opacity-20 transition-opacity`}),l.jsxs("div",{className:"relative z-10",children:[l.jsxs("div",{className:"flex items-center justify-between mb-3",children:[l.jsx("div",{className:"text-3xl",children:x.icon}),r===x.id&&l.jsx("div",{className:"w-2 h-2 bg-brand-primary rounded-full"})]}),l.jsx("h3",{className:"font-bold text-white mb-1 group-hover:text-brand-primary transition-colors",children:x.name}),l.jsx("p",{className:"text-xs text-brand-text-secondary mb-2 line-clamp-2",children:x.description}),l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("span",{className:"text-xs font-medium text-brand-text-light",children:[x.debateCount," debates"]}),x.debateCount>0&&l.jsx("div",{className:"w-1 h-1 bg-green-400 rounded-full"})]})]})]},x.id))}),r&&l.jsx("div",{className:"mt-4 p-4 bg-brand-primary/10 border border-brand-primary/20 rounded-lg",children:(()=>{const x=c.find(y=>y.id===r);return x?l.jsxs("div",{className:"flex items-center gap-3",children:[l.jsx("span",{className:"text-2xl",children:x.icon}),l.jsxs("div",{children:[l.jsx("h3",{className:"font-bold text-white",children:x.name}),l.jsx("p",{className:"text-sm text-brand-text-secondary",children:x.description})]})]}):null})()})]})},H0=({onSuccess:a,onSwitchToRegister:r})=>{const{login:c,isLoading:o,error:d,clearError:h}=xt(),[m,b]=k.useState({email:"",password:""}),[g,x]=k.useState({}),y=()=>{const B={};return m.email?/\S+@\S+\.\S+/.test(m.email)||(B.email="Email is invalid"):B.email="Email is required",m.password||(B.password="Password is required"),x(B),Object.keys(B).length===0},S=async B=>{if(B.preventDefault(),h(),!!y())try{await c(m),a==null||a()}catch{}},U=B=>{const{name:O,value:A}=B.target;b(T=>({...T,[O]:A})),g[O]&&x(T=>({...T,[O]:void 0}))};return l.jsxs("div",{className:"max-w-md mx-auto bg-brand-surface rounded-lg p-6 shadow-lg",children:[l.jsx("h2",{className:"text-2xl font-bold text-white mb-6 text-center",children:"Sign In"}),d&&l.jsx("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4",children:l.jsx("p",{className:"text-red-400 text-sm",children:d})}),l.jsxs("form",{onSubmit:S,className:"space-y-4",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-brand-text-light mb-1",children:"Email"}),l.jsx("input",{type:"email",id:"email",name:"email",value:m.email,onChange:U,className:`w-full px-3 py-2 bg-brand-bg border rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-primary ${g.email?"border-red-500":"border-brand-surface-light"}`,placeholder:"Enter your email"}),g.email&&l.jsx("p",{className:"text-red-400 text-xs mt-1",children:g.email})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-brand-text-light mb-1",children:"Password"}),l.jsx("input",{type:"password",id:"password",name:"password",value:m.password,onChange:U,className:`w-full px-3 py-2 bg-brand-bg border rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-primary ${g.password?"border-red-500":"border-brand-surface-light"}`,placeholder:"Enter your password"}),g.password&&l.jsx("p",{className:"text-red-400 text-xs mt-1",children:g.password})]}),l.jsx("button",{type:"submit",disabled:o,className:"w-full bg-brand-primary hover:bg-brand-primary-dark text-white font-medium py-2 px-4 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:o?"Signing In...":"Sign In"})]}),r&&l.jsx("div",{className:"mt-6 text-center",children:l.jsxs("p",{className:"text-brand-text-light text-sm",children:["Don't have an account?"," ",l.jsx("button",{onClick:r,className:"text-brand-primary hover:text-brand-primary-light transition-colors",children:"Sign up"})]})})]})},Y0=({onSuccess:a,onSwitchToLogin:r})=>{const{register:c,isLoading:o,error:d,clearError:h}=xt(),[m,b]=k.useState({username:"",email:"",password:"",firstName:"",lastName:""}),[g,x]=k.useState(""),[y,S]=k.useState({}),U=()=>{const A={};return m.username?m.username.length<3?A.username="Username must be at least 3 characters":/^[a-zA-Z0-9_]+$/.test(m.username)||(A.username="Username can only contain letters, numbers, and underscores"):A.username="Username is required",m.email?/\S+@\S+\.\S+/.test(m.email)||(A.email="Email is invalid"):A.email="Email is required",m.password?m.password.length<6&&(A.password="Password must be at least 6 characters"):A.password="Password is required",g?m.password!==g&&(A.confirmPassword="Passwords do not match"):A.confirmPassword="Please confirm your password",m.firstName||(A.firstName="First name is required"),m.lastName||(A.lastName="Last name is required"),S(A),Object.keys(A).length===0},B=async A=>{if(A.preventDefault(),h(),!!U())try{await c(m),a==null||a()}catch{}},O=A=>{const{name:T,value:X}=A.target;T==="confirmPassword"?x(X):b(L=>({...L,[T]:X})),y[T]&&S(L=>({...L,[T]:void 0}))};return l.jsxs("div",{className:"max-w-md mx-auto bg-brand-surface rounded-lg p-6 shadow-lg",children:[l.jsx("h2",{className:"text-2xl font-bold text-white mb-6 text-center",children:"Create Account"}),d&&l.jsx("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4",children:l.jsx("p",{className:"text-red-400 text-sm",children:d})}),l.jsxs("form",{onSubmit:B,className:"space-y-4",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-brand-text-light mb-1",children:"Username"}),l.jsx("input",{type:"text",id:"username",name:"username",value:m.username,onChange:O,className:`w-full px-3 py-2 bg-brand-bg border rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-primary ${y.username?"border-red-500":"border-brand-surface-light"}`,placeholder:"Choose a username"}),y.username&&l.jsx("p",{className:"text-red-400 text-xs mt-1",children:y.username})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"firstName",className:"block text-sm font-medium text-brand-text-light mb-1",children:"First Name"}),l.jsx("input",{type:"text",id:"firstName",name:"firstName",value:m.firstName,onChange:O,className:`w-full px-3 py-2 bg-brand-bg border rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-primary ${y.firstName?"border-red-500":"border-brand-surface-light"}`,placeholder:"First name"}),y.firstName&&l.jsx("p",{className:"text-red-400 text-xs mt-1",children:y.firstName})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"lastName",className:"block text-sm font-medium text-brand-text-light mb-1",children:"Last Name"}),l.jsx("input",{type:"text",id:"lastName",name:"lastName",value:m.lastName,onChange:O,className:`w-full px-3 py-2 bg-brand-bg border rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-primary ${y.lastName?"border-red-500":"border-brand-surface-light"}`,placeholder:"Last name"}),y.lastName&&l.jsx("p",{className:"text-red-400 text-xs mt-1",children:y.lastName})]})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-brand-text-light mb-1",children:"Email"}),l.jsx("input",{type:"email",id:"email",name:"email",value:m.email,onChange:O,className:`w-full px-3 py-2 bg-brand-bg border rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-primary ${y.email?"border-red-500":"border-brand-surface-light"}`,placeholder:"Enter your email"}),y.email&&l.jsx("p",{className:"text-red-400 text-xs mt-1",children:y.email})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-brand-text-light mb-1",children:"Password"}),l.jsx("input",{type:"password",id:"password",name:"password",value:m.password,onChange:O,className:`w-full px-3 py-2 bg-brand-bg border rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-primary ${y.password?"border-red-500":"border-brand-surface-light"}`,placeholder:"Create a password"}),y.password&&l.jsx("p",{className:"text-red-400 text-xs mt-1",children:y.password})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-brand-text-light mb-1",children:"Confirm Password"}),l.jsx("input",{type:"password",id:"confirmPassword",name:"confirmPassword",value:g,onChange:O,className:`w-full px-3 py-2 bg-brand-bg border rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-primary ${y.confirmPassword?"border-red-500":"border-brand-surface-light"}`,placeholder:"Confirm your password"}),y.confirmPassword&&l.jsx("p",{className:"text-red-400 text-xs mt-1",children:y.confirmPassword})]}),l.jsx("button",{type:"submit",disabled:o,className:"w-full bg-brand-primary hover:bg-brand-primary-dark text-white font-medium py-2 px-4 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:o?"Creating Account...":"Create Account"})]}),r&&l.jsx("div",{className:"mt-6 text-center",children:l.jsxs("p",{className:"text-brand-text-light text-sm",children:["Already have an account?"," ",l.jsx("button",{onClick:r,className:"text-brand-primary hover:text-brand-primary-light transition-colors",children:"Sign in"})]})})]})},V0=({isOpen:a,onClose:r,initialMode:c="login"})=>{const[o,d]=k.useState(c);if(!a)return null;const h=()=>{r()},m=b=>{b.target===b.currentTarget&&r()};return l.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",onClick:m,children:l.jsxs("div",{className:"relative max-w-md w-full",children:[l.jsx("button",{onClick:r,className:"absolute -top-2 -right-2 bg-brand-surface hover:bg-brand-surface-light text-white rounded-full w-8 h-8 flex items-center justify-center transition-colors z-10",children:"×"}),o==="login"?l.jsx(H0,{onSuccess:h,onSwitchToRegister:()=>d("register")}):l.jsx(Y0,{onSuccess:h,onSwitchToLogin:()=>d("login")})]})})},rn=a=>{if(!a.privacySettings)return a.firstName&&a.lastName?`${a.firstName} ${a.lastName}`:a.firstName||a.username;switch(a.privacySettings.displayName){case"firstName":return a.firstName||a.username;case"fullName":return a.firstName&&a.lastName?`${a.firstName} ${a.lastName}`:a.firstName||a.username;case"username":default:return a.username}},G0=({onNavigate:a})=>{const{user:r,logout:c}=xt(),[o,d]=k.useState(!1),h=k.useRef(null);if(k.useEffect(()=>{const g=x=>{h.current&&!h.current.contains(x.target)&&d(!1)};return document.addEventListener("mousedown",g),()=>document.removeEventListener("mousedown",g)},[]),!r)return null;const m=g=>{d(!1),a==null||a(g)},b=()=>{d(!1),c()};return l.jsxs("div",{className:"relative",ref:h,children:[l.jsxs("button",{onClick:()=>d(!o),className:"flex items-center space-x-2 text-white hover:text-brand-primary transition-colors",children:[l.jsx("span",{className:"hidden md:block flex-1 text-right",children:rn(r)}),l.jsx("svg",{className:`w-4 h-4 transition-transform ${o?"rotate-180":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})}),l.jsx("img",{src:r.avatarUrl||`https://picsum.photos/seed/${r.username}/40`,alt:rn(r),className:"w-8 h-8 rounded-full"})]}),o&&l.jsxs("div",{className:"absolute right-0 mt-2 w-48 bg-brand-surface border border-brand-surface-light rounded-lg shadow-lg py-1 z-50",children:[l.jsxs("div",{className:"px-4 py-2 border-b border-brand-surface-light",children:[l.jsx("p",{className:"text-sm font-medium text-white",children:rn(r)}),l.jsxs("p",{className:"text-xs text-brand-text-light",children:["@",r.username]}),l.jsxs("p",{className:"text-xs text-brand-text-light",children:["Reputation: ",r.reputation]})]}),l.jsx("button",{onClick:()=>m("dashboard"),className:"w-full text-left px-4 py-2 text-sm text-brand-text hover:bg-brand-surface-light transition-colors",children:"Dashboard"}),l.jsx("button",{onClick:()=>m("profile"),className:"w-full text-left px-4 py-2 text-sm text-brand-text hover:bg-brand-surface-light transition-colors",children:"Profile"}),r.role==="admin"&&l.jsx("button",{onClick:()=>m("admin"),className:"w-full text-left px-4 py-2 text-sm text-purple-400 hover:bg-brand-surface-light transition-colors",children:"Admin Dashboard"}),l.jsx("button",{onClick:()=>m("debates"),className:"w-full text-left px-4 py-2 text-sm text-brand-text hover:bg-brand-surface-light transition-colors",children:"Browse Debates"}),l.jsx("button",{onClick:b,className:"w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-brand-surface-light transition-colors",children:"Sign Out"})]})]})},X0=({userId:a})=>{var ae,P;const{user:r,updateUser:c}=xt(),[o,d]=k.useState(null),[h,m]=k.useState(!0),[b,g]=k.useState(null),[x,y]=k.useState(!1),[S,U]=k.useState([]),[B,O]=k.useState(!1),[A,T]=k.useState({firstName:"",lastName:"",bio:"",avatarUrl:"",privacySettings:{displayName:"username"}}),X=!a||a===(r==null?void 0:r.id),L=X?r:o;k.useEffect(()=>{(async()=>{if(X){d(r),m(!1);return}if(a)try{m(!0);const le=await Xr.getUserById(a);le.success?d(le.data):g(le.message||"Failed to load user profile")}catch(le){g("Failed to load user profile"),console.error("Error loading user profile:",le)}finally{m(!1)}})()},[a,r,X]),k.useEffect(()=>{L&&(async()=>{const le=X?r==null?void 0:r.id:a;if(le)try{O(!0);const Q=await Xr.getUserDebates(le);Q.success&&U(Q.data||[])}catch(Q){console.error("Error loading user debates:",Q)}finally{O(!1)}})()},[L,X,r==null?void 0:r.id,a]),k.useEffect(()=>{L&&X&&T({firstName:L.firstName||"",lastName:L.lastName||"",bio:L.bio||"",avatarUrl:L.avatarUrl||"",privacySettings:L.privacySettings||{displayName:"username"}})},[L,X]);const F=async K=>{if(K.preventDefault(),!!X)try{await c(A),y(!1)}catch(le){g("Failed to update profile"),console.error("Error updating profile:",le)}};return h?l.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-brand-primary mx-auto mb-4"}),l.jsx("p",{className:"text-brand-text-secondary",children:"Loading profile..."})]})}):b||!L?l.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-red-500 text-xl mb-4",children:"⚠️"}),l.jsx("p",{className:"text-red-400 mb-4",children:b||"User not found"})]})}):l.jsxs("div",{className:"w-full p-4 md:p-8",children:[l.jsx("div",{className:"bg-brand-surface rounded-lg p-6 mb-8",children:l.jsxs("div",{className:"flex items-start space-x-6",children:[l.jsx("img",{src:L.avatarUrl||`https://picsum.photos/seed/${L.username}/100`,alt:rn(L),className:"w-24 h-24 rounded-full object-cover"}),l.jsxs("div",{className:"flex-1",children:[l.jsxs("div",{className:"flex items-center justify-between mb-4",children:[l.jsxs("div",{children:[l.jsx("h1",{className:"text-3xl font-bold text-white",children:rn(L)}),l.jsxs("p",{className:"text-brand-text-secondary",children:["@",L.username]})]}),X&&l.jsx("button",{onClick:()=>y(!x),className:"bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg transition-colors",children:x?"Cancel":"Edit Profile"})]}),x?l.jsxs("form",{onSubmit:F,className:"space-y-4",children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text-secondary mb-1",children:"First Name"}),l.jsx("input",{type:"text",value:A.firstName,onChange:K=>T({...A,firstName:K.target.value}),className:"w-full px-3 py-2 bg-brand-surface-light border border-brand-surface-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-brand-primary",placeholder:"Your first name"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text-secondary mb-1",children:"Last Name"}),l.jsx("input",{type:"text",value:A.lastName,onChange:K=>T({...A,lastName:K.target.value}),className:"w-full px-3 py-2 bg-brand-surface-light border border-brand-surface-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-brand-primary",placeholder:"Your last name"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text-secondary mb-1",children:"Bio"}),l.jsx("textarea",{value:A.bio,onChange:K=>T({...A,bio:K.target.value}),rows:3,className:"w-full px-3 py-2 bg-brand-surface-light border border-brand-surface-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-brand-primary resize-none",placeholder:"Tell us about yourself..."})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-brand-text-secondary mb-2",children:"Display Name Preference"}),l.jsxs("div",{className:"space-y-2",children:[l.jsxs("label",{className:"flex items-center gap-2 cursor-pointer",children:[l.jsx("input",{type:"radio",name:"displayName",value:"username",checked:A.privacySettings.displayName==="username",onChange:K=>T({...A,privacySettings:{...A.privacySettings,displayName:K.target.value}}),className:"text-brand-primary focus:ring-brand-primary"}),l.jsxs("span",{className:"text-brand-text",children:["Username only (@",L==null?void 0:L.username,")"]})]}),l.jsxs("label",{className:"flex items-center gap-2 cursor-pointer",children:[l.jsx("input",{type:"radio",name:"displayName",value:"firstName",checked:A.privacySettings.displayName==="firstName",onChange:K=>T({...A,privacySettings:{...A.privacySettings,displayName:K.target.value}}),className:"text-brand-primary focus:ring-brand-primary"}),l.jsxs("span",{className:"text-brand-text",children:["First name only (",A.firstName||"Not set",")"]})]}),l.jsxs("label",{className:"flex items-center gap-2 cursor-pointer",children:[l.jsx("input",{type:"radio",name:"displayName",value:"fullName",checked:A.privacySettings.displayName==="fullName",onChange:K=>T({...A,privacySettings:{...A.privacySettings,displayName:K.target.value}}),className:"text-brand-primary focus:ring-brand-primary"}),l.jsxs("span",{className:"text-brand-text",children:["Full name (",A.firstName," ",A.lastName," ",!A.firstName&&!A.lastName?"- Not set":"",")"]})]})]}),l.jsx("p",{className:"text-xs text-brand-text-secondary mt-1",children:"This controls how your name appears to other users in debates and discussions."})]}),l.jsxs("div",{className:"flex space-x-3",children:[l.jsx("button",{type:"submit",className:"bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg transition-colors",children:"Save Changes"}),l.jsx("button",{type:"button",onClick:()=>y(!1),className:"bg-brand-surface-light hover:bg-brand-surface text-white px-4 py-2 rounded-lg transition-colors",children:"Cancel"})]})]}):l.jsxs("div",{children:[l.jsx("p",{className:"text-brand-text-secondary mb-4",children:L.bio||"No bio provided."}),l.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-2xl font-bold text-brand-primary",children:L.reputation||0}),l.jsx("div",{className:"text-sm text-brand-text-secondary",children:"Reputation"})]}),l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-2xl font-bold text-brand-primary",children:L.totalVotes||0}),l.jsx("div",{className:"text-sm text-brand-text-secondary",children:"Total Votes"})]}),l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-2xl font-bold text-brand-primary",children:((ae=L.debatesCreated)==null?void 0:ae.length)||0}),l.jsx("div",{className:"text-sm text-brand-text-secondary",children:"Debates Created"})]}),l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-2xl font-bold text-brand-primary",children:((P=L.debatesParticipated)==null?void 0:P.length)||0}),l.jsx("div",{className:"text-sm text-brand-text-secondary",children:"Debates Joined"})]})]})]})]})]})}),l.jsxs("div",{className:"bg-brand-surface rounded-lg p-6",children:[l.jsx("h2",{className:"text-2xl font-bold text-white mb-6",children:"Recent Activity"}),B?l.jsxs("div",{className:"text-center py-8",children:[l.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary mx-auto mb-4"}),l.jsx("p",{className:"text-brand-text-secondary",children:"Loading activity..."})]}):S.length>0?l.jsx("div",{className:"space-y-4",children:S.map(K=>{var le;return l.jsx("div",{className:"bg-brand-surface-light rounded-lg p-4 border border-brand-border",children:l.jsxs("div",{className:"flex items-start justify-between",children:[l.jsxs("div",{className:"flex-1",children:[l.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:K.title}),l.jsx("p",{className:"text-brand-text-secondary text-sm mb-3 line-clamp-2",children:K.description}),l.jsxs("div",{className:"flex items-center gap-4 text-sm text-brand-text-secondary",children:[l.jsxs("span",{className:"flex items-center gap-1",children:[l.jsx("span",{className:"w-2 h-2 bg-success rounded-full"}),"FOR: ",K.votesFor]}),l.jsxs("span",{className:"flex items-center gap-1",children:[l.jsx("span",{className:"w-2 h-2 bg-danger rounded-full"}),"AGAINST: ",K.votesAgainst]}),l.jsxs("span",{children:[((le=K.arguments)==null?void 0:le.length)||0," arguments"]}),l.jsx("span",{children:"•"}),l.jsx("span",{children:new Date(K.createdAt).toLocaleDateString()})]})]}),l.jsx("div",{className:"ml-4",children:l.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${K.status==="active"?"bg-success/20 text-success":K.status==="completed"?"bg-brand-text-secondary/20 text-brand-text-secondary":"bg-warning/20 text-warning"}`,children:K.status.charAt(0).toUpperCase()+K.status.slice(1)})})]})},K.id)})}):l.jsxs("div",{className:"text-center py-8",children:[l.jsx("p",{className:"text-brand-text-secondary",children:"No recent activity to display."}),l.jsx("p",{className:"text-brand-text-secondary text-sm mt-2",children:X?"Start participating in debates to see your activity here!":"This user hasn't participated in any debates yet."})]})]})]})},Q0=({onDebateSelect:a,onNavigate:r})=>{const{user:c}=xt(),[o,d]=k.useState({debatesCreated:0,debatesParticipated:0,totalArguments:0,totalVotes:0,reputation:0}),[h,m]=k.useState([]),[b,g]=k.useState(!0),[x,y]=k.useState(null),[S,U]=k.useState("created");k.useEffect(()=>{(async()=>{if(c)try{g(!0),y(null);const T=await Xr.getUserDebates(c.id);T.success&&m(T.data),d({debatesCreated:0,debatesParticipated:0,totalArguments:0,totalVotes:c.totalVotes||0,reputation:c.reputation||0})}catch(T){y("Failed to load dashboard data"),console.error("Error loading dashboard:",T)}finally{g(!1)}})()},[c]);const B=h.filter(A=>A.proponent.id===(c==null?void 0:c.id)),O=h.filter(A=>A.participants.some(T=>T.id===(c==null?void 0:c.id))&&A.proponent.id!==(c==null?void 0:c.id));return c?b?l.jsx("div",{className:"w-full p-4 md:p-8",children:l.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-brand-primary mx-auto mb-4"}),l.jsx("p",{className:"text-brand-text-secondary",children:"Loading dashboard..."})]})})}):l.jsxs("div",{className:"w-full p-4 md:p-8",children:[l.jsxs("div",{className:"mb-8",children:[l.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Dashboard"}),l.jsxs("p",{className:"text-brand-text-secondary",children:["Welcome back, ",rn(c),"!"]})]}),l.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 mb-8",children:[l.jsxs("div",{className:"bg-brand-surface rounded-lg p-4 text-center",children:[l.jsx("div",{className:"text-2xl font-bold text-brand-primary",children:o.reputation}),l.jsx("div",{className:"text-sm text-brand-text-secondary",children:"Reputation"})]}),l.jsxs("div",{className:"bg-brand-surface rounded-lg p-4 text-center",children:[l.jsx("div",{className:"text-2xl font-bold text-brand-primary",children:B.length}),l.jsx("div",{className:"text-sm text-brand-text-secondary",children:"Debates Created"})]}),l.jsxs("div",{className:"bg-brand-surface rounded-lg p-4 text-center",children:[l.jsx("div",{className:"text-2xl font-bold text-brand-primary",children:O.length}),l.jsx("div",{className:"text-sm text-brand-text-secondary",children:"Debates Joined"})]}),l.jsxs("div",{className:"bg-brand-surface rounded-lg p-4 text-center",children:[l.jsx("div",{className:"text-2xl font-bold text-brand-primary",children:o.totalArguments}),l.jsx("div",{className:"text-sm text-brand-text-secondary",children:"Arguments Posted"})]}),l.jsxs("div",{className:"bg-brand-surface rounded-lg p-4 text-center",children:[l.jsx("div",{className:"text-2xl font-bold text-brand-primary",children:o.totalVotes}),l.jsx("div",{className:"text-sm text-brand-text-secondary",children:"Votes Cast"})]})]}),l.jsxs("div",{className:"bg-brand-surface rounded-lg p-6 mb-8",children:[l.jsx("h2",{className:"text-xl font-bold text-white mb-4",children:"Quick Actions"}),l.jsxs("div",{className:"flex flex-wrap gap-4",children:[l.jsx("button",{onClick:()=>r==null?void 0:r("create-debate"),className:"bg-brand-primary hover:bg-brand-primary-dark text-white px-6 py-3 rounded-lg transition-colors",children:"Create New Debate"}),l.jsx("button",{onClick:()=>r==null?void 0:r("debates"),className:"bg-brand-surface-light hover:bg-brand-surface text-white px-6 py-3 rounded-lg transition-colors",children:"Browse Debates"}),l.jsx("button",{onClick:()=>r==null?void 0:r("profile"),className:"bg-brand-surface-light hover:bg-brand-surface text-white px-6 py-3 rounded-lg transition-colors",children:"View Profile"})]})]}),l.jsxs("div",{className:"bg-brand-surface rounded-lg p-6",children:[l.jsxs("div",{className:"flex items-center justify-between mb-6",children:[l.jsx("h2",{className:"text-xl font-bold text-white",children:"Your Debates"}),l.jsxs("div",{className:"flex bg-brand-surface-light rounded-lg p-1",children:[l.jsxs("button",{onClick:()=>U("created"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${S==="created"?"bg-brand-primary text-white":"text-brand-text-secondary hover:text-white"}`,children:["Created (",B.length,")"]}),l.jsxs("button",{onClick:()=>U("participated"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${S==="participated"?"bg-brand-primary text-white":"text-brand-text-secondary hover:text-white"}`,children:["Participated (",O.length,")"]})]})]}),x&&l.jsx("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6",children:l.jsx("p",{className:"text-red-400",children:x})}),l.jsx("div",{className:"space-y-4",children:S==="created"?B.length>0?B.map(A=>l.jsx(Qr,{debate:A,onSelect:()=>a==null?void 0:a(A.id)},A.id)):l.jsxs("div",{className:"text-center py-8",children:[l.jsx("p",{className:"text-brand-text-secondary mb-4",children:"You haven't created any debates yet."}),l.jsx("button",{onClick:()=>r==null?void 0:r("create-debate"),className:"bg-brand-primary hover:bg-brand-primary-dark text-white px-6 py-2 rounded-lg transition-colors",children:"Create Your First Debate"})]}):O.length>0?O.map(A=>l.jsx(Qr,{debate:A,onSelect:()=>a==null?void 0:a(A.id)},A.id)):l.jsxs("div",{className:"text-center py-8",children:[l.jsx("p",{className:"text-brand-text-secondary mb-4",children:"You haven't joined any debates yet."}),l.jsx("button",{onClick:()=>r==null?void 0:r("debates"),className:"bg-brand-primary hover:bg-brand-primary-dark text-white px-6 py-2 rounded-lg transition-colors",children:"Browse Available Debates"})]})})]})]}):l.jsx("div",{className:"w-full p-4 md:p-8",children:l.jsx("div",{className:"text-center py-12",children:l.jsx("p",{className:"text-brand-text-secondary",children:"Please log in to view your dashboard."})})})};var Z0={};const sn=Z0.REACT_APP_API_URL||"http://localhost:5000/api",an=()=>{const a=localStorage.getItem("token");return{"Content-Type":"application/json",...a&&{Authorization:`Bearer ${a}`}}},ln=async a=>{if(!a.ok){const r=await a.json().catch(()=>({message:"Network error"}));throw new Error(r.message||"API request failed")}return a.json()},Yr={getStats:async()=>{const a=await fetch(`${sn}/admin/stats`,{method:"GET",headers:an()});return ln(a)},getUsers:async(a={})=>{const r=new URLSearchParams;Object.entries(a).forEach(([o,d])=>{d!==void 0&&d!==""&&r.append(o,d.toString())});const c=await fetch(`${sn}/admin/users?${r}`,{method:"GET",headers:an()});return ln(c)},getUserDetails:async a=>{const r=await fetch(`${sn}/admin/users/${a}`,{method:"GET",headers:an()});return ln(r)},updateUserRole:async(a,r)=>{const c=await fetch(`${sn}/admin/users/${a}/role`,{method:"PUT",headers:an(),body:JSON.stringify({role:r})});return ln(c)},suspendUser:async(a,r,c)=>{const o=await fetch(`${sn}/admin/users/${a}/suspend`,{method:"PUT",headers:an(),body:JSON.stringify({suspended:r,reason:c})});return ln(o)}},To={getQueue:async(a={})=>{const r=new URLSearchParams;Object.entries(a).forEach(([o,d])=>{d!==void 0&&d!==""&&r.append(o,d.toString())});const c=await fetch(`${sn}/moderation/queue?${r}`,{method:"GET",headers:an()});return ln(c)},getStats:async()=>{const a=await fetch(`${sn}/moderation/stats`,{method:"GET",headers:an()});return ln(a)},reviewItem:async(a,r)=>{const c=await fetch(`${sn}/moderation/${a}/review`,{method:"PUT",headers:an(),body:JSON.stringify(r)});return ln(c)},triggerCleanup:async()=>{const a=await fetch(`${sn}/moderation/cleanup`,{method:"POST",headers:an()});return ln(a)},getCleanupStats:async()=>{const a=await fetch(`${sn}/moderation/cleanup/stats`,{method:"GET",headers:an()});return ln(a)}},K0=({stats:a,onRefresh:r})=>{const{overview:c,userRoles:o,recentActivity:d}=a,h=[{title:"Total Users",value:c.totalUsers,icon:"👥",color:"bg-blue-500/20 border-blue-500/30",textColor:"text-blue-400"},{title:"Total Debates",value:c.totalDebates,icon:"💬",color:"bg-green-500/20 border-green-500/30",textColor:"text-green-400"},{title:"Active Debates",value:c.activeDebates,icon:"🔥",color:"bg-orange-500/20 border-orange-500/30",textColor:"text-orange-400"},{title:"Pending Moderation",value:c.pendingModerationItems,icon:"⚠️",color:"bg-red-500/20 border-red-500/30",textColor:"text-red-400"},{title:"New Users Today",value:c.usersToday,icon:"📈",color:"bg-purple-500/20 border-purple-500/30",textColor:"text-purple-400"},{title:"New Debates Today",value:c.debatesToday,icon:"🆕",color:"bg-cyan-500/20 border-cyan-500/30",textColor:"text-cyan-400"}];return l.jsxs("div",{className:"space-y-8",children:[l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("h2",{className:"text-2xl font-bold text-white",children:"System Overview"}),l.jsxs("button",{onClick:r,className:"bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2",children:[l.jsx("span",{children:"🔄"}),l.jsx("span",{children:"Refresh"})]})]}),l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:h.map((m,b)=>l.jsx("div",{className:`${m.color} border rounded-lg p-6`,children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("p",{className:"text-brand-text-secondary text-sm font-medium",children:m.title}),l.jsx("p",{className:`text-3xl font-bold ${m.textColor}`,children:m.value.toLocaleString()})]}),l.jsx("div",{className:"text-3xl",children:m.icon})]})},b))}),l.jsxs("div",{className:"bg-brand-surface rounded-lg p-6",children:[l.jsx("h3",{className:"text-xl font-bold text-white mb-4",children:"User Roles Distribution"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[l.jsx("div",{className:"bg-brand-surface-light rounded-lg p-4",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("span",{className:"text-brand-text-secondary",children:"Users"}),l.jsx("span",{className:"text-2xl font-bold text-blue-400",children:o.user||0})]})}),l.jsx("div",{className:"bg-brand-surface-light rounded-lg p-4",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("span",{className:"text-brand-text-secondary",children:"Moderators"}),l.jsx("span",{className:"text-2xl font-bold text-green-400",children:o.moderator||0})]})}),l.jsx("div",{className:"bg-brand-surface-light rounded-lg p-4",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("span",{className:"text-brand-text-secondary",children:"Admins"}),l.jsx("span",{className:"text-2xl font-bold text-purple-400",children:o.admin||0})]})})]})]}),l.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[l.jsxs("div",{className:"bg-brand-surface rounded-lg p-6",children:[l.jsx("h3",{className:"text-xl font-bold text-white mb-4",children:"Recent Users"}),l.jsx("div",{className:"space-y-3",children:d.users.length>0?d.users.map(m=>l.jsxs("div",{className:"flex items-center justify-between bg-brand-surface-light rounded-lg p-3",children:[l.jsxs("div",{children:[l.jsx("p",{className:"text-white font-medium",children:rn(m)}),l.jsxs("p",{className:"text-brand-text-secondary text-sm",children:["@",m.username]})]}),l.jsxs("div",{className:"text-right",children:[l.jsx("span",{className:`inline-block px-2 py-1 rounded text-xs font-medium ${m.role==="admin"?"bg-purple-500/20 text-purple-400":m.role==="moderator"?"bg-green-500/20 text-green-400":"bg-blue-500/20 text-blue-400"}`,children:m.role}),l.jsx("p",{className:"text-brand-text-secondary text-xs mt-1",children:new Date(m.createdAt).toLocaleDateString()})]})]},m.id)):l.jsx("p",{className:"text-brand-text-secondary text-center py-4",children:"No recent users"})})]}),l.jsxs("div",{className:"bg-brand-surface rounded-lg p-6",children:[l.jsx("h3",{className:"text-xl font-bold text-white mb-4",children:"Recent Debates"}),l.jsx("div",{className:"space-y-3",children:d.debates.length>0?d.debates.map(m=>l.jsxs("div",{className:"bg-brand-surface-light rounded-lg p-3",children:[l.jsx("p",{className:"text-white font-medium mb-1",children:m.title}),l.jsxs("div",{className:"flex items-center justify-between text-sm",children:[l.jsxs("span",{className:"text-brand-text-secondary",children:["by ",rn(m.proponent)]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("span",{className:`inline-block px-2 py-1 rounded text-xs font-medium ${m.status==="active"?"bg-green-500/20 text-green-400":m.status==="completed"?"bg-blue-500/20 text-blue-400":"bg-gray-500/20 text-gray-400"}`,children:m.status}),l.jsx("span",{className:"text-brand-text-secondary",children:new Date(m.createdAt).toLocaleDateString()})]})]})]},m.id)):l.jsx("p",{className:"text-brand-text-secondary text-center py-4",children:"No recent debates"})})]})]})]})},J0=()=>{const[a,r]=k.useState([]),[c,o]=k.useState({page:1,limit:20,total:0,pages:0}),[d,h]=k.useState(!0),[m,b]=k.useState(null),[g,x]=k.useState({role:"all",search:"",sortBy:"createdAt",sortOrder:"desc"});k.useEffect(()=>{y()},[c.page,g]);const y=async()=>{try{h(!0),b(null);const A=await Yr.getUsers({page:c.page,limit:c.limit,...g});A.success&&(r(A.data.users),o(A.data.pagination))}catch(A){b(A instanceof Error?A.message:"Failed to load users")}finally{h(!1)}},S=async(A,T)=>{try{(await Yr.updateUserRole(A,T)).success&&r(a.map(L=>L.id===A?{...L,role:T}:L))}catch(X){alert(X instanceof Error?X.message:"Failed to update user role")}},U=async(A,T,X)=>{try{(await Yr.suspendUser(A,T,X)).success&&r(a.map(F=>F.id===A?{...F,suspended:T,suspensionReason:X}:F))}catch(L){alert(L instanceof Error?L.message:"Failed to update user suspension")}},B=(A,T)=>{x(X=>({...X,[A]:T})),o(X=>({...X,page:1}))},O=A=>{o(T=>({...T,page:A}))};return l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("h2",{className:"text-2xl font-bold text-white",children:"User Management"}),l.jsx("button",{onClick:y,className:"bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg transition-colors",children:"Refresh"})]}),l.jsx("div",{className:"bg-brand-surface rounded-lg p-6",children:l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-brand-text-secondary text-sm font-medium mb-2",children:"Search Users"}),l.jsx("input",{type:"text",value:g.search,onChange:A=>B("search",A.target.value),placeholder:"Username, email, or name...",className:"w-full bg-brand-surface-light border border-brand-border rounded-lg px-3 py-2 text-white placeholder-brand-text-secondary focus:outline-none focus:ring-2 focus:ring-brand-primary"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-brand-text-secondary text-sm font-medium mb-2",children:"Role Filter"}),l.jsxs("select",{value:g.role,onChange:A=>B("role",A.target.value),className:"w-full bg-brand-surface-light border border-brand-border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-brand-primary",children:[l.jsx("option",{value:"all",children:"All Roles"}),l.jsx("option",{value:"user",children:"Users"}),l.jsx("option",{value:"moderator",children:"Moderators"}),l.jsx("option",{value:"admin",children:"Admins"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-brand-text-secondary text-sm font-medium mb-2",children:"Sort By"}),l.jsxs("select",{value:g.sortBy,onChange:A=>B("sortBy",A.target.value),className:"w-full bg-brand-surface-light border border-brand-border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-brand-primary",children:[l.jsx("option",{value:"createdAt",children:"Join Date"}),l.jsx("option",{value:"username",children:"Username"}),l.jsx("option",{value:"email",children:"Email"}),l.jsx("option",{value:"reputation",children:"Reputation"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-brand-text-secondary text-sm font-medium mb-2",children:"Order"}),l.jsxs("select",{value:g.sortOrder,onChange:A=>B("sortOrder",A.target.value),className:"w-full bg-brand-surface-light border border-brand-border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-brand-primary",children:[l.jsx("option",{value:"desc",children:"Descending"}),l.jsx("option",{value:"asc",children:"Ascending"})]})]})]})}),l.jsx("div",{className:"bg-brand-surface rounded-lg overflow-hidden",children:d?l.jsxs("div",{className:"p-8 text-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary mx-auto mb-4"}),l.jsx("p",{className:"text-brand-text-secondary",children:"Loading users..."})]}):m?l.jsxs("div",{className:"p-8 text-center",children:[l.jsx("p",{className:"text-red-400 mb-4",children:m}),l.jsx("button",{onClick:y,className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors",children:"Retry"})]}):l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"w-full",children:[l.jsx("thead",{className:"bg-brand-surface-light",children:l.jsxs("tr",{children:[l.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-brand-text-secondary uppercase tracking-wider",children:"User"}),l.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-brand-text-secondary uppercase tracking-wider",children:"Role"}),l.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-brand-text-secondary uppercase tracking-wider",children:"Status"}),l.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-brand-text-secondary uppercase tracking-wider",children:"Reputation"}),l.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-brand-text-secondary uppercase tracking-wider",children:"Joined"}),l.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-brand-text-secondary uppercase tracking-wider",children:"Actions"})]})}),l.jsx("tbody",{className:"divide-y divide-brand-border",children:a.map(A=>l.jsx(F0,{user:A,onRoleChange:S,onSuspendUser:U},A.id))})]})}),c.pages>1&&l.jsx("div",{className:"px-6 py-4 border-t border-brand-border",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"text-brand-text-secondary text-sm",children:["Showing ",(c.page-1)*c.limit+1," to"," ",Math.min(c.page*c.limit,c.total)," of"," ",c.total," users"]}),l.jsxs("div",{className:"flex space-x-2",children:[l.jsx("button",{onClick:()=>O(c.page-1),disabled:c.page===1,className:"px-3 py-1 rounded bg-brand-surface-light text-brand-text-secondary hover:bg-brand-primary hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"Previous"}),l.jsxs("span",{className:"px-3 py-1 text-white",children:["Page ",c.page," of ",c.pages]}),l.jsx("button",{onClick:()=>O(c.page+1),disabled:c.page===c.pages,className:"px-3 py-1 rounded bg-brand-surface-light text-brand-text-secondary hover:bg-brand-primary hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"Next"})]})]})})]})})]})},F0=({user:a,onRoleChange:r,onSuspendUser:c})=>{const[o,d]=k.useState(!1),h=()=>{const b=prompt("Enter suspension reason:");b&&c(a.id,!0,b)},m=()=>{c(a.id,!1)};return l.jsxs("tr",{className:"hover:bg-brand-surface-light",children:[l.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:l.jsxs("div",{children:[l.jsx("div",{className:"text-white font-medium",children:rn(a)}),l.jsxs("div",{className:"text-brand-text-secondary text-sm",children:["@",a.username]}),l.jsx("div",{className:"text-brand-text-secondary text-sm",children:a.email})]})}),l.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:l.jsxs("select",{value:a.role,onChange:b=>r(a.id,b.target.value),className:`text-xs font-medium px-2 py-1 rounded border-0 ${a.role==="admin"?"bg-purple-500/20 text-purple-400":a.role==="moderator"?"bg-green-500/20 text-green-400":"bg-blue-500/20 text-blue-400"}`,children:[l.jsx("option",{value:"user",children:"User"}),l.jsx("option",{value:"moderator",children:"Moderator"}),l.jsx("option",{value:"admin",children:"Admin"})]})}),l.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:l.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${a.suspended?"bg-red-500/20 text-red-400":"bg-green-500/20 text-green-400"}`,children:a.suspended?"Suspended":"Active"})}),l.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-white",children:a.reputation}),l.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-brand-text-secondary",children:new Date(a.createdAt).toLocaleDateString()}),l.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:l.jsxs("div",{className:"relative",children:[l.jsx("button",{onClick:()=>d(!o),className:"text-brand-text-secondary hover:text-white",children:"⋮"}),o&&l.jsx("div",{className:"absolute right-0 mt-2 w-48 bg-brand-surface border border-brand-border rounded-lg shadow-lg z-10",children:l.jsxs("div",{className:"py-1",children:[a.suspended?l.jsx("button",{onClick:m,className:"block w-full text-left px-4 py-2 text-sm text-green-400 hover:bg-brand-surface-light",children:"Unsuspend User"}):l.jsx("button",{onClick:h,className:"block w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-brand-surface-light",children:"Suspend User"}),l.jsx("button",{onClick:()=>d(!1),className:"block w-full text-left px-4 py-2 text-sm text-brand-text-secondary hover:bg-brand-surface-light",children:"View Details"})]})})]})})]})},$0=()=>{const[a,r]=k.useState([]),[c,o]=k.useState({page:1,limit:10,total:0,pages:0}),[d,h]=k.useState(!0),[m,b]=k.useState(null),[g,x]=k.useState("pending"),[y,S]=k.useState(null);k.useEffect(()=>{U(),B()},[c.page,g]);const U=async()=>{try{h(!0),b(null);const T=await To.getQueue({status:g==="all"?void 0:g,page:c.page,limit:c.limit});T.success&&(r(T.data.items),o(T.data.pagination))}catch(T){b(T instanceof Error?T.message:"Failed to load moderation queue")}finally{h(!1)}},B=async()=>{try{const T=await To.getStats();T.success&&S(T.data)}catch(T){console.error("Failed to load moderation stats:",T)}},O=async(T,X,L,F)=>{try{const ae=await To.reviewItem(T,{status:X,actionTaken:L,moderatorNotes:F});ae.success&&r(a.map(P=>P.id===T?ae.data:P))}catch(ae){alert(ae instanceof Error?ae.message:"Failed to review item")}},A=T=>{o(X=>({...X,page:T}))};return l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("h2",{className:"text-2xl font-bold text-white",children:"Moderation Queue"}),l.jsx("button",{onClick:U,className:"bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg transition-colors",children:"Refresh"})]}),y&&l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[l.jsx("div",{className:"bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("p",{className:"text-brand-text-secondary text-sm",children:"Pending"}),l.jsx("p",{className:"text-2xl font-bold text-yellow-400",children:y.moderation.pending})]}),l.jsx("span",{className:"text-2xl",children:"⏳"})]})}),l.jsx("div",{className:"bg-blue-500/20 border border-blue-500/30 rounded-lg p-4",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("p",{className:"text-brand-text-secondary text-sm",children:"Reviewed"}),l.jsx("p",{className:"text-2xl font-bold text-blue-400",children:y.moderation.reviewed})]}),l.jsx("span",{className:"text-2xl",children:"👁️"})]})}),l.jsx("div",{className:"bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("p",{className:"text-brand-text-secondary text-sm",children:"Resolved"}),l.jsx("p",{className:"text-2xl font-bold text-green-400",children:y.moderation.resolved})]}),l.jsx("span",{className:"text-2xl",children:"✅"})]})}),l.jsx("div",{className:"bg-gray-500/20 border border-gray-500/30 rounded-lg p-4",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("p",{className:"text-brand-text-secondary text-sm",children:"Dismissed"}),l.jsx("p",{className:"text-2xl font-bold text-gray-400",children:y.moderation.dismissed})]}),l.jsx("span",{className:"text-2xl",children:"❌"})]})})]}),l.jsx("div",{className:"bg-brand-surface rounded-lg p-6",children:l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsx("label",{className:"text-brand-text-secondary text-sm font-medium",children:"Status Filter:"}),l.jsxs("select",{value:g,onChange:T=>x(T.target.value),className:"bg-brand-surface-light border border-brand-border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-brand-primary",children:[l.jsx("option",{value:"pending",children:"Pending"}),l.jsx("option",{value:"reviewed",children:"Reviewed"}),l.jsx("option",{value:"resolved",children:"Resolved"}),l.jsx("option",{value:"dismissed",children:"Dismissed"}),l.jsx("option",{value:"all",children:"All"})]})]})}),l.jsx("div",{className:"space-y-4",children:d?l.jsxs("div",{className:"bg-brand-surface rounded-lg p-8 text-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary mx-auto mb-4"}),l.jsx("p",{className:"text-brand-text-secondary",children:"Loading moderation queue..."})]}):m?l.jsxs("div",{className:"bg-brand-surface rounded-lg p-8 text-center",children:[l.jsx("p",{className:"text-red-400 mb-4",children:m}),l.jsx("button",{onClick:U,className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors",children:"Retry"})]}):a.length===0?l.jsx("div",{className:"bg-brand-surface rounded-lg p-8 text-center",children:l.jsx("p",{className:"text-brand-text-secondary",children:"No moderation items found"})}):a.map(T=>l.jsx(W0,{item:T,onReview:O},T.id))}),c.pages>1&&l.jsx("div",{className:"bg-brand-surface rounded-lg px-6 py-4",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"text-brand-text-secondary text-sm",children:["Showing ",(c.page-1)*c.limit+1," to"," ",Math.min(c.page*c.limit,c.total)," of"," ",c.total," items"]}),l.jsxs("div",{className:"flex space-x-2",children:[l.jsx("button",{onClick:()=>A(c.page-1),disabled:c.page===1,className:"px-3 py-1 rounded bg-brand-surface-light text-brand-text-secondary hover:bg-brand-primary hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"Previous"}),l.jsxs("span",{className:"px-3 py-1 text-white",children:["Page ",c.page," of ",c.pages]}),l.jsx("button",{onClick:()=>A(c.page+1),disabled:c.page===c.pages,className:"px-3 py-1 rounded bg-brand-surface-light text-brand-text-secondary hover:bg-brand-primary hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"Next"})]})]})})]})},W0=({item:a,onReview:r})=>{const[c,o]=k.useState(!1),[d,h]=k.useState(""),[m,b]=k.useState("none"),g=x=>{r(a.id,x,m,d),o(!1),h(""),b("none")};return l.jsxs("div",{className:"bg-brand-surface rounded-lg p-6",children:[l.jsx("div",{className:"flex justify-between items-start mb-4",children:l.jsxs("div",{children:[l.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:a.debateTitle}),l.jsxs("div",{className:"flex items-center space-x-4 text-sm text-brand-text-secondary",children:[l.jsxs("span",{children:["Reported by: ",a.reportedBy]}),l.jsx("span",{children:"•"}),l.jsx("span",{children:new Date(a.reportedAt).toLocaleString()}),l.jsx("span",{children:"•"}),l.jsx("span",{className:`px-2 py-1 rounded text-xs font-medium ${a.status==="pending"?"bg-yellow-500/20 text-yellow-400":a.status==="reviewed"?"bg-blue-500/20 text-blue-400":a.status==="resolved"?"bg-green-500/20 text-green-400":"bg-gray-500/20 text-gray-400"}`,children:a.status})]})]})}),l.jsxs("div",{className:"bg-brand-surface-light rounded-lg p-4 mb-4",children:[l.jsxs("div",{className:"mb-2",children:[l.jsx("span",{className:"text-brand-text-secondary text-sm",children:"Reported Comment by "}),l.jsx("span",{className:"text-white font-medium",children:rn(a.commentAuthor)}),l.jsxs("span",{className:"text-brand-text-secondary text-sm",children:[" on ",new Date(a.commentCreatedAt).toLocaleString()]})]}),l.jsx("p",{className:"text-white",children:a.commentText})]}),a.status==="pending"&&l.jsxs("div",{className:"flex space-x-2",children:[l.jsx("button",{onClick:()=>o(!c),className:"bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg transition-colors",children:"Review"}),l.jsx("button",{onClick:()=>r(a.id,"dismissed"),className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors",children:"Dismiss"})]}),c&&l.jsx("div",{className:"mt-4 bg-brand-surface-light rounded-lg p-4",children:l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-brand-text-secondary text-sm font-medium mb-2",children:"Action to Take"}),l.jsxs("select",{value:m,onChange:x=>b(x.target.value),className:"w-full bg-brand-surface border border-brand-border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-brand-primary",children:[l.jsx("option",{value:"none",children:"No Action"}),l.jsx("option",{value:"warning",children:"Send Warning"}),l.jsx("option",{value:"comment_removed",children:"Remove Comment"}),l.jsx("option",{value:"user_suspended",children:"Suspend User"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-brand-text-secondary text-sm font-medium mb-2",children:"Moderator Notes"}),l.jsx("textarea",{value:d,onChange:x=>h(x.target.value),placeholder:"Add notes about your decision...",rows:3,className:"w-full bg-brand-surface border border-brand-border rounded-lg px-3 py-2 text-white placeholder-brand-text-secondary focus:outline-none focus:ring-2 focus:ring-brand-primary"})]}),l.jsxs("div",{className:"flex space-x-2",children:[l.jsx("button",{onClick:()=>g("resolved"),className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors",children:"Resolve"}),l.jsx("button",{onClick:()=>g("reviewed"),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors",children:"Mark Reviewed"}),l.jsx("button",{onClick:()=>o(!1),className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors",children:"Cancel"})]})]})}),a.moderatorNotes&&l.jsxs("div",{className:"mt-4 bg-blue-500/10 border border-blue-500/20 rounded-lg p-3",children:[l.jsx("p",{className:"text-blue-400 text-sm font-medium mb-1",children:"Moderator Notes:"}),l.jsx("p",{className:"text-white text-sm",children:a.moderatorNotes})]})]})},P0=()=>{const{user:a}=xt(),[r,c]=k.useState("overview"),[o,d]=k.useState(null),[h,m]=k.useState(!0),[b,g]=k.useState(null);k.useEffect(()=>{x()},[]);const x=async()=>{try{m(!0),g(null);const S=await Yr.getStats();S.success&&d(S.data)}catch(S){g(S instanceof Error?S.message:"Failed to load admin stats")}finally{m(!1)}};if((a==null?void 0:a.role)!=="admin")return l.jsx("div",{className:"min-h-screen bg-brand-bg flex items-center justify-center",children:l.jsxs("div",{className:"bg-brand-surface rounded-lg p-8 text-center",children:[l.jsx("h1",{className:"text-2xl font-bold text-white mb-4",children:"Access Denied"}),l.jsx("p",{className:"text-brand-text-secondary",children:"You don't have permission to access the admin dashboard."})]})});const y=[{id:"overview",label:"Overview",icon:"📊"},{id:"users",label:"User Management",icon:"👥"},{id:"moderation",label:"Moderation Queue",icon:"🛡️"},{id:"settings",label:"Settings",icon:"⚙️"}];return l.jsx("div",{className:"min-h-screen bg-brand-bg",children:l.jsxs("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:[l.jsxs("div",{className:"mb-8",children:[l.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Admin Dashboard"}),l.jsx("p",{className:"text-brand-text-secondary",children:"Manage users, moderate content, and monitor system health"})]}),l.jsx("div",{className:"bg-brand-surface rounded-lg p-6 mb-8",children:l.jsx("div",{className:"flex flex-wrap gap-4",children:y.map(S=>l.jsxs("button",{onClick:()=>c(S.id),className:`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${r===S.id?"bg-brand-primary text-white":"bg-brand-surface-light hover:bg-brand-surface text-brand-text-secondary hover:text-white"}`,children:[l.jsx("span",{children:S.icon}),l.jsx("span",{children:S.label})]},S.id))})}),l.jsxs("div",{className:"space-y-8",children:[h&&l.jsxs("div",{className:"bg-brand-surface rounded-lg p-8 text-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-brand-primary mx-auto mb-4"}),l.jsx("p",{className:"text-brand-text-secondary",children:"Loading admin data..."})]}),b&&l.jsxs("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4",children:[l.jsx("p",{className:"text-red-400",children:b}),l.jsx("button",{onClick:x,className:"mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors",children:"Retry"})]}),!h&&!b&&l.jsxs(l.Fragment,{children:[r==="overview"&&o&&l.jsx(K0,{stats:o,onRefresh:x}),r==="users"&&l.jsx(J0,{}),r==="moderation"&&l.jsx($0,{}),r==="settings"&&l.jsxs("div",{className:"bg-brand-surface rounded-lg p-8 text-center",children:[l.jsx("h2",{className:"text-xl font-bold text-white mb-4",children:"Settings"}),l.jsx("p",{className:"text-brand-text-secondary",children:"Settings panel coming soon..."})]})]})]})]})})},I0=({currentView:a,onNavigate:r,onAuthModalOpen:c})=>{const{isAuthenticated:o,user:d}=xt();return l.jsxs("nav",{className:"bg-brand-surface border-b border-brand-surface-light",children:[l.jsx("div",{className:"w-full px-4 sm:px-6 lg:px-8",children:l.jsxs("div",{className:"flex items-center justify-between h-16",children:[l.jsxs("div",{className:"flex items-center space-x-8",children:[l.jsxs("button",{onClick:()=>r("debates"),className:"text-2xl font-bold text-white tracking-tight hover:text-brand-primary transition-colors",children:[l.jsx("span",{className:"text-brand-primary",children:"Agora:"})," The Debate Platform"]}),l.jsxs("div",{className:"hidden md:flex items-center space-x-6",children:[l.jsx("button",{onClick:()=>r("debates"),className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${a==="debates"||a==="debate"?"text-brand-primary bg-brand-primary/10":"text-brand-text-secondary hover:text-white"}`,children:"Debates"}),o&&l.jsxs(l.Fragment,{children:[l.jsx("button",{onClick:()=>r("dashboard"),className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${a==="dashboard"?"text-brand-primary bg-brand-primary/10":"text-brand-text-secondary hover:text-white"}`,children:"Dashboard"}),(d==null?void 0:d.role)==="admin"&&l.jsx("button",{onClick:()=>r("admin"),className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${a==="admin"?"text-brand-primary bg-brand-primary/10":"text-brand-text-secondary hover:text-white"}`,children:"Admin"}),l.jsx("button",{onClick:()=>r("create-debate"),className:"bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:"Create Debate"})]})]})]}),l.jsx("div",{className:"flex items-center space-x-4",children:o&&d?l.jsx(G0,{onNavigate:r}):l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("button",{onClick:()=>c("login"),className:"text-brand-text-secondary hover:text-white transition-colors text-sm font-medium",children:"Sign In"}),l.jsx("button",{onClick:()=>c("register"),className:"bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:"Sign Up"})]})})]})}),o&&l.jsx("div",{className:"md:hidden border-t border-brand-surface-light",children:l.jsxs("div",{className:"px-4 py-3 space-y-1",children:[l.jsx("button",{onClick:()=>r("debates"),className:`block w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${a==="debates"||a==="debate"?"text-brand-primary bg-brand-primary/10":"text-brand-text-secondary hover:text-white"}`,children:"Debates"}),l.jsx("button",{onClick:()=>r("dashboard"),className:`block w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${a==="dashboard"?"text-brand-primary bg-brand-primary/10":"text-brand-text-secondary hover:text-white"}`,children:"Dashboard"}),l.jsx("button",{onClick:()=>r("profile"),className:`block w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${a==="profile"?"text-brand-primary bg-brand-primary/10":"text-brand-text-secondary hover:text-white"}`,children:"Profile"}),l.jsx("button",{onClick:()=>r("create-debate"),className:"block w-full text-left px-3 py-2 rounded-md text-sm font-medium bg-brand-primary hover:bg-brand-primary-dark text-white transition-colors",children:"Create Debate"})]})})]})},Dr=({children:a,fallback:r})=>{const{isAuthenticated:c}=xt();return c?l.jsx(l.Fragment,{children:a}):l.jsx("div",{className:"w-full p-4 md:p-8",children:l.jsx("div",{className:"text-center py-12",children:l.jsxs("div",{className:"bg-brand-surface rounded-lg p-8",children:[l.jsx("div",{className:"text-brand-primary text-4xl mb-4",children:"🔒"}),l.jsx("h2",{className:"text-2xl font-bold text-white mb-4",children:"Authentication Required"}),l.jsx("p",{className:"text-brand-text-secondary mb-6",children:"You need to be logged in to access this page."}),r||l.jsxs("div",{className:"space-x-4",children:[l.jsx("button",{className:"bg-brand-primary hover:bg-brand-primary-dark text-white px-6 py-2 rounded-lg transition-colors",children:"Sign In"}),l.jsx("button",{className:"bg-brand-surface-light hover:bg-brand-surface text-white px-6 py-2 rounded-lg transition-colors",children:"Create Account"})]})]})})})},ev=()=>{const{debates:a,selectedDebate:r,selectDebate:c,castVote:o,castDebateVote:d,loading:h,error:m,refetch:b}=_y(),{isAuthenticated:g}=xt(),[x,y]=k.useState(!1),[S,U]=k.useState("login"),[B,O]=k.useState("debates"),[A,T]=k.useState(null),X=(J,xe)=>{if(!g){U("login"),y(!0);return}r&&o(r.id,J,xe)},L=J=>{if(!g){U("login"),y(!0);return}r&&d(r.id,J)},F=J=>{U(J),y(!0)},ae=J=>{J==="debates"?(O("debates"),c(null),T(null)):O(J)},P=J=>{T(J)},K=J=>{c(J),O("debate")},le=()=>{c(null),O("debates")},Q=(J,xe,de)=>{console.log("Add sub-argument:",{parentId:J,text:xe,isChallenge:de})},$=(J,xe)=>{console.log("Add evidence:",{argumentId:J,evidence:xe})};return l.jsxs("div",{className:"min-h-screen bg-brand-bg font-sans",children:[l.jsx(I0,{currentView:B,onNavigate:ae,onAuthModalOpen:F}),l.jsx("main",{children:B==="profile"?l.jsx(Dr,{fallback:l.jsxs("div",{className:"space-x-4",children:[l.jsx("button",{onClick:()=>F("login"),className:"bg-brand-primary hover:bg-brand-primary-dark text-white px-6 py-2 rounded-lg transition-colors",children:"Sign In"}),l.jsx("button",{onClick:()=>F("register"),className:"bg-brand-surface-light hover:bg-brand-surface text-white px-6 py-2 rounded-lg transition-colors",children:"Create Account"})]}),children:l.jsx(X0,{})}):B==="dashboard"?l.jsx(Dr,{fallback:l.jsxs("div",{className:"space-x-4",children:[l.jsx("button",{onClick:()=>F("login"),className:"bg-brand-primary hover:bg-brand-primary-dark text-white px-6 py-2 rounded-lg transition-colors",children:"Sign In"}),l.jsx("button",{onClick:()=>F("register"),className:"bg-brand-surface-light hover:bg-brand-surface text-white px-6 py-2 rounded-lg transition-colors",children:"Create Account"})]}),children:l.jsx(Q0,{onDebateSelect:K,onNavigate:ae})}):B==="create-debate"?l.jsx(Dr,{fallback:l.jsxs("div",{className:"space-x-4",children:[l.jsx("button",{onClick:()=>F("login"),className:"bg-brand-primary hover:bg-brand-primary-dark text-white px-6 py-2 rounded-lg transition-colors",children:"Sign In"}),l.jsx("button",{onClick:()=>F("register"),className:"bg-brand-surface-light hover:bg-brand-surface text-white px-6 py-2 rounded-lg transition-colors",children:"Create Account"})]}),children:l.jsx(k0,{onSuccess:J=>{K(J),b()},onCancel:()=>ae("debates")})}):B==="admin"?l.jsx(Dr,{fallback:l.jsxs("div",{className:"space-x-4",children:[l.jsx("button",{onClick:()=>F("login"),className:"bg-brand-primary hover:bg-brand-primary-dark text-white px-6 py-2 rounded-lg transition-colors",children:"Sign In"}),l.jsx("button",{onClick:()=>F("register"),className:"bg-brand-surface-light hover:bg-brand-surface text-white px-6 py-2 rounded-lg transition-colors",children:"Create Account"})]}),children:l.jsx(P0,{})}):B==="debate"&&r?l.jsx(B0,{debate:r,onBack:le,onVote:X,onDebateVote:L,onAddSubArgument:Q,onAddEvidence:$,onRefresh:b}):h?l.jsx("div",{className:"w-full p-4 md:p-8",children:l.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-brand-primary mx-auto mb-4"}),l.jsx("p",{className:"text-brand-text-secondary",children:"Loading debates..."})]})})}):m?l.jsx("div",{className:"w-full p-4 md:p-8",children:l.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-red-500 text-xl mb-4",children:"⚠️"}),l.jsx("p",{className:"text-red-400 mb-4",children:m}),l.jsx("button",{onClick:()=>window.location.reload(),className:"bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg transition-colors",children:"Retry"})]})})}):l.jsxs("div",{className:"w-full p-4 md:p-8",children:[a.length>0&&!A&&l.jsx(q0,{debates:a,onSelect:K}),l.jsx(L0,{onCategorySelect:P,selectedCategory:A}),l.jsx("h2",{className:"text-3xl font-extrabold text-white mb-6",children:A?"Filtered Debates":"Open Debates"}),a.length===0?l.jsxs("div",{className:"text-center py-12",children:[l.jsx("p",{className:"text-brand-text-secondary text-lg mb-4",children:"No debates available yet."}),l.jsx("p",{className:"text-brand-text-secondary",children:"Be the first to start a debate!"})]}):l.jsxs("div",{className:"space-y-6",children:[a.filter(J=>!A||J.category===A).map(J=>l.jsx(Qr,{debate:J,onSelect:K},J.id)),a.filter(J=>!A||J.category===A).length===0&&A&&l.jsxs("div",{className:"text-center py-12",children:[l.jsx("p",{className:"text-brand-text-secondary text-lg mb-4",children:"No debates found in this category."}),l.jsx("p",{className:"text-brand-text-secondary",children:"Try selecting a different category or create a new debate!"})]})]})]})}),l.jsx("footer",{className:"bg-brand-surface mt-12 py-6",children:l.jsx("div",{className:"w-full px-4 sm:px-6 lg:px-8 text-center text-brand-text-light text-sm",children:l.jsxs("p",{children:["© ",new Date().getFullYear()," Agora Platform. All rights reserved."]})})}),l.jsx(V0,{isOpen:x,onClose:()=>y(!1),initialMode:S})]})},tv=()=>l.jsx(Ey,{children:l.jsx(U0,{children:l.jsx(ev,{})})}),bp=document.getElementById("root");if(!bp)throw new Error("Could not find root element to mount to");const nv=Kb.createRoot(bp);nv.render(l.jsx(Am.StrictMode,{children:l.jsx(tv,{})}));
