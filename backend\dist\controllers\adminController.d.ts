import { Response, NextFunction } from 'express';
import { AuthRequest } from '../middleware/auth';
export declare const getUsers: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getUserDetails: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const updateUserRole: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const suspendUser: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getAdminStats: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=adminController.d.ts.map