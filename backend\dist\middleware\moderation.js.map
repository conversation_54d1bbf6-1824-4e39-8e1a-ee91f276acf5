{"version": 3, "file": "moderation.js", "sourceRoot": "", "sources": ["../../src/middleware/moderation.ts"], "names": [], "mappings": ";;;AAUA,MAAM,cAAc,GAAG,IAAI,GAAG,EAA2B,CAAC;AAG1D,MAAM,mBAAmB,GAAG;IAC1B,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;CACxE,CAAC;AAGK,MAAM,uBAAuB,GAAG,CAAC,GAAgB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC7F,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;IAE1B,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QACtC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,2BAA2B;SACrC,CAAC,CAAC;IACL,CAAC;IAED,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IAG5C,MAAM,uBAAuB,GAAG,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC9D,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CACvC,CAAC;IAEF,IAAI,uBAAuB,EAAE,CAAC;QAC5B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,wGAAwG;SAClH,CAAC,CAAC;IACL,CAAC;IAGD,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAClD,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;QAClB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,4EAA4E;SACtF,CAAC,CAAC;IACL,CAAC;IAGD,MAAM,cAAc,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IACzE,IAAI,cAAc,GAAG,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;QAC7C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,gFAAgF;SAC1F,CAAC,CAAC;IACL,CAAC;IAGD,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACrC,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;IACnC,MAAM,eAAe,GAAG,WAAW,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;IACxD,IAAI,eAAe,GAAG,GAAG,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;QAC/C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,wEAAwE;SAClF,CAAC,CAAC;IACL,CAAC;IAED,IAAI,EAAE,CAAC;IACP,OAAO;AACT,CAAC,CAAC;AAvDW,QAAA,uBAAuB,2BAuDlC;AAGK,MAAM,uBAAuB,GAAG,CAAC,GAAgB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC7F,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;IAE3C,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAC5C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,8BAA8B;SACxC,CAAC,CAAC;IACL,CAAC;IAED,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IAelD,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QAC5C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,0DAA0D;SACpE,CAAC,CAAC;IACL,CAAC;IAGD,IAAI,CAAC;QACH,MAAM,iBAAiB,GAAG,CAAC,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QACtE,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QAC7D,IAAI,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;YACjE,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,qFAAqF;aAC/F,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,+BAA+B;SACzC,CAAC,CAAC;IACL,CAAC;IAGD,MAAM,YAAY,GAAG;QACnB,WAAW,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC;QACjE,OAAO,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,YAAY,CAAC;QACzE,gBAAgB,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,CAAC;QAC9E,YAAY,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC;QACtE,iBAAiB,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC;KAClF,CAAC;IAEF,IAAI,IAAI,KAAK,OAAO,IAAI,YAAY,CAAC,IAAiC,CAAC,EAAE,CAAC;QACxE,MAAM,QAAQ,GAAG,YAAY,CAAC,IAAiC,CAAC,CAAC;QACjE,MAAM,mBAAmB,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAClD,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAC/E,CAAC;QAEF,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACzB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,kBAAkB,IAAI,wGAAwG;aACxI,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,IAAI,EAAE,CAAC;IACP,OAAO;AACT,CAAC,CAAC;AA1EW,QAAA,uBAAuB,2BA0ElC;AAGF,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAgD,CAAC;AAE1E,MAAM,gBAAgB,GAAG,CAAC,aAAqB,EAAE,EAAE,gBAAwB,EAAE,EAAE,EAAE;IACtF,OAAO,CAAC,GAAgB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QAC7D,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;QAC5B,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yBAAyB;aACnC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,aAAa,GAAG,EAAE,GAAG,IAAI,CAAC;QAE3C,MAAM,WAAW,GAAG,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEjD,IAAI,CAAC,WAAW,IAAI,GAAG,GAAG,WAAW,CAAC,SAAS,EAAE,CAAC;YAEhD,gBAAgB,CAAC,GAAG,CAAC,MAAM,EAAE;gBAC3B,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,GAAG,GAAG,QAAQ;aAC1B,CAAC,CAAC;YACH,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,IAAI,WAAW,CAAC,KAAK,IAAI,UAAU,EAAE,CAAC;YACpC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;YACjE,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,oDAAoD,OAAO,WAAW;aAChF,CAAC,CAAC;QACL,CAAC;QAED,WAAW,CAAC,KAAK,EAAE,CAAC;QACpB,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAnCW,QAAA,gBAAgB,oBAmC3B;AAGK,MAAM,qBAAqB,GAAG,CAAC,WAAmB,CAAC,EAAE,EAAE;IAC5D,OAAO,KAAK,EAAE,GAAgB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;QAClF,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAE5C,IAAI,CAAC;YAEH,MAAM,MAAM,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC;YAGnD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC/C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,kBAAkB;iBAC5B,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAGD,MAAM,iBAAiB,GAAG,CAAC,IAAW,EAAE,QAAgB,EAAE,eAAuB,CAAC,EAAU,EAAE;gBAC5F,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;oBACvB,IAAI,GAAG,CAAC,EAAE,KAAK,QAAQ,EAAE,CAAC;wBACxB,OAAO,YAAY,CAAC;oBACtB,CAAC;oBAED,IAAI,GAAG,CAAC,YAAY,IAAI,GAAG,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACpD,MAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;wBAC9E,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;4BACjB,OAAO,KAAK,CAAC;wBACf,CAAC;oBACH,CAAC;gBACH,CAAC;gBACD,OAAO,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC;YAEF,MAAM,WAAW,GAAG,iBAAiB,CAAC,MAAM,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YAEpE,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE,CAAC;gBACvB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,2BAA2B;iBACrC,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,MAAM,QAAQ,GAAG,WAAW,GAAG,CAAC,CAAC;YAEjC,IAAI,QAAQ,IAAI,QAAQ,EAAE,CAAC;gBACzB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,6BAA6B,QAAQ,wEAAwE;iBACvH,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,CAAC;QACd,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AA5DW,QAAA,qBAAqB,yBA4DhC;AAGK,MAAM,aAAa,GAAG,KAAK,EAAE,GAAgB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IACxG,IAAI,CAAC;QACH,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QACpD,MAAM,IAAI,GAAG,GAAG,CAAC,IAAK,CAAC;QAEvB,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC;YAC1C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,2CAA2C;aACrD,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,MAAM,YAAY,GAAG;YACnB,wBAAwB;YACxB,YAAY;YACZ,MAAM;YACN,gBAAgB;YAChB,WAAW;YACX,iBAAiB;YACjB,OAAO;SACR,CAAC;QAEF,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACnC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,uBAAuB;aACjC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,MAAM,SAAS,GAAG,GAAG,WAAW,IAAI,SAAS,EAAE,CAAC;QAChD,MAAM,eAAe,GAAG,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAG5D,MAAM,eAAe,GAAG,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;QACtF,IAAI,eAAe,EAAE,CAAC;YACpB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,wCAAwC;aAClD,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAGD,eAAe,CAAC,IAAI,CAAC;YACnB,UAAU,EAAE,IAAI,CAAC,EAAE;YACnB,MAAM;YACN,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAC;QAEH,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;QAG/C,IAAI,eAAe,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAGhC,OAAO,CAAC,GAAG,CAAC,WAAW,SAAS,iBAAiB,eAAe,CAAC,MAAM,iCAAiC,CAAC,CAAC;QAC5G,CAAC;QAED,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,oFAAoF;SAC9F,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAnEW,QAAA,aAAa,iBAmExB;AAGK,MAAM,iBAAiB,GAAG,KAAK,EAAE,GAAgB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IAC5G,IAAI,CAAC;QACH,MAAM,IAAI,GAAG,GAAG,CAAC,IAAK,CAAC;QAGvB,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACvD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,+CAA+C;aACzD,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAKR,EAAE,CAAC;QAER,KAAK,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YACzD,MAAM,CAAC,WAAW,EAAE,SAAS,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAChD,OAAO,CAAC,IAAI,CAAC;gBACX,SAAS;gBACT,WAAW;gBACX,WAAW,EAAE,UAAU,CAAC,MAAM;gBAC9B,OAAO,EAAE,UAAU;aACpB,CAAC,CAAC;QACL,CAAC;QAGD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC;QAEtD,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,OAAO;SACd,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAxCW,QAAA,iBAAiB,qBAwC5B"}