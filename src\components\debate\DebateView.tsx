
import React, { useState, useEffect } from 'react';
import { Debate, VoteSide, DebatePosition, Reference, Evidence, Argument } from '../../types';
import { ParticipantIcon } from '../ui/ParticipantIcon';
import { ArgumentTree } from './ArgumentTree';
import { AddArgumentForm } from './AddArgumentForm';
import { LiveDebateStage } from './LiveDebateStage';
import { FocusedArgumentView } from './FocusedArgumentView';
import { ArgumentBreadcrumbs } from './ArgumentBreadcrumbs';
import { ChatView } from './ChatView';
import { useSocket } from '../../contexts/SocketContext';
import { ArrowLeftIcon, LinkIcon, PlusIcon } from '../ui/icons';
import { debatesAPI } from '../../utils/api';
import { useAuth } from '../../contexts/AuthContext';

interface DebateViewProps {
  debate: Debate;
  onBack: () => void;
  onVote: (side: VoteSide, argumentId?: string) => void;
  onDebateVote: (position: DebatePosition) => void;
  onAddSubArgument: (parentId: string, text: string, isChallenge: boolean) => void;
  onAddEvidence: (argumentId: string, evidence: Partial<Evidence>) => void;
  onRefresh: () => void;
}

const ReferenceList: React.FC<{references: Reference[]}> = ({ references }) => (
  <div>
    <h4 className="text-sm font-semibold text-gray-400 uppercase tracking-wider mb-2">Evidence</h4>
    <ul className="space-y-1">
      {references.map(ref => (
        <li key={ref.id}>
          <a href={ref.url} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2 text-brand-secondary/80 hover:text-brand-secondary text-sm transition-colors">
            <LinkIcon className="w-4 h-4" />
            <span>{ref.title}</span>
          </a>
        </li>
      ))}
    </ul>
  </div>
);

export const DebateView: React.FC<DebateViewProps> = ({
  debate,
  onBack,
  onVote,
  onDebateVote,
  onAddSubArgument,
  onAddEvidence,
  onRefresh
}) => {
  const { user } = useAuth();
  const { joinDebate, leaveDebate } = useSocket();
  const [showAddArgumentFor, setShowAddArgumentFor] = useState(false);
  const [showAddArgumentAgainst, setShowAddArgumentAgainst] = useState(false);
  const [isSubmittingArgument, setIsSubmittingArgument] = useState(false);

  // Focused argument navigation state
  const [focusedArgument, setFocusedArgument] = useState<Argument | null>(null);
  const [chatArgument, setChatArgument] = useState<Argument | null>(null);
  const [breadcrumbPath, setBreadcrumbPath] = useState<Array<{ id: string; text: string; type: 'claim' | 'argument' }>>([
    { id: 'main-claim', text: debate.claim.text, type: 'claim' }
  ]);

  // Calculate progress bar percentages
  const supportVotes = debate.supportVotes || 0;
  const opposeVotes = debate.opposeVotes || 0;
  const totalVotes = supportVotes + opposeVotes;

  let supportPercentage = 50; // Default to 50/50 when no votes
  let opposePercentage = 50;

  if (totalVotes > 0) {
    supportPercentage = (supportVotes / totalVotes) * 100;
    opposePercentage = (opposeVotes / totalVotes) * 100;
  }

  const forArguments = debate.arguments.filter(a => a.side === VoteSide.FOR);
  const againstArguments = debate.arguments.filter(a => a.side === VoteSide.AGAINST);

  // Check if user can add arguments
  const canAddArguments = user && (
    debate.type === 'open' ||
    debate.participants.some(p => p.id === user.id)
  );

  // Join debate room for real-time updates
  useEffect(() => {
    if (user && debate.id) {
      console.log('DebateView: Joining debate room:', debate.id);
      joinDebate(debate.id);

      return () => {
        console.log('DebateView: Leaving debate room:', debate.id);
        leaveDebate(debate.id);
      };
    }
  }, [user, debate.id, joinDebate, leaveDebate]);

  // Debug user authentication and debate data
  console.log('DebateView - Debug info:', {
    isAuthenticated: !!user,
    username: user?.username,
    canAddArguments,
    debateId: debate.id,
    debateType: debate.type,
    isLive: debate.isLive,
    hasVoiceDebate: !!debate.voiceDebate
  });

  // Helper function to find argument by ID in the debate tree
  const findArgumentById = (argumentId: string, args: Argument[] = debate.arguments): Argument | null => {
    for (const arg of args) {
      if (arg.id === argumentId) return arg;
      if (arg.subArguments) {
        const found = findArgumentById(argumentId, arg.subArguments);
        if (found) return found;
      }
    }
    return null;
  };

  // Helper function to build breadcrumb path to an argument
  const buildBreadcrumbPath = (targetArgument: Argument): Array<{ id: string; text: string; type: 'claim' | 'argument' }> => {
    const path = [{ id: 'main-claim', text: debate.claim.text, type: 'claim' as const }];

    const findPath = (args: Argument[], target: Argument, currentPath: Array<{ id: string; text: string; type: 'claim' | 'argument' }>): boolean => {
      for (const arg of args) {
        const newPath = [...currentPath, { id: arg.id, text: arg.text, type: 'argument' as const }];

        if (arg.id === target.id) {
          path.push(...newPath.slice(1)); // Skip the claim part since it's already added
          return true;
        }

        if (arg.subArguments && findPath(arg.subArguments, target, newPath)) {
          return true;
        }
      }
      return false;
    };

    findPath(debate.arguments, targetArgument, []);
    return path;
  };

  // Handle argument click to focus it
  const handleArgumentClick = (argument: Argument) => {
    setFocusedArgument(argument);
    setBreadcrumbPath(buildBreadcrumbPath(argument));
  };

  // Handle breadcrumb navigation
  const handleBreadcrumbNavigate = (index: number) => {
    if (index === 0) {
      // Navigate back to main claim
      setFocusedArgument(null);
      setBreadcrumbPath([{ id: 'main-claim', text: debate.claim.text, type: 'claim' }]);
    } else {
      // Navigate to specific argument in the path
      const targetItem = breadcrumbPath[index];
      if (targetItem.type === 'argument') {
        const argument = findArgumentById(targetItem.id);
        if (argument) {
          setFocusedArgument(argument);
          setBreadcrumbPath(breadcrumbPath.slice(0, index + 1));
        }
      }
    }
  };

  const handleArgumentVote = async (argumentId: string, voteType: 'up' | 'down') => {
    try {
      await debatesAPI.voteOnArgument(debate.id, argumentId, voteType);
      onRefresh(); // Refresh the debate data to show updated votes
    } catch (error) {
      console.error('Error voting on argument:', error);
    }
  };

  const handleAddSubArgument = async (parentId: string, text: string, isChallenge: boolean) => {
    try {
      await debatesAPI.addSubArgument(debate.id, parentId, { text, isChallenge });
      onRefresh(); // Refresh the debate data to show new sub-argument
    } catch (error) {
      console.error('Error adding sub-argument:', error);
    }
  };

  const handleAddEvidence = async (argumentId: string, evidence: Partial<Evidence>) => {
    try {
      console.log('Adding evidence:');
      console.log('- debateId:', debate.id);
      console.log('- argumentId:', argumentId);
      console.log('- evidence:', JSON.stringify(evidence, null, 2));
      await debatesAPI.addEvidence(debate.id, argumentId, evidence);
      await onRefresh(); // Refresh the debate data to show new evidence

      // Update focused argument if it's the one we just added evidence to
      if (focusedArgument && focusedArgument.id === argumentId) {
        const updatedArgument = findArgumentById(argumentId);
        if (updatedArgument) {
          setFocusedArgument(updatedArgument);
        }
      }
    } catch (error) {
      console.error('Error adding evidence:', error);
      console.error('Error details:', error.response?.data);
    }
  };

  const handleVoteOnEvidence = async (argumentId: string, evidenceId: string, vote: 'accurate' | 'inaccurate') => {
    if (!user) {
      console.error('User not authenticated');
      alert('You must be logged in to vote on evidence');
      return;
    }

    try {
      console.log('Voting on evidence:');
      console.log('- debateId:', debate.id);
      console.log('- argumentId:', argumentId);
      console.log('- evidenceId:', evidenceId);
      console.log('- vote:', vote);
      console.log('- user:', user.username);

      const result = await debatesAPI.voteOnEvidence(debate.id, argumentId, evidenceId, vote);
      console.log('Vote result:', result);

      await onRefresh(); // Refresh the debate data to show updated vote counts

      // Update focused argument if it's the one we just voted on evidence for
      if (focusedArgument && focusedArgument.id === argumentId) {
        const updatedArgument = findArgumentById(argumentId);
        if (updatedArgument) {
          setFocusedArgument(updatedArgument);
        }
      }
    } catch (error: any) {
      console.error('Error voting on evidence:', error);
      console.error('Error details:', error.response?.data);

      if (error.response?.status === 401) {
        alert('Authentication failed. Please log in again.');
      } else if (error.response?.status === 400) {
        alert(error.response.data?.message || 'Invalid vote request');
      } else {
        alert('Failed to vote on evidence. Please try again.');
      }
    }
  };

  const handleAddComment = async (argumentId: string, text: string, parentCommentId?: string) => {
    if (!user) {
      console.error('User not authenticated');
      alert('You must be logged in to comment');
      return;
    }

    try {
      const result = await debatesAPI.addComment(debate.id, argumentId, { text, parentCommentId });

      // Optimistically update the focused argument with the new comment
      if (focusedArgument && focusedArgument.id === argumentId) {
        const newComment = result.data;
        const updatedArgument = { ...focusedArgument };

        if (!updatedArgument.comments) {
          updatedArgument.comments = [];
        }

        if (parentCommentId) {
          // Add as reply to existing comment
          const addReplyToComment = (comments: any[]): boolean => {
            for (const comment of comments) {
              if (comment.id === parentCommentId) {
                if (!comment.replies) comment.replies = [];
                comment.replies.push(newComment);
                return true;
              }
              if (comment.replies && addReplyToComment(comment.replies)) {
                return true;
              }
            }
            return false;
          };
          addReplyToComment(updatedArgument.comments);
        } else {
          // Add as top-level comment
          updatedArgument.comments.push(newComment);
        }

        setFocusedArgument(updatedArgument);
      }

      // Update chat argument if it's the one we just commented on
      if (chatArgument && chatArgument.id === argumentId) {
        const updatedArgument = { ...chatArgument };

        if (!updatedArgument.comments) {
          updatedArgument.comments = [];
        }

        if (parentCommentId) {
          // Add as reply to existing comment
          const addReplyToComment = (comments: any[]): boolean => {
            for (const comment of comments) {
              if (comment.id === parentCommentId) {
                if (!comment.replies) comment.replies = [];
                comment.replies.push(result.data);
                return true;
              }
              if (comment.replies && addReplyToComment(comment.replies)) {
                return true;
              }
            }
            return false;
          };
          addReplyToComment(updatedArgument.comments);
        } else {
          // Add as top-level comment
          updatedArgument.comments.push(result.data);
        }

        setChatArgument(updatedArgument);
      }

      // Refresh in background to sync with server
      onRefresh();
    } catch (error: any) {
      console.error('Error adding comment:', error);
      if (error.response?.status === 401) {
        alert('Authentication failed. Please log in again.');
      } else {
        alert('Failed to add comment. Please try again.');
      }
    }
  };

  const handleVoteOnComment = async (argumentId: string, commentId: string, voteType: 'up' | 'down') => {
    if (!user) {
      console.error('User not authenticated');
      alert('You must be logged in to vote on comments');
      return;
    }

    try {
      await debatesAPI.voteOnComment(debate.id, argumentId, commentId, voteType);

      // Optimistically update the focused argument with the vote
      if (focusedArgument && focusedArgument.id === argumentId) {
        const updatedArgument = { ...focusedArgument };

        const updateCommentVote = (comments: any[]): boolean => {
          for (const comment of comments) {
            if (comment.id === commentId) {
              if (voteType === 'up') {
                comment.upvotes = (comment.upvotes || 0) + 1;
              } else {
                comment.downvotes = (comment.downvotes || 0) + 1;
              }
              comment.votes = (comment.upvotes || 0) - (comment.downvotes || 0);
              return true;
            }
            if (comment.replies && updateCommentVote(comment.replies)) {
              return true;
            }
          }
          return false;
        };

        if (updatedArgument.comments) {
          updateCommentVote(updatedArgument.comments);
          setFocusedArgument(updatedArgument);
        }
      }

      // Update chat argument if it's the one we just voted on a comment for
      if (chatArgument && chatArgument.id === argumentId) {
        const updatedArgument = { ...chatArgument };

        const updateCommentVote = (comments: any[]): boolean => {
          for (const comment of comments) {
            if (comment.id === commentId) {
              if (voteType === 'up') {
                comment.upvotes = (comment.upvotes || 0) + 1;
              } else {
                comment.downvotes = (comment.downvotes || 0) + 1;
              }
              comment.votes = (comment.upvotes || 0) - (comment.downvotes || 0);
              return true;
            }
            if (comment.replies && updateCommentVote(comment.replies)) {
              return true;
            }
          }
          return false;
        };

        if (updatedArgument.comments) {
          updateCommentVote(updatedArgument.comments);
          setChatArgument(updatedArgument);
        }
      }

      // Refresh in background to sync with server
      onRefresh();
    } catch (error: any) {
      console.error('Error voting on comment:', error);
      if (error.response?.status === 401) {
        alert('Authentication failed. Please log in again.');
      } else {
        alert('Failed to vote on comment. Please try again.');
      }
    }
  };

  const handleAddArgument = async (text: string, side: VoteSide, references: Reference[]) => {
    setIsSubmittingArgument(true);
    try {
      await debatesAPI.addArgument(debate.id, { text, side, references });
      onRefresh(); // Refresh the debate data to show new argument
      setShowAddArgumentFor(false);
      setShowAddArgumentAgainst(false);
    } catch (error) {
      console.error('Error adding argument:', error);
      throw error; // Re-throw to let the form handle the error
    } finally {
      setIsSubmittingArgument(false);
    }
  };

  const handleChatClick = (argument: Argument) => {
    setChatArgument(argument);
    // Don't clear focused argument - keep the main view intact
  };

  const handleBackFromChat = () => {
    setChatArgument(null);
  };

  const handleArgumentUpdate = (updatedArgument: Argument) => {
    setChatArgument(updatedArgument);
  };

  const handleReportComment = async (argumentId: string, commentId: string) => {
    if (!user) {
      console.error('User not authenticated');
      return;
    }

    // Optimistically update the chat argument to show the report immediately
    if (chatArgument && chatArgument.id === argumentId) {
      const updatedArgument = { ...chatArgument };

      const updateCommentReport = (comments: any[]): boolean => {
        for (const comment of comments) {
          if (comment.id === commentId) {
            if (!comment.reportedBy) comment.reportedBy = [];
            if (!comment.reportedBy.includes(user.id)) {
              comment.reportedBy.push(user.id);
            }
            return true;
          }
          if (comment.replies && updateCommentReport(comment.replies)) {
            return true;
          }
        }
        return false;
      };

      if (updatedArgument.comments) {
        updateCommentReport(updatedArgument.comments);
        setChatArgument(updatedArgument);
      }
    }

    try {
      await debatesAPI.reportComment(debate.id, argumentId, commentId);
      // No alert needed - visual feedback through red flag icon is sufficient
    } catch (error: any) {
      console.error('Failed to report comment:', error);
      // Silent failure - optimistic update already shows the report
    }
  };

  return (
    <div className="w-full p-4 md:p-8 relative">
      <button onClick={onBack} className="flex items-center gap-2 text-brand-text-light hover:text-white mb-8 transition-colors">
        <ArrowLeftIcon className="w-5 h-5"/>
        Back to All Debates
      </button>

      {/* Main Claim */}
      <div className="bg-brand-surface rounded-xl shadow-lg p-6 mb-8">
        <h1 className="text-3xl font-extrabold text-white mb-2">{debate.title}</h1>
        <p className="text-brand-text-light mb-6">{debate.description}</p>
        <div className="bg-brand-bg/50 rounded-lg p-6">
            <p className="text-lg text-brand-text mb-4 italic">"{debate.claim.text}"</p>
            <div className="flex justify-between items-end">
                {debate.claim.references.length > 0 && <ReferenceList references={debate.claim.references} />}
                <div className="flex flex-col items-end">
                    <ParticipantIcon participant={debate.claim.author} />
                    <span className="text-sm font-semibold mt-2 text-brand-text-light">{debate.claim.author.name}</span>
                </div>
            </div>
        </div>
      </div>
      
      {/* Voting Section */}
      <div className="mb-8">
        <h2 className="text-xl font-bold text-center mb-4">Overall Vote</h2>
        <div className="flex items-center gap-4 mb-4">
            <div className="flex flex-col items-center">
              <button
                onClick={() => onDebateVote(DebatePosition.SUPPORT)}
                className="bg-success/80 hover:bg-success text-white font-bold py-3 px-6 rounded-lg transition-colors w-32 text-center mb-2"
              >
                SUPPORT
              </button>
              <span className="text-success font-bold text-lg">{supportVotes}</span>
            </div>
            <div className="w-full bg-brand-surface rounded-full h-6 flex overflow-hidden">
                <div className="bg-success h-6 transition-all duration-300" style={{ width: `${supportPercentage}%` }}></div>
                <div className="bg-danger h-6 transition-all duration-300" style={{ width: `${opposePercentage}%` }}></div>
            </div>
            <div className="flex flex-col items-center">
              <button
                onClick={() => onDebateVote(DebatePosition.OPPOSE)}
                className="bg-danger/80 hover:bg-danger text-white font-bold py-3 px-6 rounded-lg transition-colors w-32 text-center mb-2"
              >
                OPPOSE
              </button>
              <span className="text-danger font-bold text-lg">{opposeVotes}</span>
            </div>
        </div>
      </div>

      {/* Voice Debate Section - Compact integration */}
      <div className="mb-8">
        <LiveDebateStage debate={debate} onRefresh={onRefresh} />
      </div>

      {/* Breadcrumb Navigation */}
      <ArgumentBreadcrumbs
        breadcrumbPath={breadcrumbPath}
        onNavigate={handleBreadcrumbNavigate}
      />

      {/* Focused Argument View or Arguments Section */}
      {focusedArgument ? (
        <FocusedArgumentView
          argument={focusedArgument}
          onVote={handleArgumentVote}
          onAddSubArgument={handleAddSubArgument}
          onAddEvidence={handleAddEvidence}
          onVoteOnEvidence={handleVoteOnEvidence}
          onArgumentClick={handleArgumentClick}
          canAddArguments={canAddArguments}
          onRefresh={onRefresh}
          currentUserId={user?.id}
          onChatClick={handleChatClick}
        />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-stretch">
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-2xl font-bold border-l-4 border-success pl-3 text-success">Arguments For</h3>
            {canAddArguments && !showAddArgumentFor && (
              <button
                onClick={() => setShowAddArgumentFor(true)}
                className="flex items-center gap-2 px-4 py-2 bg-success/20 hover:bg-success/30 text-success border border-success/50 rounded-lg transition-colors"
              >
                <PlusIcon className="w-4 h-4" />
                Add Argument
              </button>
            )}
          </div>
          <div className="space-y-4">
            {showAddArgumentFor && (
              <AddArgumentForm
                side={VoteSide.FOR}
                onSubmit={handleAddArgument}
                onCancel={() => setShowAddArgumentFor(false)}
                isSubmitting={isSubmittingArgument}
              />
            )}
            {forArguments.length > 0 ? (
              forArguments.map(arg => (
                <ArgumentTree
                  key={arg.id}
                  argument={arg}
                  onVote={handleArgumentVote}
                  onAddSubArgument={handleAddSubArgument}
                  onAddEvidence={handleAddEvidence}
                  onVoteOnEvidence={handleVoteOnEvidence}
                  onArgumentClick={handleArgumentClick}
                  onChatClick={handleChatClick}
                  currentUserId={user?.id}
                />
              ))
            ) : !showAddArgumentFor && <p className="text-brand-text-light italic">No arguments for this side yet.</p>}
          </div>
        </div>
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-2xl font-bold border-l-4 border-danger pl-3 text-danger">Arguments Against</h3>
            {canAddArguments && !showAddArgumentAgainst && (
              <button
                onClick={() => setShowAddArgumentAgainst(true)}
                className="flex items-center gap-2 px-4 py-2 bg-danger/20 hover:bg-danger/30 text-danger border border-danger/50 rounded-lg transition-colors"
              >
                <PlusIcon className="w-4 h-4" />
                Add Argument
              </button>
            )}
          </div>
          <div className="space-y-4">
            {showAddArgumentAgainst && (
              <AddArgumentForm
                side={VoteSide.AGAINST}
                onSubmit={handleAddArgument}
                onCancel={() => setShowAddArgumentAgainst(false)}
                isSubmitting={isSubmittingArgument}
              />
            )}
            {againstArguments.length > 0 ? (
              againstArguments.map(arg => (
                <ArgumentTree
                  key={arg.id}
                  argument={arg}
                  onVote={handleArgumentVote}
                  onAddSubArgument={handleAddSubArgument}
                  onAddEvidence={handleAddEvidence}
                  onVoteOnEvidence={handleVoteOnEvidence}
                  onArgumentClick={handleArgumentClick}
                  onChatClick={handleChatClick}
                  currentUserId={user?.id}
                />
              ))
            ) : !showAddArgumentAgainst && <p className="text-brand-text-light italic">No arguments for this side yet.</p>}
          </div>
        </div>
        </div>
      )}

      {/* Chat Panel Overlay */}
      {chatArgument && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/20 z-40"
            onClick={handleBackFromChat}
          />

          {/* Chat Panel */}
          <ChatView
            argument={chatArgument}
            debateId={debate.id}
            onBack={handleBackFromChat}
            onAddComment={handleAddComment}
            onVoteOnComment={handleVoteOnComment}
            onReportComment={handleReportComment}
            onArgumentUpdate={handleArgumentUpdate}
          />
        </>
      )}
    </div>
  );
};
