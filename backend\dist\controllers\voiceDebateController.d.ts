import { Response, NextFunction } from 'express';
import { AuthRequest } from '../middleware/auth';
export declare const initializeVoiceDebate: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const joinVoiceQueue: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const leaveVoiceQueue: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const nextSpeaker: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const giveTime: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getTimeBlocks: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const allocateTime: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const grantTimeBlocks: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getVoiceDebateState: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const updateVoiceSettings: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const reportMutualExclusion: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const releaseMutualExclusion: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=voiceDebateController.d.ts.map