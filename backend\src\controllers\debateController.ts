import { Response, NextFunction } from 'express';
import { AuthRequest } from '../middleware/auth';
import Debate from '../models/Debate';
import User from '../models/User';
import { v4 as uuidv4 } from 'uuid';
import { getDisplayName } from '../utils/userDisplay';
import { io } from '../server';

// Migration function to reset debate vote totals (removed confusing calculateDebateVoteTotals)
export const recalculateAllDebateVoteTotals = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const debates = await Debate.find({});
    let updatedCount = 0;

    for (const debate of debates) {
      // Reset to clean state - debate votes should be independent of argument votes
      const oldSupportVotes = debate.supportVotes || 0;
      const oldOpposeVotes = debate.opposeVotes || 0;

      // Initialize clean voting system if not already present
      if (!debate.userVotes) {
        debate.userVotes = [];
      }
      if (debate.supportVotes === undefined) {
        debate.supportVotes = 0;
      }
      if (debate.opposeVotes === undefined) {
        debate.opposeVotes = 0;
      }

      if (debate.supportVotes !== oldSupportVotes || debate.opposeVotes !== oldOpposeVotes) {
        await debate.save();
        updatedCount++;
        console.log(`Updated debate ${debate._id}: SUPPORT ${oldSupportVotes} -> ${debate.supportVotes}, OPPOSE ${oldOpposeVotes} -> ${debate.opposeVotes}`);
      }
    }

    res.json({
      success: true,
      message: `Initialized clean voting system for ${updatedCount} debates`,
      data: { updatedCount, totalDebates: debates.length }
    });
  } catch (error) {
    next(error);
  }
};

// Helper function to calculate argument strength recursively
const calculateArgumentStrength = async (debate: any): Promise<void> => {
  const calculateStrength = (argument: any): number => {
    // Base score from votes (logarithmic scaling to prevent vote manipulation)
    const netVotes = Math.max(0, argument.upvotes - argument.downvotes);
    const totalVotes = argument.upvotes + argument.downvotes;
    const voteRatio = totalVotes > 0 ? argument.upvotes / totalVotes : 0.5;
    const voteScore = Math.log(netVotes + 1) * 2 + (voteRatio - 0.5) * 10;

    // Evidence score based on quantity, verification status, and community votes
    let evidenceScore = 0;
    if (argument.evidence && argument.evidence.length > 0) {
      const weightedEvidenceSum = argument.evidence.reduce((sum: number, ev: any) => {
        let verificationMultiplier = 1;

        // Base multiplier from verification status
        switch (ev.verificationStatus) {
          case 'verified': verificationMultiplier = 1.2; break;
          case 'pending': verificationMultiplier = 1.0; break;
          case 'disputed': verificationMultiplier = 0.5; break;
          case 'unverified': verificationMultiplier = 0.8; break;
        }

        // Additional multiplier based on community verification score
        if (ev.totalVotes && ev.totalVotes > 0) {
          const communityMultiplier = 0.8 + (ev.verificationScore / 100) * 0.4; // Range: 0.8 to 1.2
          verificationMultiplier *= communityMultiplier;
        }

        // Base evidence value of 5 (equivalent to old default strength)
        return sum + (5 * verificationMultiplier);
      }, 0);
      evidenceScore = weightedEvidenceSum / argument.evidence.length;
    }

    // Sub-arguments score with diminishing returns
    let subArgumentScore = 0;
    if (argument.subArguments && argument.subArguments.length > 0) {
      let supportingScore = 0;
      let challengingScore = 0;
      let supportingCount = 0;
      let challengingCount = 0;

      for (const subArg of argument.subArguments) {
        const subScore = calculateStrength(subArg);
        if (subArg.isChallenge) {
          challengingScore += subScore;
          challengingCount++;
        } else {
          supportingScore += subScore;
          supportingCount++;
        }
      }

      // Apply diminishing returns for multiple sub-arguments
      const supportingAvg = supportingCount > 0 ? supportingScore / supportingCount : 0;
      const challengingAvg = challengingCount > 0 ? challengingScore / challengingCount : 0;

      // Supporting arguments boost strength with diminishing returns
      const supportingBoost = supportingAvg * Math.min(1, supportingCount * 0.3) * 0.6;

      // Challenging arguments reduce strength but with resistance
      const challengingReduction = challengingAvg * Math.min(1, challengingCount * 0.25) * 0.4;

      subArgumentScore = supportingBoost - challengingReduction;
    }

    // Reference quality bonus (small boost for having references)
    const referenceBonus = argument.references && argument.references.length > 0
      ? Math.min(5, argument.references.length * 1.5)
      : 0;

    // Calculate final strength with weighted components
    const baseStrength = 30; // Minimum base strength for any argument
    const totalStrength = Math.max(0, Math.min(100,
      baseStrength +
      voteScore * 0.3 +
      evidenceScore * 4 +
      subArgumentScore +
      referenceBonus
    ));

    argument.strengthScore = Math.round(totalStrength);
    return totalStrength;
  };

  // Calculate strength for all arguments recursively (depth-first)
  const processArgument = (argument: any) => {
    // First process all sub-arguments
    if (argument.subArguments && argument.subArguments.length > 0) {
      for (const subArg of argument.subArguments) {
        processArgument(subArg);
      }
    }
    // Then calculate this argument's strength
    calculateStrength(argument);
  };

  // Process all top-level arguments
  for (const argument of debate.arguments) {
    processArgument(argument);
  }
};

// GET /api/debates
export const getDebates = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    const filter: any = {};
    if (req.query.status) filter.status = req.query.status;
    if (req.query.isLive !== undefined) filter.isLive = req.query.isLive === 'true';

    const debates = await Debate.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Debate.countDocuments(filter);

    res.json({
      success: true,
      data: debates,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    next(error);
  }
};

// GET /api/debates/:id
export const getDebateById = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const debate = await Debate.findById(req.params.id);

    if (!debate) {
      res.status(404).json({
        success: false,
        message: 'Debate not found'
      });
      return;
    }

    res.json({
      success: true,
      data: debate
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/debates
export const createDebate = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { title, description, category, claim, opponent, type } = req.body;
    const user = req.user!;

    // Create participant objects
    const proponent = {
      id: (user._id as any).toString(),
      name: getDisplayName(user),
      avatarUrl: user.avatarUrl || `https://picsum.photos/seed/${user.username}/40`
    };

    let opponentParticipant = null;
    let participants = [proponent];

    // Handle opponent for one-on-one debates
    if (type === 'one-on-one') {
      if (!opponent || !opponent.id) {
        res.status(400).json({
          success: false,
          message: 'Opponent is required for one-on-one debates'
        });
        return;
      }

      // Find opponent user
      const opponentUser = await User.findById(opponent.id);
      if (!opponentUser) {
        res.status(404).json({
          success: false,
          message: 'Opponent user not found'
        });
        return;
      }

      opponentParticipant = {
        id: (opponentUser._id as any).toString(),
        name: getDisplayName(opponentUser),
        avatarUrl: opponentUser.avatarUrl || `https://picsum.photos/seed/${opponentUser.username}/40`
      };

      participants.push(opponentParticipant);
    }

    // Create the debate
    const debate = new Debate({
      title,
      description,
      category,
      type: type || 'open',
      claim: {
        author: proponent,
        text: claim.text,
        references: claim.references || []
      },
      participants,
      proponent,
      opponent: opponentParticipant,
      arguments: [],
      supportVotes: 0,
      opposeVotes: 0,
      userVotes: [],
      isLive: false,
      status: 'active'
    });

    await debate.save();

    // Update user's debate participation
    await User.findByIdAndUpdate(user._id as any, {
      $push: { debatesCreated: debate._id },
      $inc: { totalVotes: 0 }
    });

    res.status(201).json({
      success: true,
      data: debate,
      message: 'Debate created successfully'
    });
  } catch (error) {
    next(error);
  }
};

// PUT /api/debates/:id
export const updateDebate = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { title, description, status } = req.body;
    const user = req.user!;
    const debateId = req.params.id;

    // Find the debate
    const debate = await Debate.findById(debateId);
    if (!debate) {
      res.status(404).json({
        success: false,
        message: 'Debate not found'
      });
      return;
    }

    // Check if user is the creator (proponent)
    if (debate.proponent.id !== (user._id as any).toString()) {
      res.status(403).json({
        success: false,
        message: 'Only the debate creator can update the debate'
      });
      return;
    }

    // Update fields
    if (title) debate.title = title;
    if (description) debate.description = description;
    if (status) debate.status = status;

    await debate.save();

    res.json({
      success: true,
      data: debate,
      message: 'Debate updated successfully'
    });
  } catch (error) {
    next(error);
  }
};

// DELETE /api/debates/:id
export const deleteDebate = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const user = req.user!;
    const debateId = req.params.id;

    // Find the debate
    const debate = await Debate.findById(debateId);
    if (!debate) {
      res.status(404).json({
        success: false,
        message: 'Debate not found'
      });
      return;
    }

    // Check if user is the creator (proponent) or admin
    if (debate.proponent.id !== (user._id as any).toString() && user.role !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Only the debate creator or admin can delete the debate'
      });
      return;
    }

    await Debate.findByIdAndDelete(debateId);

    res.json({
      success: true,
      message: 'Debate deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/debates/:id/join
export const joinDebate = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const user = req.user!;
    const debateId = req.params.id;

    // Find the debate
    const debate = await Debate.findById(debateId);
    if (!debate) {
      res.status(404).json({
        success: false,
        message: 'Debate not found'
      });
      return;
    }

    // Check if user is already a participant
    const isAlreadyParticipant = debate.participants.some(p => p.id === (user._id as any).toString());
    if (isAlreadyParticipant) {
      res.status(400).json({
        success: false,
        message: 'User is already a participant in this debate'
      });
      return;
    }

    // Add user as participant
    const participant = {
      id: (user._id as any).toString(),
      name: getDisplayName(user),
      avatarUrl: user.avatarUrl || `https://picsum.photos/seed/${user.username}/40`
    };

    debate.participants.push(participant);
    await debate.save();

    // Update user's debate participation
    await User.findByIdAndUpdate(user._id as any, {
      $push: { debatesParticipated: debate._id }
    });

    res.json({
      success: true,
      data: participant,
      message: 'Successfully joined the debate'
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/debates/:id/vote
export const voteOnDebate = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { position } = req.body; // 'support' or 'oppose'
    const user = req.user!;
    const debateId = req.params.id;

    // Find the debate
    const debate = await Debate.findById(debateId);
    if (!debate) {
      res.status(404).json({
        success: false,
        message: 'Debate not found'
      });
      return;
    }

    // Check if debate is active
    if (debate.status !== 'active') {
      res.status(400).json({
        success: false,
        message: 'Can only vote on active debates'
      });
      return;
    }

    // Initialize userVotes array if it doesn't exist
    if (!debate.userVotes) {
      debate.userVotes = [];
    }

    // Check for existing vote
    const userId = (user._id as any).toString();
    const existingVoteIndex = debate.userVotes.findIndex(v => v.userId === userId);

    if (existingVoteIndex !== -1) {
      const existingVote = debate.userVotes[existingVoteIndex];

      if (existingVote.position === position) {
        // Remove vote (toggle off)
        debate.userVotes.splice(existingVoteIndex, 1);
        if (position === 'support') {
          debate.supportVotes = Math.max(0, (debate.supportVotes || 0) - 1);
        } else {
          debate.opposeVotes = Math.max(0, (debate.opposeVotes || 0) - 1);
        }
      } else {
        // Change vote
        existingVote.position = position;
        if (position === 'support') {
          debate.supportVotes = (debate.supportVotes || 0) + 1;
          debate.opposeVotes = Math.max(0, (debate.opposeVotes || 0) - 1);
        } else {
          debate.opposeVotes = (debate.opposeVotes || 0) + 1;
          debate.supportVotes = Math.max(0, (debate.supportVotes || 0) - 1);
        }
      }
    } else {
      // New vote
      debate.userVotes.push({ userId, position });

      if (position === 'support') {
        debate.supportVotes = (debate.supportVotes || 0) + 1;
      } else {
        debate.opposeVotes = (debate.opposeVotes || 0) + 1;
      }
    }

    await debate.save();

    // Update user's total votes (only for new votes, not changes/toggles)
    if (existingVoteIndex === -1) {
      await User.findByIdAndUpdate(user._id as any, {
        $inc: { totalVotes: 1 }
      });
    }

    res.json({
      success: true,
      data: {
        supportVotes: debate.supportVotes,
        opposeVotes: debate.opposeVotes,
        userVote: debate.userVotes.find(v => v.userId === userId)?.position || null
      },
      message: 'Vote recorded successfully'
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/debates/:id/arguments
export const addArgument = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { text, side, references } = req.body;
    const user = req.user!;
    const debateId = req.params.id;

    // Find the debate
    const debate = await Debate.findById(debateId);
    if (!debate) {
      res.status(404).json({
        success: false,
        message: 'Debate not found'
      });
      return;
    }

    // Check participation rules based on debate type
    const userId = (user._id as any).toString();
    const isParticipant = debate.participants.some(p => p.id === userId);

    if (debate.type === 'one-on-one') {
      // For one-on-one debates, only existing participants can add arguments
      if (!isParticipant) {
        res.status(403).json({
          success: false,
          message: 'Only debate participants can add arguments to one-on-one debates'
        });
        return;
      }
    } else if (debate.type === 'open') {
      // For open debates, automatically add user as participant if not already
      if (!isParticipant) {
        const newParticipant = {
          id: userId,
          name: getDisplayName(user),
          avatarUrl: user.avatarUrl || `https://picsum.photos/seed/${user.username}/40`
        };

        debate.participants.push(newParticipant);

        // Update user's debate participation
        await User.findByIdAndUpdate(user._id as any, {
          $push: { debatesParticipated: debate._id }
        });
      }
    }

    // Create the argument
    const argument = {
      id: uuidv4(),
      author: {
        id: (user._id as any).toString(),
        name: getDisplayName(user),
        avatarUrl: user.avatarUrl || `https://picsum.photos/seed/${user.username}/40`
      },
      text,
      references: references || [],
      evidence: [],
      side,
      votes: 0,
      upvotes: 0,
      downvotes: 0,
      userVotes: [], // Initialize empty user votes array
      parentArgumentId: undefined,
      subArguments: [],
      comments: [],
      depth: 0,
      strengthScore: 0,
      isChallenge: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Add argument to debate
    debate.arguments.push(argument);

    await debate.save();

    res.status(201).json({
      success: true,
      data: argument,
      message: 'Argument added successfully'
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/debates/:debateId/arguments/:argumentId/vote
export const voteOnArgument = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { vote } = req.body; // 'up' or 'down'
    const user = req.user!;
    const { debateId, argumentId } = req.params;

    console.log('=== ARGUMENT VOTE DEBUG ===');
    console.log('Vote type:', vote);
    console.log('User:', user.username);
    console.log('Debate ID:', debateId);
    console.log('Argument ID:', argumentId);

    // Find the debate
    const debate = await Debate.findById(debateId);
    if (!debate) {
      res.status(404).json({
        success: false,
        message: 'Debate not found'
      });
      return;
    }

    // Find the argument recursively (including sub-arguments)
    const findArgument = (args: any[], id: string): any => {
      for (const arg of args) {
        if (arg.id === id) return arg;
        const found = findArgument(arg.subArguments || [], id);
        if (found) return found;
      }
      return null;
    };

    const argument = findArgument(debate.arguments, argumentId);
    if (!argument) {
      res.status(404).json({
        success: false,
        message: 'Argument not found'
      });
      return;
    }

    // Initialize userVotes array if it doesn't exist (for existing arguments)
    if (!argument.userVotes) {
      argument.userVotes = [];
    }

    // Check if user has already voted on this argument
    const existingVoteIndex = argument.userVotes.findIndex((v: any) => v.userId === user.id);

    if (existingVoteIndex !== -1) {
      const existingVote = argument.userVotes[existingVoteIndex];

      // If same vote, remove it (toggle off)
      if (existingVote.vote === vote) {
        argument.userVotes.splice(existingVoteIndex, 1);
        // Decrease the vote count
        if (vote === 'up') {
          argument.upvotes = Math.max(0, (argument.upvotes || 0) - 1);
        } else {
          argument.downvotes = Math.max(0, (argument.downvotes || 0) - 1);
        }
      } else {
        // Change vote (up to down or down to up)
        existingVote.vote = vote;
        // Adjust both counts
        if (vote === 'up') {
          argument.upvotes = (argument.upvotes || 0) + 1;
          argument.downvotes = Math.max(0, (argument.downvotes || 0) - 1);
        } else {
          argument.downvotes = (argument.downvotes || 0) + 1;
          argument.upvotes = Math.max(0, (argument.upvotes || 0) - 1);
        }
      }
    } else {
      // New vote
      argument.userVotes.push({ userId: user.id, vote });
      if (vote === 'up') {
        argument.upvotes = (argument.upvotes || 0) + 1;
      } else {
        argument.downvotes = (argument.downvotes || 0) + 1;
      }
    }

    // Update legacy votes field for compatibility
    argument.votes = (argument.upvotes || 0) - (argument.downvotes || 0);
    argument.updatedAt = new Date();

    // Recalculate argument strength scores after vote change
    await calculateArgumentStrength(debate);

    await debate.save();

    res.json({
      success: true,
      data: {
        argumentId,
        votes: argument.votes,
        upvotes: argument.upvotes,
        downvotes: argument.downvotes,
        strengthScore: argument.strengthScore
      },
      message: 'Vote on argument recorded successfully'
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/debates/:debateId/arguments/:argumentId/sub-arguments
export const addSubArgument = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { text, isChallenge, references, evidence } = req.body;
    const user = req.user!;
    const { debateId, argumentId } = req.params;

    // Find the debate
    const debate = await Debate.findById(debateId);
    if (!debate) {
      res.status(404).json({
        success: false,
        message: 'Debate not found'
      });
      return;
    }

    // Find the parent argument recursively
    const findArgument = (args: any[], id: string): any => {
      for (const arg of args) {
        if (arg.id === id) return arg;
        const found = findArgument(arg.subArguments || [], id);
        if (found) return found;
      }
      return null;
    };

    const parentArgument = findArgument(debate.arguments, argumentId);
    if (!parentArgument) {
      res.status(404).json({
        success: false,
        message: 'Parent argument not found'
      });
      return;
    }

    // Check if user is a participant in the debate
    const isParticipant = debate.participants.some(p => p.id === (user._id as any).toString());
    if (!isParticipant) {
      res.status(403).json({
        success: false,
        message: 'Only debate participants can add sub-arguments'
      });
      return;
    }

    // Create the sub-argument
    // If it's a challenge, the sub-argument takes the opposite side of the parent
    // If it's supporting, the sub-argument takes the same side as the parent
    const subArgumentSide = isChallenge
      ? (parentArgument.side === 'FOR' ? 'AGAINST' : 'FOR')
      : parentArgument.side;

    const subArgument = {
      id: uuidv4(),
      author: {
        id: (user._id as any).toString(),
        name: getDisplayName(user),
        avatarUrl: user.avatarUrl || `https://picsum.photos/seed/${user.username}/40`
      },
      text,
      references: references || [],
      evidence: evidence || [],
      side: subArgumentSide,
      votes: 0,
      upvotes: 0,
      downvotes: 0,
      userVotes: [], // Initialize empty user votes array
      parentArgumentId: argumentId,
      subArguments: [],
      comments: [],
      depth: parentArgument.depth + 1,
      strengthScore: 0,
      isChallenge: isChallenge || false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Add sub-argument to parent
    if (!parentArgument.subArguments) {
      parentArgument.subArguments = [];
    }
    parentArgument.subArguments.push(subArgument);

    // Recalculate strength scores
    await calculateArgumentStrength(debate);
    await debate.save();

    res.status(201).json({
      success: true,
      data: subArgument,
      message: 'Sub-argument added successfully'
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/debates/:debateId/arguments/:argumentId/evidence
export const addEvidence = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    console.log('=== ADD EVIDENCE DEBUG ===');
    console.log('Request body:', JSON.stringify(req.body, null, 2));
    console.log('Request params:', req.params);
    console.log('User:', req.user?.username);

    const { type, content, source } = req.body;
    const user = req.user!;
    const { debateId, argumentId } = req.params;

    // Find the debate
    console.log('Looking for debate with ID:', debateId);
    const debate = await Debate.findById(debateId);
    if (!debate) {
      console.log('Debate not found');
      res.status(404).json({
        success: false,
        message: 'Debate not found'
      });
      return;
    }
    console.log('Debate found:', debate.title);

    // Find the argument recursively
    const findArgument = (args: any[], id: string): any => {
      for (const arg of args) {
        if (arg.id === id) return arg;
        const found = findArgument(arg.subArguments || [], id);
        if (found) return found;
      }
      return null;
    };

    console.log('Looking for argument with ID:', argumentId);
    const argument = findArgument(debate.arguments, argumentId);
    console.log('Argument found:', argument ? 'YES' : 'NO');
    if (!argument) {
      res.status(404).json({
        success: false,
        message: 'Argument not found'
      });
      return;
    }

    // Check if user is a participant in the debate
    console.log('Checking participation...');
    console.log('User ID:', (user._id as any).toString());
    console.log('Debate participants:', debate.participants.map(p => ({ id: p.id })));
    const isParticipant = debate.participants.some(p => p.id === (user._id as any).toString());
    console.log('Is participant:', isParticipant);
    if (!isParticipant) {
      console.log('User is not a participant - returning 403');
      res.status(403).json({
        success: false,
        message: 'Only debate participants can add evidence'
      });
      return;
    }

    // Create the evidence
    console.log('Creating evidence object...');
    const evidence = {
      id: uuidv4(),
      type,
      content,
      source: {
        id: uuidv4(),
        ...source
      },
      verificationStatus: 'unverified',
      addedBy: {
        id: (user._id as any).toString(),
        name: getDisplayName(user),
        avatarUrl: user.avatarUrl || `https://picsum.photos/seed/${user.username}/40`
      },
      addedAt: new Date(),
      // Initialize community verification voting
      accurateVotes: 0,
      inaccurateVotes: 0,
      totalVotes: 0,
      verificationScore: 0,
      votedBy: []
    };
    console.log('Evidence object created:', JSON.stringify(evidence, null, 2));

    // Add evidence to argument
    console.log('Adding evidence to argument...');
    if (!argument.evidence) {
      argument.evidence = [];
    }
    argument.evidence.push(evidence);
    console.log('Evidence added to argument. Total evidence count:', argument.evidence.length);

    // Recalculate strength scores
    console.log('Recalculating strength scores...');
    await calculateArgumentStrength(debate);
    console.log('Saving debate...');
    await debate.save();
    console.log('Debate saved successfully!');

    res.status(201).json({
      success: true,
      data: evidence,
      message: 'Evidence added successfully'
    });
    console.log('Response sent successfully!');
  } catch (error) {
    next(error);
  }
};

// POST /api/debates/:debateId/arguments/:argumentId/evidence/:evidenceId/vote
export const voteOnEvidence = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { vote } = req.body; // 'accurate' or 'inaccurate'
    const user = req.user!;
    const { debateId, argumentId, evidenceId } = req.params;

    console.log('=== VOTE ON EVIDENCE DEBUG ===');
    console.log('Vote type:', vote);
    console.log('User:', user.username);
    console.log('Debate ID:', debateId);
    console.log('Argument ID:', argumentId);
    console.log('Evidence ID:', evidenceId);

    // Find the debate
    const debate = await Debate.findById(debateId);
    if (!debate) {
      res.status(404).json({
        success: false,
        message: 'Debate not found'
      });
      return;
    }

    // Find the argument recursively (including sub-arguments)
    const findArgumentRecursively = (args: any[], targetId: string): any => {
      for (const arg of args) {
        if (arg.id === targetId) return arg;
        if (arg.subArguments && arg.subArguments.length > 0) {
          const found = findArgumentRecursively(arg.subArguments, targetId);
          if (found) return found;
        }
      }
      return null;
    };

    const argument = findArgumentRecursively(debate.arguments, argumentId);
    if (!argument) {
      res.status(404).json({
        success: false,
        message: 'Argument not found'
      });
      return;
    }

    // Find the evidence
    const evidence = argument.evidence?.find((ev: any) => ev.id === evidenceId);
    if (!evidence) {
      res.status(404).json({
        success: false,
        message: 'Evidence not found'
      });
      return;
    }

    // Prevent users from voting on their own evidence
    if (evidence.addedBy.id === (user._id as any).toString()) {
      res.status(400).json({
        success: false,
        message: 'You cannot vote on your own evidence'
      });
      return;
    }

    // Initialize votedBy array if it doesn't exist
    if (!evidence.votedBy) {
      evidence.votedBy = [];
    }

    // Check if user has already voted on this evidence
    const userId = (user._id as any).toString();
    const existingVoteIndex = evidence.votedBy.findIndex((v: any) => v.userId === userId);
    const isVoteChange = existingVoteIndex !== -1;

    if (isVoteChange) {
      // User is changing their vote
      const oldVote = evidence.votedBy[existingVoteIndex].vote;

      // Remove the old vote count
      if (oldVote === 'accurate') {
        evidence.accurateVotes = Math.max(0, (evidence.accurateVotes || 0) - 1);
      } else if (oldVote === 'inaccurate') {
        evidence.inaccurateVotes = Math.max(0, (evidence.inaccurateVotes || 0) - 1);
      }

      // Update the user's vote
      evidence.votedBy[existingVoteIndex].vote = vote;
    } else {
      // New vote from this user
      evidence.votedBy.push({
        userId,
        vote
      });
    }

    // Update evidence votes with new vote
    if (vote === 'accurate') {
      evidence.accurateVotes = (evidence.accurateVotes || 0) + 1;
    } else if (vote === 'inaccurate') {
      evidence.inaccurateVotes = (evidence.inaccurateVotes || 0) + 1;
    } else {
      res.status(400).json({
        success: false,
        message: 'Invalid vote type. Must be "accurate" or "inaccurate"'
      });
      return;
    }

    // Update total votes and calculate verification score
    evidence.totalVotes = (evidence.accurateVotes || 0) + (evidence.inaccurateVotes || 0);

    // Calculate verification score (percentage of accurate votes)
    evidence.verificationScore = evidence.totalVotes > 0
      ? (evidence.accurateVotes / evidence.totalVotes) * 100
      : 0;

    // Auto-update verification status based on community votes
    await updateEvidenceVerificationStatus(evidence);

    console.log('Evidence after vote:', {
      id: evidence.id,
      accurateVotes: evidence.accurateVotes,
      inaccurateVotes: evidence.inaccurateVotes,
      totalVotes: evidence.totalVotes,
      verificationScore: evidence.verificationScore,
      verificationStatus: evidence.verificationStatus
    });

    // Recalculate argument strength scores after evidence vote change
    await calculateArgumentStrength(debate);

    await debate.save();

    res.json({
      success: true,
      data: {
        evidenceId,
        accurateVotes: evidence.accurateVotes,
        inaccurateVotes: evidence.inaccurateVotes,
        totalVotes: evidence.totalVotes,
        verificationScore: evidence.verificationScore,
        verificationStatus: evidence.verificationStatus
      },
      message: isVoteChange ? 'Vote on evidence updated successfully' : 'Vote on evidence recorded successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Helper function to update evidence verification status based on community votes
const updateEvidenceVerificationStatus = async (evidence: any): Promise<void> => {
  const { totalVotes, verificationScore } = evidence;

  // Require minimum votes before auto-verification
  const MIN_VOTES_FOR_VERIFICATION = 3;
  const MIN_VOTES_FOR_DISPUTE = 2;

  if (totalVotes >= MIN_VOTES_FOR_VERIFICATION) {
    if (verificationScore >= 80) {
      // 80%+ accurate votes = verified
      evidence.verificationStatus = 'verified';
    } else if (verificationScore <= 30) {
      // 30% or less accurate votes = disputed
      evidence.verificationStatus = 'disputed';
    } else {
      // Between 30-80% = pending (needs more votes)
      evidence.verificationStatus = 'pending';
    }
  } else if (totalVotes >= MIN_VOTES_FOR_DISPUTE && verificationScore <= 20) {
    // Quick dispute for obviously bad evidence
    evidence.verificationStatus = 'disputed';
  } else {
    // Not enough votes yet, keep as unverified or pending
    evidence.verificationStatus = totalVotes > 0 ? 'pending' : 'unverified';
  }
};

// POST /api/debates/:debateId/arguments/:argumentId/comments
export const addComment = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { text, parentCommentId } = req.body;
    const user = req.user!;
    const { debateId, argumentId } = req.params;

    // Clean up old messages if needed to make room for new ones
    const { MessageCleanupService } = await import('../services/messageCleanupService');
    await MessageCleanupService.cleanupIfNeeded(debateId, argumentId);

    // Find the debate
    const debate = await Debate.findById(debateId);
    if (!debate) {
      res.status(404).json({
        success: false,
        message: 'Debate not found'
      });
      return;
    }

    // Find the argument recursively
    const findArgument = (args: any[], id: string): any => {
      for (const arg of args) {
        if (arg.id === id) return arg;
        const found = findArgument(arg.subArguments || [], id);
        if (found) return found;
      }
      return null;
    };

    const argument = findArgument(debate.arguments, argumentId);
    if (!argument) {
      res.status(404).json({
        success: false,
        message: 'Argument not found'
      });
      return;
    }

    // Create the comment
    const comment = {
      id: uuidv4(),
      author: {
        id: (user._id as any).toString(),
        name: getDisplayName(user),
        avatarUrl: user.avatarUrl || `https://picsum.photos/seed/${user.username}/40`
      },
      text,
      parentCommentId,
      replies: [],
      upvotes: 0,
      downvotes: 0,
      votes: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Initialize comments array if it doesn't exist
    if (!argument.comments) {
      argument.comments = [];
    }

    // If this is a reply, add it to the parent comment's replies
    if (parentCommentId) {
      const findComment = (comments: any[], id: string): any => {
        for (const comment of comments) {
          if (comment.id === id) return comment;
          const found = findComment(comment.replies || [], id);
          if (found) return found;
        }
        return null;
      };

      const parentComment = findComment(argument.comments, parentCommentId);
      if (!parentComment) {
        res.status(404).json({
          success: false,
          message: 'Parent comment not found'
        });
        return;
      }

      if (!parentComment.replies) {
        parentComment.replies = [];
      }
      parentComment.replies.push(comment);
    } else {
      // Add as top-level comment
      argument.comments.push(comment);
    }

    await debate.save();

    // Emit real-time event to argument chat room
    io.to(`argument-${argumentId}`).emit('comment-added', {
      argumentId,
      comment,
      parentCommentId
    });

    res.status(201).json({
      success: true,
      data: comment,
      message: 'Comment added successfully'
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/debates/:debateId/arguments/:argumentId/comments/:commentId/vote
export const voteOnComment = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { vote } = req.body; // 'up' or 'down'
    const user = req.user!;
    const { debateId, argumentId, commentId } = req.params;

    // Find the debate
    const debate = await Debate.findById(debateId);
    if (!debate) {
      res.status(404).json({
        success: false,
        message: 'Debate not found'
      });
      return;
    }

    // Find the argument recursively
    const findArgument = (args: any[], id: string): any => {
      for (const arg of args) {
        if (arg.id === id) return arg;
        const found = findArgument(arg.subArguments || [], id);
        if (found) return found;
      }
      return null;
    };

    const argument = findArgument(debate.arguments, argumentId);
    if (!argument) {
      res.status(404).json({
        success: false,
        message: 'Argument not found'
      });
      return;
    }

    // Find the comment recursively
    const findComment = (comments: any[], id: string): any => {
      for (const comment of comments) {
        if (comment.id === id) return comment;
        const found = findComment(comment.replies || [], id);
        if (found) return found;
      }
      return null;
    };

    const comment = findComment(argument.comments || [], commentId);
    if (!comment) {
      res.status(404).json({
        success: false,
        message: 'Comment not found'
      });
      return;
    }

    // Initialize userVotes array if it doesn't exist
    if (!comment.userVotes) {
      comment.userVotes = [];
    }

    // Check if user has already voted
    const existingVoteIndex = comment.userVotes.findIndex((v: any) => v.userId === user.id);

    if (existingVoteIndex !== -1) {
      const existingVote = comment.userVotes[existingVoteIndex];

      // If same vote, remove it (toggle off)
      if (existingVote.vote === vote) {
        comment.userVotes.splice(existingVoteIndex, 1);
        // Decrease the vote count
        if (vote === 'up') {
          comment.upvotes = Math.max(0, (comment.upvotes || 0) - 1);
        } else {
          comment.downvotes = Math.max(0, (comment.downvotes || 0) - 1);
        }
      } else {
        // Change vote (up to down or down to up)
        existingVote.vote = vote;
        // Adjust both counts
        if (vote === 'up') {
          comment.upvotes = (comment.upvotes || 0) + 1;
          comment.downvotes = Math.max(0, (comment.downvotes || 0) - 1);
        } else {
          comment.downvotes = (comment.downvotes || 0) + 1;
          comment.upvotes = Math.max(0, (comment.upvotes || 0) - 1);
        }
      }
    } else {
      // New vote
      comment.userVotes.push({ userId: user.id, vote });
      if (vote === 'up') {
        comment.upvotes = (comment.upvotes || 0) + 1;
      } else {
        comment.downvotes = (comment.downvotes || 0) + 1;
      }
    }

    comment.votes = (comment.upvotes || 0) - (comment.downvotes || 0);

    await debate.save();

    // Emit real-time event to argument chat room
    io.to(`argument-${argumentId}`).emit('comment-vote-updated', {
      argumentId,
      commentId,
      upvotes: comment.upvotes,
      downvotes: comment.downvotes,
      votes: comment.votes,
      voteType: vote,
      voterId: user.id
    });

    res.json({
      success: true,
      data: {
        upvotes: comment.upvotes,
        downvotes: comment.downvotes,
        votes: comment.votes
      },
      message: 'Vote recorded successfully'
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/debates/:debateId/arguments/:argumentId/comments/:commentId/report
export const reportComment = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const user = req.user!;
    const { debateId, argumentId, commentId } = req.params;

    // Find the debate
    const debate = await Debate.findById(debateId);
    if (!debate) {
      res.status(404).json({
        success: false,
        message: 'Debate not found'
      });
      return;
    }

    // Find the argument recursively
    const findArgument = (args: any[], id: string): any => {
      for (const arg of args) {
        if (arg.id === id) return arg;
        const found = findArgument(arg.subArguments || [], id);
        if (found) return found;
      }
      return null;
    };

    const argument = findArgument(debate.arguments, argumentId);
    if (!argument) {
      res.status(404).json({
        success: false,
        message: 'Argument not found'
      });
      return;
    }

    // Find the comment recursively and capture the full comment tree for context
    const findCommentWithContext = (comments: any[], id: string, path: any[] = []): { comment: any; context: any[] } | null => {
      for (const comment of comments) {
        const currentPath = [...path, comment];
        if (comment.id === id) {
          return { comment, context: currentPath };
        }
        if (comment.replies && comment.replies.length > 0) {
          const found = findCommentWithContext(comment.replies, id, currentPath);
          if (found) return found;
        }
      }
      return null;
    };

    const result = findCommentWithContext(argument.comments || [], commentId);
    if (!result) {
      res.status(404).json({
        success: false,
        message: 'Comment not found'
      });
      return;
    }

    const { comment, context } = result;

    // Initialize reportedBy array if it doesn't exist
    if (!comment.reportedBy) {
      comment.reportedBy = [];
    }

    // Check if user has already reported this comment
    if (comment.reportedBy.includes(user.id)) {
      res.status(400).json({
        success: false,
        message: 'You have already reported this comment'
      });
      return;
    }

    // Add user to reportedBy array
    comment.reportedBy.push(user.id);

    // Create report record in moderation queue
    const { ModerationItem } = await import('../models/ModerationQueue');

    const moderationItem = new ModerationItem({
      reportedBy: user.id,
      reportedAt: new Date(),
      debateId: debate.id,
      debateTitle: debate.title,
      argumentId: argument.id,
      argumentText: argument.text,
      commentId: comment.id,
      commentText: comment.text,
      commentAuthor: comment.author,
      commentCreatedAt: comment.createdAt,
      // Save the full comment tree context for moderation
      contextTree: context.map(c => ({
        id: c.id,
        text: c.text,
        author: c.author,
        createdAt: c.createdAt,
        upvotes: c.upvotes || 0,
        downvotes: c.downvotes || 0
      })),
      status: 'pending'
    });

    await moderationItem.save();
    await debate.save();

    res.json({
      success: true,
      message: 'Comment reported successfully'
    });
  } catch (error) {
    next(error);
  }
};


