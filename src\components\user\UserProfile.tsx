import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { User, Debate } from '../../types';
import { getDisplayName } from '../../utils/userDisplay';
import { usersAPI } from '../../utils/api';

interface UserProfileProps {
  userId?: string; // If provided, shows another user's profile; otherwise shows current user
}

export const UserProfile: React.FC<UserProfileProps> = ({ userId }) => {
  const { user: currentUser, updateUser } = useAuth();
  const [profileUser, setProfileUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [userDebates, setUserDebates] = useState<Debate[]>([]);
  const [debatesLoading, setDebatesLoading] = useState(false);
  const [editForm, setEditForm] = useState({
    firstName: '',
    lastName: '',
    bio: '',
    avatarUrl: '',
    privacySettings: {
      displayName: 'username' as 'username' | 'firstName' | 'fullName'
    }
  });

  const isOwnProfile = !userId || userId === currentUser?.id;
  const displayUser = isOwnProfile ? currentUser : profileUser;

  useEffect(() => {
    const loadProfile = async () => {
      if (isOwnProfile) {
        setProfileUser(currentUser);
        setLoading(false);
        return;
      }

      if (userId) {
        try {
          setLoading(true);
          const response = await usersAPI.getUserById(userId);
          if (response.success) {
            setProfileUser(response.data);
          } else {
            setError(response.message || 'Failed to load user profile');
          }
        } catch (err) {
          setError('Failed to load user profile');
          console.error('Error loading user profile:', err);
        } finally {
          setLoading(false);
        }
      }
    };

    loadProfile();
  }, [userId, currentUser, isOwnProfile]);

  // Load user debates
  useEffect(() => {
    const loadUserDebates = async () => {
      const targetUserId = isOwnProfile ? currentUser?.id : userId;
      if (!targetUserId) return;

      try {
        setDebatesLoading(true);
        const response = await usersAPI.getUserDebates(targetUserId);
        if (response.success) {
          setUserDebates(response.data || []);
        }
      } catch (err) {
        console.error('Error loading user debates:', err);
      } finally {
        setDebatesLoading(false);
      }
    };

    if (displayUser) {
      loadUserDebates();
    }
  }, [displayUser, isOwnProfile, currentUser?.id, userId]);

  useEffect(() => {
    if (displayUser && isOwnProfile) {
      setEditForm({
        firstName: displayUser.firstName || '',
        lastName: displayUser.lastName || '',
        bio: displayUser.bio || '',
        avatarUrl: displayUser.avatarUrl || '',
        privacySettings: displayUser.privacySettings || { displayName: 'username' }
      });
    }
  }, [displayUser, isOwnProfile]);

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isOwnProfile) return;

    try {
      await updateUser(editForm);
      setIsEditing(false);
    } catch (err) {
      setError('Failed to update profile');
      console.error('Error updating profile:', err);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-primary mx-auto mb-4"></div>
          <p className="text-brand-text-secondary">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (error || !displayUser) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">⚠️</div>
          <p className="text-red-400 mb-4">{error || 'User not found'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full p-4 md:p-8">
      <div className="bg-brand-surface rounded-lg p-6 mb-8">
        <div className="flex items-start space-x-6">
          <img
            src={displayUser.avatarUrl || `https://picsum.photos/seed/${displayUser.username}/100`}
            alt={getDisplayName(displayUser)}
            className="w-24 h-24 rounded-full object-cover"
          />
          
          <div className="flex-1">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h1 className="text-3xl font-bold text-white">{getDisplayName(displayUser)}</h1>
                <p className="text-brand-text-secondary">@{displayUser.username}</p>
              </div>
              
              {isOwnProfile && (
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg transition-colors"
                >
                  {isEditing ? 'Cancel' : 'Edit Profile'}
                </button>
              )}
            </div>

            {isEditing ? (
              <form onSubmit={handleEditSubmit} className="space-y-4">

                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-brand-text-secondary mb-1">
                      First Name
                    </label>
                    <input
                      type="text"
                      value={editForm.firstName}
                      onChange={(e) => setEditForm({ ...editForm, firstName: e.target.value })}
                      className="w-full px-3 py-2 bg-brand-surface-light border border-brand-surface-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-brand-primary"
                      placeholder="Your first name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-brand-text-secondary mb-1">
                      Last Name
                    </label>
                    <input
                      type="text"
                      value={editForm.lastName}
                      onChange={(e) => setEditForm({ ...editForm, lastName: e.target.value })}
                      className="w-full px-3 py-2 bg-brand-surface-light border border-brand-surface-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-brand-primary"
                      placeholder="Your last name"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-brand-text-secondary mb-1">
                    Bio
                  </label>
                  <textarea
                    value={editForm.bio}
                    onChange={(e) => setEditForm({ ...editForm, bio: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 bg-brand-surface-light border border-brand-surface-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-brand-primary resize-none"
                    placeholder="Tell us about yourself..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-brand-text-secondary mb-2">
                    Display Name Preference
                  </label>
                  <div className="space-y-2">
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="displayName"
                        value="username"
                        checked={editForm.privacySettings.displayName === 'username'}
                        onChange={(e) => setEditForm({
                          ...editForm,
                          privacySettings: { ...editForm.privacySettings, displayName: e.target.value as 'username' | 'firstName' | 'fullName' }
                        })}
                        className="text-brand-primary focus:ring-brand-primary"
                      />
                      <span className="text-brand-text">Username only (@{displayUser?.username})</span>
                    </label>

                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="displayName"
                        value="firstName"
                        checked={editForm.privacySettings.displayName === 'firstName'}
                        onChange={(e) => setEditForm({
                          ...editForm,
                          privacySettings: { ...editForm.privacySettings, displayName: e.target.value as 'username' | 'firstName' | 'fullName' }
                        })}
                        className="text-brand-primary focus:ring-brand-primary"
                      />
                      <span className="text-brand-text">First name only ({editForm.firstName || 'Not set'})</span>
                    </label>

                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="displayName"
                        value="fullName"
                        checked={editForm.privacySettings.displayName === 'fullName'}
                        onChange={(e) => setEditForm({
                          ...editForm,
                          privacySettings: { ...editForm.privacySettings, displayName: e.target.value as 'username' | 'firstName' | 'fullName' }
                        })}
                        className="text-brand-primary focus:ring-brand-primary"
                      />
                      <span className="text-brand-text">Full name ({editForm.firstName} {editForm.lastName} {!editForm.firstName && !editForm.lastName ? '- Not set' : ''})</span>
                    </label>
                  </div>
                  <p className="text-xs text-brand-text-secondary mt-1">
                    This controls how your name appears to other users in debates and discussions.
                  </p>
                </div>

                <div className="flex space-x-3">
                  <button
                    type="submit"
                    className="bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    Save Changes
                  </button>
                  <button
                    type="button"
                    onClick={() => setIsEditing(false)}
                    className="bg-brand-surface-light hover:bg-brand-surface text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            ) : (
              <div>
                <p className="text-brand-text-secondary mb-4">
                  {displayUser.bio || 'No bio provided.'}
                </p>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-brand-primary">{displayUser.reputation || 0}</div>
                    <div className="text-sm text-brand-text-secondary">Reputation</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-brand-primary">{displayUser.totalVotes || 0}</div>
                    <div className="text-sm text-brand-text-secondary">Total Votes</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-brand-primary">{displayUser.debatesCreated?.length || 0}</div>
                    <div className="text-sm text-brand-text-secondary">Debates Created</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-brand-primary">{displayUser.debatesParticipated?.length || 0}</div>
                    <div className="text-sm text-brand-text-secondary">Debates Joined</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* User's Debates Section */}
      <div className="bg-brand-surface rounded-lg p-6">
        <h2 className="text-2xl font-bold text-white mb-6">Recent Activity</h2>

        {debatesLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary mx-auto mb-4"></div>
            <p className="text-brand-text-secondary">Loading activity...</p>
          </div>
        ) : userDebates.length > 0 ? (
          <div className="space-y-4">
            {userDebates.map((debate) => (
              <div key={debate.id} className="bg-brand-surface-light rounded-lg p-4 border border-brand-border">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-white mb-2">{debate.title}</h3>
                    <p className="text-brand-text-secondary text-sm mb-3 line-clamp-2">
                      {debate.description}
                    </p>

                    <div className="flex items-center gap-4 text-sm text-brand-text-secondary">
                      <span className="flex items-center gap-1">
                        <span className="w-2 h-2 bg-success rounded-full"></span>
                        FOR: {debate.votesFor}
                      </span>
                      <span className="flex items-center gap-1">
                        <span className="w-2 h-2 bg-danger rounded-full"></span>
                        AGAINST: {debate.votesAgainst}
                      </span>
                      <span>{debate.arguments?.length || 0} arguments</span>
                      <span>•</span>
                      <span>{new Date(debate.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>

                  <div className="ml-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      debate.status === 'active'
                        ? 'bg-success/20 text-success'
                        : debate.status === 'completed'
                        ? 'bg-brand-text-secondary/20 text-brand-text-secondary'
                        : 'bg-warning/20 text-warning'
                    }`}>
                      {debate.status.charAt(0).toUpperCase() + debate.status.slice(1)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-brand-text-secondary">No recent activity to display.</p>
            <p className="text-brand-text-secondary text-sm mt-2">
              {isOwnProfile
                ? "Start participating in debates to see your activity here!"
                : "This user hasn't participated in any debates yet."
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
