
import React from 'react';
import { Debate } from '../../types';
import { ParticipantIcon } from '../ui/ParticipantIcon';

interface DebateCardProps {
  debate: Debate;
  onSelect: (id: string) => void;
}

export const DebateCard: React.FC<DebateCardProps> = ({ debate, onSelect }) => {
  return (
    <div className="bg-brand-surface rounded-lg shadow-lg p-6 transition-all duration-300 hover:shadow-brand-primary/20 hover:ring-1 hover:ring-brand-primary/50">
      <div className="flex justify-between items-start">
        <h2 className="text-xl font-bold text-brand-text mb-2 max-w-md">{debate.title}</h2>
        {debate.isLive && (
          <span className="flex items-center gap-2 text-sm font-semibold bg-danger/20 text-danger px-3 py-1 rounded-full">
            <span className="relative flex h-2 w-2">
              <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-danger opacity-75"></span>
              <span className="relative inline-flex rounded-full h-2 w-2 bg-danger"></span>
            </span>
            LIVE
          </span>
        )}
      </div>
      <p className="text-brand-text-light mb-4 text-sm">{debate.description}</p>
      
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
            {debate.participants.slice(0, 4).map((p, index) => (
                <div key={p.id} className="-ml-3" style={{ zIndex: 4-index }}>
                    <ParticipantIcon participant={p} />
                </div>
            ))}
            {debate.participants.length > 4 && (
                <div className="-ml-3 flex items-center justify-center w-10 h-10 rounded-full bg-brand-surface-light text-brand-text-light text-xs font-bold ring-2 ring-brand-surface">
                    +{debate.participants.length - 4}
                </div>
            )}
        </div>
        <div className="flex gap-4 font-mono text-sm">
          <span className="text-success">SUPPORT: {debate.supportVotes || 0}</span>
          <span className="text-danger">OPPOSE: {debate.opposeVotes || 0}</span>
        </div>
      </div>

      <button
        onClick={() => onSelect(debate.id)}
        className="w-full bg-brand-primary text-white font-bold py-2 px-4 rounded-lg hover:bg-brand-primary-hover transition-colors"
      >
        View Debate
      </button>
    </div>
  );
};
