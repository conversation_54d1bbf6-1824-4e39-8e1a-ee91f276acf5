"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchedulerService = void 0;
const cron = __importStar(require("node-cron"));
const messageCleanupService_1 = require("./messageCleanupService");
class SchedulerService {
    static start() {
        console.log('Starting scheduler service...');
        this.cleanupJob = cron.schedule('0 * * * *', async () => {
            console.log('Running scheduled message cleanup...');
            try {
                await messageCleanupService_1.MessageCleanupService.cleanupOldMessages();
            }
            catch (error) {
                console.error('Scheduled cleanup failed:', error);
            }
        });
        console.log('Scheduler service started. Message cleanup will run every hour.');
        setTimeout(async () => {
            console.log('Running initial message cleanup...');
            try {
                await messageCleanupService_1.MessageCleanupService.cleanupOldMessages();
            }
            catch (error) {
                console.error('Initial cleanup failed:', error);
            }
        }, 5000);
    }
    static stop() {
        if (this.cleanupJob) {
            this.cleanupJob.stop();
            this.cleanupJob = null;
            console.log('Scheduler service stopped.');
        }
    }
    static getStatus() {
        return {
            isRunning: this.cleanupJob !== null,
            nextRun: this.cleanupJob ? 'Every hour at minute 0' : undefined
        };
    }
    static async triggerCleanup() {
        console.log('Manually triggering message cleanup...');
        await messageCleanupService_1.MessageCleanupService.cleanupOldMessages();
    }
}
exports.SchedulerService = SchedulerService;
SchedulerService.cleanupJob = null;
//# sourceMappingURL=scheduler.js.map