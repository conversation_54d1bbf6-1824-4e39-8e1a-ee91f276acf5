"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageCleanupService = void 0;
const Debate_1 = __importDefault(require("../models/Debate"));
const ModerationQueue_1 = require("../models/ModerationQueue");
class MessageCleanupService {
    static async cleanupIfNeeded(debateId, argumentId) {
        try {
            const debate = await Debate_1.default.findById(debateId);
            if (!debate)
                return;
            const argument = this.findArgument(debate.arguments, argumentId);
            if (!argument || !argument.comments)
                return;
            const totalComments = this.countTotalComments(argument.comments);
            if (totalComments >= this.MAX_COMMENTS_PER_ARGUMENT) {
                console.log(`Cleaning up old messages in argument ${argumentId} (${totalComments} comments)`);
                await this.cleanupArgumentComments(debate, argument);
            }
        }
        catch (error) {
            console.error('Error during conditional cleanup:', error);
        }
    }
    static async cleanupOldMessages() {
        try {
            console.log('Starting message cleanup process...');
            const cutoffDate = new Date();
            cutoffDate.setHours(cutoffDate.getHours() - this.MESSAGE_RETENTION_HOURS);
            const debates = await Debate_1.default.find({
                'arguments.comments': { $exists: true, $ne: [] }
            });
            let totalProcessed = 0;
            let totalReported = 0;
            let totalDeleted = 0;
            for (const debate of debates) {
                const result = await this.processDebateComments(debate, cutoffDate);
                totalProcessed += result.processed;
                totalReported += result.reported;
                totalDeleted += result.deleted;
            }
            console.log(`Message cleanup completed:`, {
                totalProcessed,
                totalReported,
                totalDeleted,
                cutoffDate: cutoffDate.toISOString()
            });
        }
        catch (error) {
            console.error('Error during message cleanup:', error);
            throw error;
        }
    }
    static async processDebateComments(debate, cutoffDate) {
        let processed = 0;
        let reported = 0;
        let deleted = 0;
        let debateModified = false;
        for (const argument of debate.arguments) {
            if (!argument.comments || argument.comments.length === 0)
                continue;
            const result = await this.processCommentTree(argument.comments, cutoffDate, debate, argument);
            processed += result.processed;
            reported += result.reported;
            deleted += result.deleted;
            if (result.modified) {
                debateModified = true;
            }
        }
        if (debateModified) {
            await debate.save();
        }
        return { processed, reported, deleted };
    }
    static async processCommentTree(comments, cutoffDate, debate, argument, parentContext = []) {
        let processed = 0;
        let reported = 0;
        let deleted = 0;
        let modified = false;
        for (let i = comments.length - 1; i >= 0; i--) {
            const comment = comments[i];
            processed++;
            const commentDate = new Date(comment.createdAt);
            const isOld = commentDate < cutoffDate;
            const isReported = comment.reportedBy && comment.reportedBy.length > 0;
            const contextTree = [...parentContext, comment];
            if (isOld) {
                if (isReported) {
                    await this.saveToModerationQueue(comment, debate, argument, contextTree);
                    reported++;
                }
                if (comment.replies && comment.replies.length > 0) {
                    const replyResult = await this.processCommentTree(comment.replies, cutoffDate, debate, argument, contextTree);
                    processed += replyResult.processed;
                    reported += replyResult.reported;
                    deleted += replyResult.deleted;
                }
                comments.splice(i, 1);
                deleted++;
                modified = true;
            }
            else {
                if (comment.replies && comment.replies.length > 0) {
                    const replyResult = await this.processCommentTree(comment.replies, cutoffDate, debate, argument, contextTree);
                    processed += replyResult.processed;
                    reported += replyResult.reported;
                    deleted += replyResult.deleted;
                    if (replyResult.modified) {
                        modified = true;
                    }
                }
            }
        }
        return { processed, reported, deleted, modified };
    }
    static async saveToModerationQueue(comment, debate, argument, contextTree) {
        try {
            for (const reporterId of comment.reportedBy) {
                const moderationItem = new ModerationQueue_1.ModerationItem({
                    reportedBy: reporterId,
                    reportedAt: new Date(),
                    debateId: debate.id,
                    debateTitle: debate.title,
                    argumentId: argument.id,
                    argumentText: argument.text,
                    commentId: comment.id,
                    commentText: comment.text,
                    commentAuthor: comment.author,
                    commentCreatedAt: comment.createdAt,
                    contextTree: contextTree.map(c => ({
                        id: c.id,
                        text: c.text,
                        author: c.author,
                        createdAt: c.createdAt,
                        upvotes: c.upvotes || 0,
                        downvotes: c.downvotes || 0
                    })),
                    status: 'pending'
                });
                await moderationItem.save();
            }
        }
        catch (error) {
            console.error('Error saving to moderation queue:', error);
        }
    }
    static async getCleanupStats() {
        const cutoffDate = new Date();
        cutoffDate.setHours(cutoffDate.getHours() - this.MESSAGE_RETENTION_HOURS);
        const debates = await Debate_1.default.find({
            'arguments.comments': { $exists: true, $ne: [] }
        });
        let totalComments = 0;
        let oldComments = 0;
        let reportedComments = 0;
        for (const debate of debates) {
            for (const argument of debate.arguments) {
                if (!argument.comments)
                    continue;
                const stats = this.countCommentsRecursive(argument.comments, cutoffDate);
                totalComments += stats.total;
                oldComments += stats.old;
                reportedComments += stats.reported;
            }
        }
        return { totalComments, oldComments, reportedComments };
    }
    static countCommentsRecursive(comments, cutoffDate) {
        let total = 0;
        let old = 0;
        let reported = 0;
        for (const comment of comments) {
            total++;
            const commentDate = new Date(comment.createdAt);
            if (commentDate < cutoffDate) {
                old++;
            }
            if (comment.reportedBy && comment.reportedBy.length > 0) {
                reported++;
            }
            if (comment.replies && comment.replies.length > 0) {
                const replyStats = this.countCommentsRecursive(comment.replies, cutoffDate);
                total += replyStats.total;
                old += replyStats.old;
                reported += replyStats.reported;
            }
        }
        return { total, old, reported };
    }
    static findArgument(args, argumentId) {
        for (const arg of args) {
            if (arg.id === argumentId)
                return arg;
            if (arg.subArguments) {
                const found = this.findArgument(arg.subArguments, argumentId);
                if (found)
                    return found;
            }
        }
        return null;
    }
    static countTotalComments(comments) {
        let count = 0;
        for (const comment of comments) {
            count++;
            if (comment.replies && comment.replies.length > 0) {
                count += this.countTotalComments(comment.replies);
            }
        }
        return count;
    }
    static async cleanupArgumentComments(debate, argument) {
        const cutoffDate = new Date();
        cutoffDate.setHours(cutoffDate.getHours() - this.MESSAGE_RETENTION_HOURS);
        const result = await this.processCommentTree(argument.comments, cutoffDate, debate, argument);
        if (result.modified) {
            await debate.save();
            console.log(`Cleaned up ${result.deleted} old messages, preserved ${result.reported} reported messages`);
        }
    }
}
exports.MessageCleanupService = MessageCleanupService;
MessageCleanupService.MESSAGE_RETENTION_HOURS = 24;
MessageCleanupService.MAX_COMMENTS_PER_ARGUMENT = 100;
//# sourceMappingURL=messageCleanupService.js.map