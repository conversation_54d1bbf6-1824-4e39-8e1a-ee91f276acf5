
export enum VoteSide {
  FOR = 'FOR',
  AGAINST = 'AGAINST',
}

export enum DebatePosition {
  SUPPORT = 'support',
  OPPOSE = 'oppose',
}

export interface Reference {
  id: string;
  url: string;
  title: string;
  description?: string;
  type: 'academic' | 'news' | 'government' | 'organization' | 'other';
  credibilityScore?: number;
}

export interface Evidence {
  id: string;
  type: 'statistic' | 'study' | 'expert_opinion' | 'case_study' | 'historical_fact' | 'other';
  content: string;
  source: Reference;
  verificationStatus: 'verified' | 'pending' | 'disputed' | 'unverified';
  addedBy: Participant;
  addedAt: string;
  // Community verification voting
  accurateVotes: number;
  inaccurateVotes: number;
  totalVotes: number;
  verificationScore: number;
  votedBy: Array<{ userId: string; vote: 'accurate' | 'inaccurate' }>;
}

export interface Comment {
  id: string;
  author: Participant;
  text: string;
  parentCommentId?: string;
  replies: Comment[];
  upvotes: number;
  downvotes: number;
  votes: number;
  userVotes: Array<{ userId: string; vote: 'up' | 'down' }>;
  reportedBy: string[];
  createdAt: string;
  updatedAt: string;
}

export interface Argument {
  id: string;
  author: Participant;
  text: string;
  references: Reference[];
  evidence: Evidence[];
  side: VoteSide;
  votes: number;
  upvotes: number;
  downvotes: number;
  userVotes: Array<{ userId: string; vote: 'up' | 'down' }>;
  parentArgumentId?: string;
  subArguments: Argument[];
  comments: Comment[];
  depth: number;
  strengthScore: number;
  isChallenge: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Participant {
  id: string;
  name: string;
  avatarUrl: string;
}

export interface TimeBlockBalance {
  debateId: string;
  timeBlocksRemaining: number;
  timeBlocksUsed: number;
  lastUpdated: string;
}

export interface User {
  id: string;
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
  bio?: string;
  role: 'user' | 'moderator' | 'admin';
  privacySettings: {
    displayName: 'username' | 'firstName' | 'fullName';
  };
  debatesParticipated: string[];
  debatesCreated: string[];
  reputation: number;
  totalVotes: number;
  suspended?: boolean;
  suspensionReason?: string;
  suspendedAt?: string;
  suspendedBy?: string;

  // Time block system for voice debates
  timeBlockBalances: TimeBlockBalance[];
  totalTimeBlocksPurchased: number;

  createdAt: string;
}

export interface AuthResponse {
  success: boolean;
  data: {
    user: User;
    token: string;
  };
  message?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

// Admin-related types
export interface AdminStats {
  overview: {
    totalUsers: number;
    totalDebates: number;
    activeDebates: number;
    pendingModerationItems: number;
    totalModerationItems: number;
    usersToday: number;
    debatesToday: number;
  };
  userRoles: {
    user: number;
    moderator: number;
    admin: number;
  };
  recentActivity: {
    users: User[];
    debates: Debate[];
  };
}

export interface ModerationItem {
  id: string;
  reportedBy: string;
  reportedAt: string;
  debateId: string;
  debateTitle: string;
  argumentId: string;
  argumentText: string;
  commentId: string;
  commentText: string;
  commentAuthor: Participant;
  commentCreatedAt: string;
  contextTree: ModerationComment[];
  status: 'pending' | 'reviewed' | 'resolved' | 'dismissed';
  moderatorId?: string;
  moderatorNotes?: string;
  actionTaken?: 'none' | 'warning' | 'comment_removed' | 'user_suspended';
  reviewedAt?: string;
}

export interface ModerationComment {
  id: string;
  text: string;
  author: Participant;
  createdAt: string;
  upvotes: number;
  downvotes: number;
}

export interface UsersPaginationResponse {
  users: User[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface VoiceSpeaker {
  participant: Participant;
  side: VoteSide;
  timeAllocated: number; // in seconds
  timeUsed: number; // in seconds
  timeRemaining: number; // in seconds
  isSpeaking: boolean;
  isMuted: boolean;
  joinedAt: string;
}

export interface VoiceDebateSettings {
  isVoiceEnabled: boolean;
  defaultSpeakingTime: number; // in seconds, default time allocation per speaker
  maxSpeakersPerSide: number; // maximum speakers allowed per side in queue
  allowSpectatorVoice: boolean; // whether non-participants can join voice
  autoRotateSpeakers: boolean; // whether to automatically rotate speakers when time runs out
  mutualExclusionEnabled: boolean; // whether to mute all if multiple speak
}

export interface VoiceDebateState {
  settings: VoiceDebateSettings;
  speakerQueue: {
    FOR: VoiceSpeaker[];
    AGAINST: VoiceSpeaker[];
  };
  currentSpeaker?: VoiceSpeaker;
  isVoiceActive: boolean;
  voiceStartedAt?: string;
}

export interface Debate {
  id: string;
  title: string;
  description: string;
  category: string; // Category for filtering and organization
  claim: {
    author: Participant;
    text: string;
    references: Reference[];
  };
  participants: Participant[];
  proponent: Participant;
  opponent?: Participant; // Optional for open debates
  type: 'one-on-one' | 'open';
  arguments: Argument[];
  supportVotes: number;
  opposeVotes: number;
  userVotes: Array<{ userId: string; position: DebatePosition }>;
  isLive: boolean;
  status: 'draft' | 'active' | 'completed' | 'cancelled';
  startTime?: string;
  endTime?: string;

  // Voice debate features
  voiceDebate?: VoiceDebateState;

  createdAt: string;
  updatedAt: string;
}
