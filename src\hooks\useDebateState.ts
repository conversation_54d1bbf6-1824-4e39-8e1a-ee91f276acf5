
import { useState, useCallback, useEffect } from 'react';
import { Debate, VoteSide, DebatePosition } from '../types';
import { debatesAPI } from '../utils/api';
import { useAuth } from '../contexts/AuthContext';

const MOCK_PARTICIPANTS = {
  userA: { id: 'u1', name: '<PERSON>', avatarUrl: 'https://picsum.photos/seed/u1/100' },
  userB: { id: 'u2', name: '<PERSON>', avatarUrl: 'https://picsum.photos/seed/u2/100' },
  userC: { id: 'u3', name: '<PERSON>', avatarUrl: 'https://picsum.photos/seed/u3/100' },
  userD: { id: 'u4', name: '<PERSON><PERSON>', avatarUrl: 'https://picsum.photos/seed/u4/100' },
};

const MOCK_DEBATES: Debate[] = [
  {
    id: 'd1',
    title: 'Is artificial intelligence a net positive for humanity?',
    description: 'A deep-dive into the societal, economic, and ethical implications of advancing AI technology.',
    claim: {
      author: MOCK_PARTICIPANTS.userA,
      text: 'The continued development and integration of artificial intelligence will ultimately yield more benefits than harms, driving unprecedented progress in science, medicine, and human efficiency.',
      references: [{id: 'r1', title: 'Global AI Development Index 2023', url: '#' }],
    },
    proponent: MOCK_PARTICIPANTS.userA,
    opponent: MOCK_PARTICIPANTS.userB,
    participants: [MOCK_PARTICIPANTS.userA, MOCK_PARTICIPANTS.userB, MOCK_PARTICIPANTS.userC],
    arguments: [
      {
        id: 'a1',
        author: MOCK_PARTICIPANTS.userA,
        text: 'AI is accelerating drug discovery and helping to diagnose diseases earlier and more accurately than ever before.',
        side: VoteSide.FOR,
        votes: 128,
        upvotes: 140,
        downvotes: 12,
        references: [],
        evidence: [
          {
            id: 'e1',
            type: 'study',
            content: 'A recent study by MIT showed that AI-powered drug discovery reduced the time to identify potential compounds by 75%.',
            source: {
              id: 'r1',
              title: 'MIT AI Drug Discovery Study 2023',
              url: 'https://example.com/mit-study',
              type: 'academic'
            },
            verificationStatus: 'verified',
            addedBy: MOCK_PARTICIPANTS.userA,
            addedAt: '2024-01-15T10:30:00Z',
            // Community verification voting
            accurateVotes: 8,
            inaccurateVotes: 1,
            totalVotes: 9,
            verificationScore: 88.9,
            votedBy: [
              { userId: 'user1', vote: 'accurate' },
              { userId: 'user2', vote: 'accurate' },
              { userId: 'user3', vote: 'inaccurate' }
            ]
          }
        ],
        comments: [
          {
            id: 'c1',
            author: MOCK_PARTICIPANTS.userB,
            text: 'This is a compelling point about AI in healthcare. Do you have more recent studies?',
            parentCommentId: undefined,
            replies: [
              {
                id: 'c2',
                author: MOCK_PARTICIPANTS.userA,
                text: 'Yes, I can share some 2024 studies on AI drug discovery.',
                parentCommentId: 'c1',
                replies: [],
                upvotes: 3,
                downvotes: 0,
                votes: 3,
                createdAt: '2024-01-15T09:45:00Z',
                updatedAt: '2024-01-15T09:45:00Z'
              }
            ],
            upvotes: 5,
            downvotes: 1,
            votes: 4,
            createdAt: '2024-01-15T09:30:00Z',
            updatedAt: '2024-01-15T09:30:00Z'
          },
          {
            id: 'c3',
            author: MOCK_PARTICIPANTS.userC,
            text: 'The diagnostic accuracy improvements are particularly impressive.',
            parentCommentId: undefined,
            replies: [],
            upvotes: 8,
            downvotes: 0,
            votes: 8,
            createdAt: '2024-01-15T10:30:00Z',
            updatedAt: '2024-01-15T10:30:00Z'
          }
        ],
        subArguments: [
          {
            id: 'sub1',
            author: MOCK_PARTICIPANTS.userC,
            text: 'AI has already helped discover new antibiotics like halicin, which can kill drug-resistant bacteria.',
            side: VoteSide.FOR,
            votes: 15,
            upvotes: 18,
            downvotes: 3,
            references: [],
            evidence: [],
            parentArgumentId: 'a1',
            comments: [],
            subArguments: [],
            depth: 1,
            strengthScore: 75,
            isChallenge: false,
            createdAt: '2024-01-15T10:00:00Z',
            updatedAt: '2024-01-15T10:00:00Z'
          },
          {
            id: 'sub2',
            author: MOCK_PARTICIPANTS.userB,
            text: 'But AI drug discovery still requires extensive human validation and clinical trials, so the time savings may be overstated.',
            side: VoteSide.AGAINST,
            votes: 8,
            upvotes: 12,
            downvotes: 4,
            references: [],
            evidence: [],
            parentArgumentId: 'a1',
            comments: [],
            subArguments: [],
            depth: 1,
            strengthScore: 60,
            isChallenge: true,
            createdAt: '2024-01-15T10:15:00Z',
            updatedAt: '2024-01-15T10:15:00Z'
          }
        ],
        depth: 0,
        strengthScore: 85,
        isChallenge: false,
        createdAt: '2024-01-15T09:00:00Z',
        updatedAt: '2024-01-15T10:30:00Z'
      },
      {
        id: 'a2',
        author: MOCK_PARTICIPANTS.userB,
        text: 'Unchecked AI development risks massive job displacement and could exacerbate economic inequality on a global scale.',
        side: VoteSide.AGAINST,
        votes: 95,
        upvotes: 105,
        downvotes: 10,
        references: [{id: 'r2', title: 'Economic Forum Report on Future of Jobs', url: '#', type: 'organization' }],
        evidence: [
          {
            id: 'e2',
            type: 'statistic',
            content: 'According to the World Economic Forum, AI could displace 85 million jobs by 2025, while creating only 97 million new ones.',
            source: {
              id: 'r2',
              title: 'Future of Jobs Report 2023',
              url: 'https://example.com/wef-jobs',
              type: 'organization'
            },
            verificationStatus: 'pending',
            addedBy: MOCK_PARTICIPANTS.userB,
            addedAt: '2024-01-15T10:45:00Z',
            // Community verification voting
            accurateVotes: 3,
            inaccurateVotes: 2,
            totalVotes: 5,
            verificationScore: 60.0,
            votedBy: [
              { userId: 'user4', vote: 'accurate' },
              { userId: 'user5', vote: 'inaccurate' }
            ]
          }
        ],
        comments: [],
        subArguments: [],
        depth: 0,
        strengthScore: 72,
        isChallenge: false,
        createdAt: '2024-01-15T09:15:00Z',
        updatedAt: '2024-01-15T09:15:00Z'
      },
      {
        id: 'a3',
        author: MOCK_PARTICIPANTS.userC,
        text: 'The use of AI in creative fields is not replacing artists but providing them with powerful new tools for expression.',
        side: VoteSide.FOR,
        votes: 42,
        upvotes: 48,
        downvotes: 6,
        references: [{id: 'r3', title: 'Art & Machina: A Survey', url: '#', type: 'other' }],
        evidence: [],
        comments: [],
        subArguments: [],
        depth: 0,
        strengthScore: 65,
        isChallenge: false,
        createdAt: '2024-01-15T09:30:00Z',
        updatedAt: '2024-01-15T09:30:00Z'
      },
    ],
    supportVotes: 170,
    opposeVotes: 95,
    userVotes: [],
    isLive: true,
    type: 'one-on-one',
    status: 'active',
    createdAt: '2024-01-15T08:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z'
  },
  {
    id: 'd2',
    title: 'Should space exploration be privatized?',
    description: 'Examining the pros and cons of shifting the primary responsibility for space exploration from government agencies to private corporations.',
    claim: {
      author: MOCK_PARTICIPANTS.userD,
      text: 'The privatization of space exploration accelerates innovation, reduces costs, and opens up the cosmos for humanity in a way that public funding alone cannot.',
      references: [],
    },
    proponent: MOCK_PARTICIPANTS.userD,
    opponent: MOCK_PARTICIPANTS.userC,
    participants: [MOCK_PARTICIPANTS.userD, MOCK_PARTICIPANTS.userC, MOCK_PARTICIPANTS.userA],
    arguments: [
      {
        id: 'b1',
        author: MOCK_PARTICIPANTS.userC,
        text: 'Relying on private companies introduces a profit motive that could compromise scientific objectives and safety standards.',
        side: VoteSide.AGAINST,
        votes: 78,
        upvotes: 85,
        downvotes: 7,
        references: [],
        evidence: [],
        comments: [],
        subArguments: [],
        depth: 0,
        strengthScore: 68,
        isChallenge: false,
        createdAt: '2024-01-15T11:00:00Z',
        updatedAt: '2024-01-15T11:00:00Z'
      },
      {
        id: 'b2',
        author: MOCK_PARTICIPANTS.userA,
        text: 'Private competition has already driven down launch costs significantly, making space more accessible.',
        side: VoteSide.FOR,
        votes: 150,
        upvotes: 160,
        downvotes: 10,
        references: [{id: 'r4', title: 'Analysis of Launch Costs (2010-2024)', url: '#', type: 'other' }],
        evidence: [],
        comments: [],
        subArguments: [],
        depth: 0,
        strengthScore: 82,
        isChallenge: false,
        createdAt: '2024-01-15T11:15:00Z',
        updatedAt: '2024-01-15T11:15:00Z'
      },
    ],
    supportVotes: 150,
    opposeVotes: 78,
    userVotes: [],
    isLive: false,
    type: 'one-on-one',
    status: 'active',
    createdAt: '2024-01-15T11:00:00Z',
    updatedAt: '2024-01-15T11:15:00Z'
  },
];


export const useDebateState = () => {
  const [debates, setDebates] = useState<Debate[]>([]);
  const [selectedDebateId, setSelectedDebateId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated } = useAuth();

  // Load debates from API
  const loadDebates = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await debatesAPI.getDebates();
      if (response.success) {
        setDebates(response.data);
      } else {
        setError(response.message || 'Failed to load debates');
      }
    } catch (err) {
      setError('Failed to load debates');
      console.error('Error loading debates:', err);
      // Fallback to mock data if API fails
      setDebates(MOCK_DEBATES);
    } finally {
      setLoading(false);
    }
  }, []);

  // Load debates on mount
  useEffect(() => {
    loadDebates();
  }, [loadDebates]);

  const selectDebate = useCallback((id: string | null) => {
    setSelectedDebateId(id);
  }, []);

  const castVote = useCallback(async (debateId: string, side: VoteSide, argumentId?: string) => {
    if (!isAuthenticated) {
      setError('You must be logged in to vote');
      return;
    }

    try {
      if (argumentId) {
        // Vote on argument
        const voteType = side === VoteSide.FOR ? 'up' : 'down';
        const response = await debatesAPI.voteOnArgument(debateId, argumentId, voteType);
        if (response.success) {
          // Update local state
          setDebates(prevDebates =>
            prevDebates.map(debate => {
              if (debate.id === debateId) {
                const newDebate = { ...debate };
                newDebate.arguments = newDebate.arguments.map(arg => {
                  if (arg.id === argumentId) {
                    return {
                      ...arg,
                      votes: response.data.votes,
                      upvotes: response.data.upvotes,
                      downvotes: response.data.downvotes
                    };
                  }
                  return arg;
                });
                // Note: Argument voting no longer affects debate vote totals
                return newDebate;
              }
              return debate;
            })
          );
        }
      } else {
        // Vote on debate - convert VoteSide to DebatePosition
        const position = side === VoteSide.FOR ? DebatePosition.SUPPORT : DebatePosition.OPPOSE;
        const response = await debatesAPI.voteOnDebate(debateId, position);
        if (response.success) {
          // Update local state
          setDebates(prevDebates =>
            prevDebates.map(debate => {
              if (debate.id === debateId) {
                return {
                  ...debate,
                  supportVotes: response.data.supportVotes,
                  opposeVotes: response.data.opposeVotes,
                  userVotes: response.data.userVotes || debate.userVotes
                };
              }
              return debate;
            })
          );
        }
      }
    } catch (err) {
      setError('Failed to cast vote');
      console.error('Error casting vote:', err);
    }
  }, [isAuthenticated]);

  const castDebateVote = useCallback(async (debateId: string, position: DebatePosition) => {
    if (!isAuthenticated) {
      setError('You must be logged in to vote');
      return;
    }

    try {
      const response = await debatesAPI.voteOnDebate(debateId, position);
      if (response.success) {
        // Update local state
        setDebates(prevDebates =>
          prevDebates.map(debate => {
            if (debate.id === debateId) {
              return {
                ...debate,
                supportVotes: response.data.supportVotes,
                opposeVotes: response.data.opposeVotes,
                userVotes: response.data.userVotes || debate.userVotes
              };
            }
            return debate;
          })
        );
      }
    } catch (err) {
      setError('Failed to cast vote');
      console.error('Error casting vote:', err);
    }
  }, [isAuthenticated]);

  const selectedDebate = debates.find(d => d.id === selectedDebateId) || null;

  return {
    debates,
    selectedDebate,
    selectDebate,
    castVote,
    castDebateVote,
    loading,
    error,
    refetch: loadDebates
  };
};
