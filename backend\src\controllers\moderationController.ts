import { Request, Response, NextFunction } from 'express';
import { ModerationItem } from '../models/ModerationQueue';
import { MessageCleanupService } from '../services/messageCleanupService';

interface AuthRequest extends Request {
  user?: {
    id: string;
    name: string;
    email: string;
    role?: string;
  };
}

// GET /api/moderation/queue - Get pending moderation items
export const getModerationQueue = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Check if user has admin or moderator role
    if (req.user?.role !== 'admin' && req.user?.role !== 'moderator') {
      res.status(403).json({ success: false, message: 'Admin or moderator access required' });
      return;
    }

    const { status = 'pending', page = 1, limit = 20 } = req.query;
    
    const skip = (Number(page) - 1) * Number(limit);
    
    const items = await ModerationItem.find({ status })
      .sort({ reportedAt: -1 })
      .skip(skip)
      .limit(Number(limit));

    const total = await ModerationItem.countDocuments({ status });

    res.json({
      success: true,
      data: {
        items,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

// GET /api/moderation/stats - Get moderation statistics
export const getModerationStats = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Check if user has admin or moderator role
    if (req.user?.role !== 'admin' && req.user?.role !== 'moderator') {
      res.status(403).json({ success: false, message: 'Admin or moderator access required' });
      return;
    }

    const [pending, reviewed, resolved, dismissed] = await Promise.all([
      ModerationItem.countDocuments({ status: 'pending' }),
      ModerationItem.countDocuments({ status: 'reviewed' }),
      ModerationItem.countDocuments({ status: 'resolved' }),
      ModerationItem.countDocuments({ status: 'dismissed' })
    ]);

    const cleanupStats = await MessageCleanupService.getCleanupStats();

    res.json({
      success: true,
      data: {
        moderation: {
          pending,
          reviewed,
          resolved,
          dismissed,
          total: pending + reviewed + resolved + dismissed
        },
        cleanup: cleanupStats
      }
    });
  } catch (error) {
    next(error);
  }
};

// PUT /api/moderation/:id/review - Review a moderation item
export const reviewModerationItem = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Check if user has admin or moderator role
    if (req.user?.role !== 'admin' && req.user?.role !== 'moderator') {
      res.status(403).json({ success: false, message: 'Admin or moderator access required' });
      return;
    }

    const { id } = req.params;
    const { status, moderatorNotes, actionTaken } = req.body;
    const moderatorId = req.user!.id;

    const item = await ModerationItem.findById(id);
    if (!item) {
      res.status(404).json({
        success: false,
        message: 'Moderation item not found'
      });
      return;
    }

    item.status = status;
    item.moderatorId = moderatorId;
    item.moderatorNotes = moderatorNotes;
    item.actionTaken = actionTaken;
    item.reviewedAt = new Date();

    await item.save();

    res.json({
      success: true,
      data: item,
      message: 'Moderation item updated successfully'
    });
  } catch (error) {
    next(error);
  }
};

// POST /api/moderation/cleanup - Manually trigger cleanup
export const triggerCleanup = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Check if user has admin role (cleanup is admin-only)
    if (req.user?.role !== 'admin') {
      res.status(403).json({ success: false, message: 'Admin access required' });
      return;
    }

    await MessageCleanupService.cleanupOldMessages();

    res.json({
      success: true,
      message: 'Cleanup completed successfully'
    });
  } catch (error) {
    next(error);
  }
};

// GET /api/moderation/cleanup/stats - Get cleanup statistics
export const getCleanupStats = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Check if user has admin or moderator role
    if (req.user?.role !== 'admin' && req.user?.role !== 'moderator') {
      res.status(403).json({ success: false, message: 'Admin or moderator access required' });
      return;
    }

    const stats = await MessageCleanupService.getCleanupStats();

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    next(error);
  }
};
