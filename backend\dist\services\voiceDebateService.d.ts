import { IDebate, IVoiceSpeaker, IParticipant } from '../models/Debate';
export declare class VoiceDebateService {
    private static speakerTimers;
    private static readonly DEFAULT_TIME_BLOCKS;
    private static readonly TIME_BLOCK_DURATION;
    private static timeTrackingIntervals;
    static initializeVoiceDebate(debateId: string, settings?: Partial<any>): Promise<IDebate | null>;
    static joinVoiceQueue(debateId: string, participant: IParticipant, side: 'FOR' | 'AGAINST', timeAllocation?: number): Promise<{
        success: boolean;
        message: string;
        speaker?: IVoiceSpeaker;
    }>;
    static leaveVoiceQueue(debateId: string, participantId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    static handleMutualExclusion(debateId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    static handleMutualExclusionRelease(debateId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    static rotateToNextSpeaker(debateId: string): Promise<{
        success: boolean;
        speaker?: IVoiceSpeaker;
    }>;
    private static startSpeakerTimer;
    private static startTimeTracking;
    private static stopTimeTracking;
    private static stopSpeakerTimer;
    private static handleSpeakerTimeUp;
    static allocateTime(debateId: string, speakerId: string, additionalTime: number, allocatedBy: string): Promise<{
        success: boolean;
        message: string;
    }>;
    static pauseTimeTracking(debateId: string): Promise<void>;
    static resumeTimeTracking(debateId: string): Promise<void>;
    static handleMutualExclusionLegacy(debateId: string, speakingParticipants: string[]): Promise<void>;
    static initializeTimeBlocks(userId: string, debateId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    static getTimeBlockBalance(userId: string, debateId: string): Promise<{
        timeBlocksRemaining: number;
        timeBlocksUsed: number;
    } | null>;
    static useTimeBlock(userId: string, debateId: string, speakerId: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
//# sourceMappingURL=voiceDebateService.d.ts.map