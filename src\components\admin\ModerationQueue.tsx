import React, { useState, useEffect } from 'react';
import { ModerationItem } from '../../types';
import { moderationAPI } from '../../utils/adminApi';
import { getDisplayName } from '../../utils/userDisplay';

export const ModerationQueue: React.FC = () => {
  const [items, setItems] = useState<ModerationItem[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState('pending');
  const [stats, setStats] = useState<any>(null);

  useEffect(() => {
    loadModerationQueue();
    loadModerationStats();
  }, [pagination.page, statusFilter]);

  const loadModerationQueue = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await moderationAPI.getQueue({
        status: statusFilter === 'all' ? undefined : statusFilter,
        page: pagination.page,
        limit: pagination.limit
      });
      
      if (response.success) {
        setItems(response.data.items);
        setPagination(response.data.pagination);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load moderation queue');
    } finally {
      setLoading(false);
    }
  };

  const loadModerationStats = async () => {
    try {
      const response = await moderationAPI.getStats();
      if (response.success) {
        setStats(response.data);
      }
    } catch (err) {
      console.error('Failed to load moderation stats:', err);
    }
  };

  const handleReviewItem = async (
    itemId: string, 
    status: 'reviewed' | 'resolved' | 'dismissed',
    actionTaken?: 'none' | 'warning' | 'comment_removed' | 'user_suspended',
    notes?: string
  ) => {
    try {
      const response = await moderationAPI.reviewItem(itemId, {
        status,
        actionTaken,
        moderatorNotes: notes
      });
      
      if (response.success) {
        setItems(items.map(item => 
          item.id === itemId ? response.data : item
        ));
      }
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Failed to review item');
    }
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Moderation Queue</h2>
        <button
          onClick={loadModerationQueue}
          className="bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg transition-colors"
        >
          Refresh
        </button>
      </div>

      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-brand-text-secondary text-sm">Pending</p>
                <p className="text-2xl font-bold text-yellow-400">{stats.moderation.pending}</p>
              </div>
              <span className="text-2xl">⏳</span>
            </div>
          </div>
          <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-brand-text-secondary text-sm">Reviewed</p>
                <p className="text-2xl font-bold text-blue-400">{stats.moderation.reviewed}</p>
              </div>
              <span className="text-2xl">👁️</span>
            </div>
          </div>
          <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-brand-text-secondary text-sm">Resolved</p>
                <p className="text-2xl font-bold text-green-400">{stats.moderation.resolved}</p>
              </div>
              <span className="text-2xl">✅</span>
            </div>
          </div>
          <div className="bg-gray-500/20 border border-gray-500/30 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-brand-text-secondary text-sm">Dismissed</p>
                <p className="text-2xl font-bold text-gray-400">{stats.moderation.dismissed}</p>
              </div>
              <span className="text-2xl">❌</span>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-brand-surface rounded-lg p-6">
        <div className="flex items-center space-x-4">
          <label className="text-brand-text-secondary text-sm font-medium">
            Status Filter:
          </label>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="bg-brand-surface-light border border-brand-border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-brand-primary"
          >
            <option value="pending">Pending</option>
            <option value="reviewed">Reviewed</option>
            <option value="resolved">Resolved</option>
            <option value="dismissed">Dismissed</option>
            <option value="all">All</option>
          </select>
        </div>
      </div>

      {/* Moderation Items */}
      <div className="space-y-4">
        {loading ? (
          <div className="bg-brand-surface rounded-lg p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary mx-auto mb-4"></div>
            <p className="text-brand-text-secondary">Loading moderation queue...</p>
          </div>
        ) : error ? (
          <div className="bg-brand-surface rounded-lg p-8 text-center">
            <p className="text-red-400 mb-4">{error}</p>
            <button
              onClick={loadModerationQueue}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Retry
            </button>
          </div>
        ) : items.length === 0 ? (
          <div className="bg-brand-surface rounded-lg p-8 text-center">
            <p className="text-brand-text-secondary">No moderation items found</p>
          </div>
        ) : (
          items.map((item) => (
            <ModerationItemCard
              key={item.id}
              item={item}
              onReview={handleReviewItem}
            />
          ))
        )}
      </div>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="bg-brand-surface rounded-lg px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="text-brand-text-secondary text-sm">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} items
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1}
                className="px-3 py-1 rounded bg-brand-surface-light text-brand-text-secondary hover:bg-brand-primary hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Previous
              </button>
              <span className="px-3 py-1 text-white">
                Page {pagination.page} of {pagination.pages}
              </span>
              <button
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page === pagination.pages}
                className="px-3 py-1 rounded bg-brand-surface-light text-brand-text-secondary hover:bg-brand-primary hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

interface ModerationItemCardProps {
  item: ModerationItem;
  onReview: (
    itemId: string, 
    status: 'reviewed' | 'resolved' | 'dismissed',
    actionTaken?: 'none' | 'warning' | 'comment_removed' | 'user_suspended',
    notes?: string
  ) => void;
}

const ModerationItemCard: React.FC<ModerationItemCardProps> = ({ item, onReview }) => {
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [reviewNotes, setReviewNotes] = useState('');
  const [selectedAction, setSelectedAction] = useState<'none' | 'warning' | 'comment_removed' | 'user_suspended'>('none');

  const handleSubmitReview = (status: 'reviewed' | 'resolved' | 'dismissed') => {
    onReview(item.id, status, selectedAction, reviewNotes);
    setShowReviewForm(false);
    setReviewNotes('');
    setSelectedAction('none');
  };

  return (
    <div className="bg-brand-surface rounded-lg p-6">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-lg font-semibold text-white mb-2">{item.debateTitle}</h3>
          <div className="flex items-center space-x-4 text-sm text-brand-text-secondary">
            <span>Reported by: {item.reportedBy}</span>
            <span>•</span>
            <span>{new Date(item.reportedAt).toLocaleString()}</span>
            <span>•</span>
            <span className={`px-2 py-1 rounded text-xs font-medium ${
              item.status === 'pending' ? 'bg-yellow-500/20 text-yellow-400' :
              item.status === 'reviewed' ? 'bg-blue-500/20 text-blue-400' :
              item.status === 'resolved' ? 'bg-green-500/20 text-green-400' :
              'bg-gray-500/20 text-gray-400'
            }`}>
              {item.status}
            </span>
          </div>
        </div>
      </div>

      <div className="bg-brand-surface-light rounded-lg p-4 mb-4">
        <div className="mb-2">
          <span className="text-brand-text-secondary text-sm">Reported Comment by </span>
          <span className="text-white font-medium">{getDisplayName(item.commentAuthor)}</span>
          <span className="text-brand-text-secondary text-sm"> on {new Date(item.commentCreatedAt).toLocaleString()}</span>
        </div>
        <p className="text-white">{item.commentText}</p>
      </div>

      {item.status === 'pending' && (
        <div className="flex space-x-2">
          <button
            onClick={() => setShowReviewForm(!showReviewForm)}
            className="bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg transition-colors"
          >
            Review
          </button>
          <button
            onClick={() => onReview(item.id, 'dismissed')}
            className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Dismiss
          </button>
        </div>
      )}

      {showReviewForm && (
        <div className="mt-4 bg-brand-surface-light rounded-lg p-4">
          <div className="space-y-4">
            <div>
              <label className="block text-brand-text-secondary text-sm font-medium mb-2">
                Action to Take
              </label>
              <select
                value={selectedAction}
                onChange={(e) => setSelectedAction(e.target.value as any)}
                className="w-full bg-brand-surface border border-brand-border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-brand-primary"
              >
                <option value="none">No Action</option>
                <option value="warning">Send Warning</option>
                <option value="comment_removed">Remove Comment</option>
                <option value="user_suspended">Suspend User</option>
              </select>
            </div>
            
            <div>
              <label className="block text-brand-text-secondary text-sm font-medium mb-2">
                Moderator Notes
              </label>
              <textarea
                value={reviewNotes}
                onChange={(e) => setReviewNotes(e.target.value)}
                placeholder="Add notes about your decision..."
                rows={3}
                className="w-full bg-brand-surface border border-brand-border rounded-lg px-3 py-2 text-white placeholder-brand-text-secondary focus:outline-none focus:ring-2 focus:ring-brand-primary"
              />
            </div>

            <div className="flex space-x-2">
              <button
                onClick={() => handleSubmitReview('resolved')}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Resolve
              </button>
              <button
                onClick={() => handleSubmitReview('reviewed')}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Mark Reviewed
              </button>
              <button
                onClick={() => setShowReviewForm(false)}
                className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {item.moderatorNotes && (
        <div className="mt-4 bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
          <p className="text-blue-400 text-sm font-medium mb-1">Moderator Notes:</p>
          <p className="text-white text-sm">{item.moderatorNotes}</p>
        </div>
      )}
    </div>
  );
};
