{"name": "agora-backend", "version": "1.0.0", "description": "Backend API for Agora debate platform", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"@types/node-cron": "^3.0.11", "@types/uuid": "^10.0.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "node-cron": "^4.2.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "keywords": ["debate", "platform", "api", "express", "typescript"], "author": "Your Name", "license": "MIT"}