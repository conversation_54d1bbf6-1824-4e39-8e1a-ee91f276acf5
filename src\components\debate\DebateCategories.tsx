import React, { useState, useEffect } from 'react';
import { debatesAPI } from '../../utils/api';
import { DEBATE_CATEGORIES, Category } from '../../constants/categories';

interface CategoryWithCount extends Category {
  debateCount: number;
}

interface DebateCategoriesProps {
  onCategorySelect: (categoryId: string | null) => void;
  selectedCategory: string | null;
}

export const DebateCategories: React.FC<DebateCategoriesProps> = ({
  onCategorySelect,
  selectedCategory
}) => {
  const [categories, setCategories] = useState<CategoryWithCount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      // Use shared categories with debate counts
      const categoriesWithCounts = await Promise.all(
        DEBATE_CATEGORIES.map(async (category) => {
          try {
            // This would be replaced with actual API call to get debate count by category
            const response = await debatesAPI.getDebates({ category: category.id, limit: 1 });
            return {
              ...category,
              debateCount: response.pagination?.total || 0
            };
          } catch {
            return { ...category, debateCount: 0 };
          }
        })
      );

      setCategories(categoriesWithCounts);
    } catch (err) {
      setError('Failed to load categories');
      setCategories(DEBATE_CATEGORIES.map(cat => ({ ...cat, debateCount: 0 })));
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-white mb-4">Browse Categories</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {[...Array(10)].map((_, i) => (
            <div key={i} className="bg-brand-surface rounded-lg p-4 animate-pulse">
              <div className="w-12 h-12 bg-gray-600 rounded-lg mb-3"></div>
              <div className="h-4 bg-gray-600 rounded mb-2"></div>
              <div className="h-3 bg-gray-700 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mb-8">
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
          <p className="text-red-400">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold text-white">Browse Categories</h2>
        {selectedCategory && (
          <button
            onClick={() => onCategorySelect(null)}
            className="text-brand-primary hover:text-brand-primary-light text-sm font-medium"
          >
            Clear Filter
          </button>
        )}
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => onCategorySelect(category.id)}
            className={`
              group relative overflow-hidden rounded-lg p-4 text-left transition-all duration-200
              ${selectedCategory === category.id 
                ? 'ring-2 ring-brand-primary bg-brand-primary/10' 
                : 'bg-brand-surface hover:bg-brand-surface-light hover:scale-105'
              }
            `}
          >
            {/* Background gradient */}
            <div className={`absolute inset-0 ${category.color} opacity-10 group-hover:opacity-20 transition-opacity`}></div>
            
            {/* Content */}
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-3">
                <div className="text-3xl">{category.icon}</div>
                {selectedCategory === category.id && (
                  <div className="w-2 h-2 bg-brand-primary rounded-full"></div>
                )}
              </div>
              
              <h3 className="font-bold text-white mb-1 group-hover:text-brand-primary transition-colors">
                {category.name}
              </h3>
              
              <p className="text-xs text-brand-text-secondary mb-2 line-clamp-2">
                {category.description}
              </p>
              
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium text-brand-text-light">
                  {category.debateCount} debates
                </span>
                {category.debateCount > 0 && (
                  <div className="w-1 h-1 bg-green-400 rounded-full"></div>
                )}
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* Selected category info */}
      {selectedCategory && (
        <div className="mt-4 p-4 bg-brand-primary/10 border border-brand-primary/20 rounded-lg">
          {(() => {
            const category = categories.find(c => c.id === selectedCategory);
            return category ? (
              <div className="flex items-center gap-3">
                <span className="text-2xl">{category.icon}</span>
                <div>
                  <h3 className="font-bold text-white">{category.name}</h3>
                  <p className="text-sm text-brand-text-secondary">{category.description}</p>
                </div>
              </div>
            ) : null;
          })()}
        </div>
      )}
    </div>
  );
};
