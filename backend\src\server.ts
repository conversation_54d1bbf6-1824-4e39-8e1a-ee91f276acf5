import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { createServer } from 'http';
import { Server } from 'socket.io';
import jwt from 'jsonwebtoken';
import User from './models/User';
import { VoiceDebateService } from './services/voiceDebateService';

// Import database connection
import connectDB from './config/database';

// Import routes
import debateRoutes from './routes/debates';
import voiceDebateRoutes from './routes/voiceDebate';
import userRoutes from './routes/users';
import authRoutes from './routes/auth';
import adminRoutes from './routes/admin';

// Import middleware
import { errorHandler } from './middleware/errorHandler';
import { notFound } from './middleware/notFound';

// Load environment variables
dotenv.config();

// Connect to database
connectDB();

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || ["http://localhost:3000", "http://localhost:3001", "http://localhost:3002"],
    methods: ["GET", "POST"]
  }
});

// Track socket-to-user mappings and user-to-debate mappings
const socketToUser = new Map<string, string>(); // socketId -> userId
const userToDebates = new Map<string, Set<string>>(); // userId -> Set of debateIds

const PORT = process.env.PORT || 5000;

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || ["http://localhost:3000", "http://localhost:3001", "http://localhost:3002"],
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/debates', debateRoutes);
app.use('/api/debates', voiceDebateRoutes);
app.use('/api/users', userRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/moderation', require('./routes/moderation').default);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Socket.IO for real-time features
io.on('connection', async (socket) => {
  console.log('User connected:', socket.id);

  // Authenticate socket connection
  try {
    const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
    if (token) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as { userId: string };
      const user = await User.findById(decoded.userId);
      if (user) {
        socketToUser.set(socket.id, user.id);
        console.log(`Socket ${socket.id} authenticated as user ${user.id}`);
      }
    }
  } catch (error) {
    console.log(`Socket ${socket.id} authentication failed:`, error);
  }

  socket.on('join-debate', (debateId: string) => {
    socket.join(`debate-${debateId}`);
    console.log(`User ${socket.id} joined debate ${debateId}`);

    // Track user-to-debate mapping
    const userId = socketToUser.get(socket.id);
    if (userId) {
      if (!userToDebates.has(userId)) {
        userToDebates.set(userId, new Set());
      }
      userToDebates.get(userId)!.add(debateId);
    }
  });

  socket.on('leave-debate', async (debateId: string) => {
    socket.leave(`debate-${debateId}`);
    console.log(`User ${socket.id} left debate ${debateId}`);

    // Remove user from voice queue if they were in it
    const userId = socketToUser.get(socket.id);
    if (userId) {
      try {
        const result = await VoiceDebateService.leaveVoiceQueue(debateId, userId);
        if (result.success) {
          console.log(`Automatically removed user ${userId} from voice queue for debate ${debateId}`);
        } else {
          console.log(`User ${userId} was not in voice queue for debate ${debateId}: ${result.message}`);
        }
      } catch (error) {
        console.log(`Failed to remove user ${userId} from voice queue:`, error);
      }

      // Update user-to-debate tracking
      const userDebates = userToDebates.get(userId);
      if (userDebates) {
        userDebates.delete(debateId);
        if (userDebates.size === 0) {
          userToDebates.delete(userId);
        }
      }
    }
  });

  // Join argument-specific chat room
  socket.on('join-argument-chat', (data: { debateId: string; argumentId: string }) => {
    const roomName = `argument-${data.argumentId}`;
    socket.join(roomName);
    console.log(`User ${socket.id} joined argument chat ${data.argumentId}`);
  });

  // Leave argument-specific chat room
  socket.on('leave-argument-chat', (data: { debateId: string; argumentId: string }) => {
    const roomName = `argument-${data.argumentId}`;
    socket.leave(roomName);
    console.log(`User ${socket.id} left argument chat ${data.argumentId}`);
  });

  // Voice debate events
  socket.on('join-voice-debate', (debateId: string) => {
    socket.join(`voice-${debateId}`);
    console.log(`User ${socket.id} joined voice debate ${debateId}`);
  });

  socket.on('leave-voice-debate', (debateId: string) => {
    socket.leave(`voice-${debateId}`);
    console.log(`User ${socket.id} left voice debate ${debateId}`);
  });

  socket.on('voice-speaking-state', (data: { debateId: string; isSpeaking: boolean; participantId: string }) => {
    // Broadcast speaking state to all users in the voice debate
    socket.to(`voice-${data.debateId}`).emit('participant-speaking-state', {
      participantId: data.participantId,
      isSpeaking: data.isSpeaking
    });
  });

  socket.on('voice-mute-state', (data: { debateId: string; isMuted: boolean; participantId: string }) => {
    // Broadcast mute state to all users in the voice debate
    socket.to(`voice-${data.debateId}`).emit('participant-mute-state', {
      participantId: data.participantId,
      isMuted: data.isMuted
    });
  });

  socket.on('disconnect', async () => {
    console.log('User disconnected:', socket.id);

    // Remove user from all voice queues they were in
    const userId = socketToUser.get(socket.id);
    if (userId) {
      const userDebates = userToDebates.get(userId);
      if (userDebates) {
        for (const debateId of userDebates) {
          try {
            const result = await VoiceDebateService.leaveVoiceQueue(debateId, userId);
            if (result.success) {
              console.log(`Automatically removed disconnected user ${userId} from voice queue for debate ${debateId}`);
            } else {
              console.log(`Disconnected user ${userId} was not in voice queue for debate ${debateId}: ${result.message}`);
            }
          } catch (error) {
            console.log(`Failed to remove disconnected user ${userId} from voice queue:`, error);
          }
        }
      }

      // Clean up tracking maps
      socketToUser.delete(socket.id);
      userToDebates.delete(userId);
    }
  });
});

// Error handling middleware (must be last)
app.use(notFound);
app.use(errorHandler);

server.listen(Number(PORT), '0.0.0.0', () => {
  console.log(`🚀 Server running on port ${PORT} (all interfaces)`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
});

export { io };
