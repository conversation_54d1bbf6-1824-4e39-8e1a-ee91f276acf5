"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateProfile = exports.getProfile = exports.login = exports.register = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const User_1 = __importDefault(require("../models/User"));
const generateToken = (userId) => {
    return jsonwebtoken_1.default.sign({ userId }, process.env.JWT_SECRET || 'fallback-secret', {
        expiresIn: process.env.JWT_EXPIRE || '7d'
    });
};
const register = async (req, res, next) => {
    try {
        const { username, email, password, firstName, lastName } = req.body;
        const existingUser = await User_1.default.findOne({
            $or: [{ email }, { username }]
        });
        if (existingUser) {
            res.status(400).json({
                success: false,
                message: 'User with this email or username already exists'
            });
            return;
        }
        const user = await User_1.default.create({
            username,
            email,
            password,
            firstName,
            lastName,
            privacySettings: {
                displayName: 'fullName'
            }
        });
        const token = generateToken(user._id);
        res.status(201).json({
            success: true,
            data: {
                user: {
                    id: user._id,
                    username: user.username,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    avatarUrl: user.avatarUrl,
                    role: user.role,
                    privacySettings: user.privacySettings,
                    debatesParticipated: user.debatesParticipated,
                    debatesCreated: user.debatesCreated,
                    reputation: user.reputation,
                    totalVotes: user.totalVotes,
                    createdAt: user.createdAt
                },
                token
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.register = register;
const login = async (req, res, next) => {
    try {
        const { email, password } = req.body;
        const user = await User_1.default.findOne({ email }).select('+password');
        if (!user || !(await user.comparePassword(password))) {
            res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
            return;
        }
        const token = generateToken(user._id);
        res.json({
            success: true,
            data: {
                user: {
                    id: user._id,
                    username: user.username,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    avatarUrl: user.avatarUrl,
                    role: user.role,
                    privacySettings: user.privacySettings,
                    debatesParticipated: user.debatesParticipated,
                    debatesCreated: user.debatesCreated,
                    reputation: user.reputation,
                    totalVotes: user.totalVotes,
                    createdAt: user.createdAt
                },
                token
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.login = login;
const getProfile = async (req, res, next) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Not authenticated'
            });
            return;
        }
        res.json({
            success: true,
            data: {
                user: {
                    id: req.user._id,
                    username: req.user.username,
                    email: req.user.email,
                    firstName: req.user.firstName,
                    lastName: req.user.lastName,
                    avatarUrl: req.user.avatarUrl,
                    bio: req.user.bio,
                    role: req.user.role,
                    privacySettings: req.user.privacySettings,
                    debatesParticipated: req.user.debatesParticipated,
                    debatesCreated: req.user.debatesCreated,
                    reputation: req.user.reputation,
                    totalVotes: req.user.totalVotes,
                    createdAt: req.user.createdAt
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getProfile = getProfile;
const updateProfile = async (req, res, next) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Not authenticated'
            });
            return;
        }
        const { firstName, lastName, bio, avatarUrl, privacySettings } = req.body;
        const updateData = {};
        if (firstName !== undefined)
            updateData.firstName = firstName;
        if (lastName !== undefined)
            updateData.lastName = lastName;
        if (bio !== undefined)
            updateData.bio = bio;
        if (avatarUrl)
            updateData.avatarUrl = avatarUrl;
        if (privacySettings)
            updateData.privacySettings = privacySettings;
        const user = await User_1.default.findByIdAndUpdate(req.user._id, updateData, { new: true, runValidators: true });
        res.json({
            success: true,
            data: {
                user: {
                    id: user._id,
                    username: user.username,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    avatarUrl: user.avatarUrl,
                    bio: user.bio,
                    role: user.role,
                    privacySettings: user.privacySettings,
                    debatesParticipated: user.debatesParticipated,
                    debatesCreated: user.debatesCreated,
                    reputation: user.reputation,
                    totalVotes: user.totalVotes,
                    createdAt: user.createdAt
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.updateProfile = updateProfile;
//# sourceMappingURL=authController.js.map