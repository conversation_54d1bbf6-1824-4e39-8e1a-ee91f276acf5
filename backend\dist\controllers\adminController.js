"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAdminStats = exports.suspendUser = exports.updateUserRole = exports.getUserDetails = exports.getUsers = void 0;
const User_1 = __importDefault(require("../models/User"));
const Debate_1 = __importDefault(require("../models/Debate"));
const ModerationQueue_1 = require("../models/ModerationQueue");
const getUsers = async (req, res, next) => {
    try {
        if (req.user?.role !== 'admin') {
            res.status(403).json({ success: false, message: 'Admin access required' });
            return;
        }
        const { page = 1, limit = 20, role, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
        const skip = (Number(page) - 1) * Number(limit);
        const filter = {};
        if (role && role !== 'all') {
            filter.role = role;
        }
        if (search) {
            filter.$or = [
                { username: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } },
                { firstName: { $regex: search, $options: 'i' } },
                { lastName: { $regex: search, $options: 'i' } }
            ];
        }
        const sort = {};
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
        const users = await User_1.default.find(filter)
            .select('-password')
            .sort(sort)
            .skip(skip)
            .limit(Number(limit));
        const total = await User_1.default.countDocuments(filter);
        res.json({
            success: true,
            data: {
                users,
                pagination: {
                    page: Number(page),
                    limit: Number(limit),
                    total,
                    pages: Math.ceil(total / Number(limit))
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getUsers = getUsers;
const getUserDetails = async (req, res, next) => {
    try {
        if (req.user?.role !== 'admin') {
            res.status(403).json({ success: false, message: 'Admin access required' });
            return;
        }
        const { id } = req.params;
        const user = await User_1.default.findById(id).select('-password');
        if (!user) {
            res.status(404).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        const debates = await Debate_1.default.find({
            $or: [
                { 'proponent.id': id },
                { 'opponent.id': id }
            ]
        }).select('title status createdAt');
        const moderationHistory = await ModerationQueue_1.ModerationItem.find({
            $or: [
                { reportedBy: id },
                { 'commentAuthor.id': id }
            ]
        }).sort({ reportedAt: -1 }).limit(10);
        res.json({
            success: true,
            data: {
                user,
                debates,
                moderationHistory
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getUserDetails = getUserDetails;
const updateUserRole = async (req, res, next) => {
    try {
        if (req.user?.role !== 'admin') {
            res.status(403).json({ success: false, message: 'Admin access required' });
            return;
        }
        const { id } = req.params;
        const { role } = req.body;
        if (!['user', 'moderator', 'admin'].includes(role)) {
            res.status(400).json({
                success: false,
                message: 'Invalid role. Must be user, moderator, or admin'
            });
            return;
        }
        const user = await User_1.default.findByIdAndUpdate(id, { role }, { new: true, runValidators: true }).select('-password');
        if (!user) {
            res.status(404).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        res.json({
            success: true,
            data: user,
            message: `User role updated to ${role}`
        });
    }
    catch (error) {
        next(error);
    }
};
exports.updateUserRole = updateUserRole;
const suspendUser = async (req, res, next) => {
    try {
        if (req.user?.role !== 'admin') {
            res.status(403).json({ success: false, message: 'Admin access required' });
            return;
        }
        const { id } = req.params;
        const { suspended, reason } = req.body;
        if (suspended && !reason) {
            res.status(400).json({
                success: false,
                message: 'Suspension reason is required when suspending a user'
            });
            return;
        }
        const user = await User_1.default.findById(id);
        if (!user) {
            res.status(404).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        const updateData = {
            suspended: suspended,
            suspensionReason: suspended ? reason : undefined,
            suspendedAt: suspended ? new Date() : undefined,
            suspendedBy: suspended ? req.user.id : undefined
        };
        const updatedUser = await User_1.default.findByIdAndUpdate(id, updateData, { new: true, runValidators: true }).select('-password');
        res.json({
            success: true,
            data: updatedUser,
            message: suspended ? 'User suspended successfully' : 'User unsuspended successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.suspendUser = suspendUser;
const getAdminStats = async (req, res, next) => {
    try {
        if (req.user?.role !== 'admin') {
            res.status(403).json({ success: false, message: 'Admin access required' });
            return;
        }
        const [totalUsers, totalDebates, activeDebates, pendingModerationItems, totalModerationItems, usersToday, debatesToday] = await Promise.all([
            User_1.default.countDocuments(),
            Debate_1.default.countDocuments(),
            Debate_1.default.countDocuments({ status: 'active' }),
            ModerationQueue_1.ModerationItem.countDocuments({ status: 'pending' }),
            ModerationQueue_1.ModerationItem.countDocuments(),
            User_1.default.countDocuments({
                createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
            }),
            Debate_1.default.countDocuments({
                createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
            })
        ]);
        const userRoles = await User_1.default.aggregate([
            { $group: { _id: '$role', count: { $sum: 1 } } }
        ]);
        const recentUsers = await User_1.default.find()
            .select('username email role createdAt')
            .sort({ createdAt: -1 })
            .limit(5);
        const recentDebates = await Debate_1.default.find()
            .select('title status createdAt proponent')
            .sort({ createdAt: -1 })
            .limit(5);
        res.json({
            success: true,
            data: {
                overview: {
                    totalUsers,
                    totalDebates,
                    activeDebates,
                    pendingModerationItems,
                    totalModerationItems,
                    usersToday,
                    debatesToday
                },
                userRoles: userRoles.reduce((acc, role) => {
                    acc[role._id] = role.count;
                    return acc;
                }, {}),
                recentActivity: {
                    users: recentUsers,
                    debates: recentDebates
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getAdminStats = getAdminStats;
//# sourceMappingURL=adminController.js.map