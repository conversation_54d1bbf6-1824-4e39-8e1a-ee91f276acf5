import React, { useState, useRef, useEffect } from 'react';
import { Argument, Comment } from '../../types';
import { ParticipantIcon } from '../ui/ParticipantIcon';
import { ArrowLeftIcon, ChatIcon, FlagIcon } from '../ui/icons';
import { useAuth } from '../../contexts/AuthContext';
import { useSocket } from '../../contexts/SocketContext';

interface ChatViewProps {
  argument: Argument;
  debateId: string;
  onBack: () => void;
  onAddComment: (argumentId: string, text: string, parentCommentId?: string) => void;
  onVoteOnComment: (argumentId: string, commentId: string, voteType: 'up' | 'down') => void;
  onReportComment: (argumentId: string, commentId: string) => void;
  onArgumentUpdate: (updatedArgument: Argument) => void;
}

interface ChatMessageProps {
  comment: Comment;
  onReply: (parentId: string) => void;
  onVote: (commentId: string, voteType: 'up' | 'down') => void;
  onReport: (commentId: string) => void;
  currentUserId?: string;
  depth?: number;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ comment, onReply, onVote, onReport, currentUserId, depth = 0 }) => {
  const maxDepth = 3;
  const isNested = depth > 0;

  // Determine user's current vote state
  const userVote = comment.userVotes?.find(v => v.userId === currentUserId)?.vote;
  const hasUpvoted = userVote === 'up';
  const hasDownvoted = userVote === 'down';

  // Check if user has already reported this comment
  const hasReported = comment.reportedBy?.includes(currentUserId || '') || false;
  
  return (
    <div className={`${isNested ? 'ml-4 border-l border-brand-border/30 pl-4' : ''} mb-3`}>
      <div className="flex gap-3">
        <ParticipantIcon participant={comment.author} size="xs" />
        <div className="flex-1 min-w-0">
          {/* Message Header */}
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-brand-text text-sm">{comment.author.name}</span>
            <span className="text-xs text-brand-text-secondary">
              {new Date(comment.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </span>
          </div>
          
          {/* Message Content */}
          <div className="bg-brand-surface/50 rounded-lg p-3 mb-2 relative">
            <p className="text-brand-text text-sm leading-relaxed pr-6">{comment.text}</p>

            {/* Report Flag - positioned in bottom right */}
            <button
              onClick={() => onReport(comment.id)}
              disabled={hasReported}
              className={`absolute bottom-2 right-2 transition-colors ${
                hasReported
                  ? 'text-red-500 opacity-100 cursor-not-allowed'
                  : 'text-brand-text-secondary hover:text-red-400 opacity-60 hover:opacity-100'
              }`}
              title={hasReported ? "Reported" : "Report this message"}
            >
              <FlagIcon className={`w-3 h-3 ${hasReported ? 'fill-current' : ''}`} />
            </button>
          </div>
          
          {/* Message Actions */}
          <div className="flex items-center gap-4 text-xs">
            <div className="flex items-center gap-1">
              <button
                onClick={() => onVote(comment.id, 'up')}
                className={`transition-colors ${
                  hasUpvoted
                    ? 'text-green-400'
                    : 'text-brand-text-secondary hover:text-green-400'
                }`}
                title={hasUpvoted ? "Remove upvote" : "Upvote"}
              >
                ▲
              </button>
              <span className="text-brand-text-secondary min-w-[20px] text-center">
                {(comment.upvotes || 0) - (comment.downvotes || 0)}
              </span>
              <button
                onClick={() => onVote(comment.id, 'down')}
                className={`transition-colors ${
                  hasDownvoted
                    ? 'text-red-400'
                    : 'text-brand-text-secondary hover:text-red-400'
                }`}
                title={hasDownvoted ? "Remove downvote" : "Downvote"}
              >
                ▼
              </button>
            </div>
            
            {depth < maxDepth && (
              <button
                onClick={() => onReply(comment.id)}
                className="text-brand-text-secondary hover:text-brand-primary transition-colors"
              >
                Reply
              </button>
            )}
          </div>
          
          {/* Nested Replies */}
          {comment.replies && comment.replies.length > 0 && (
            <div className="mt-3">
              {comment.replies.map(reply => (
                <ChatMessage
                  key={reply.id}
                  comment={reply}
                  onReply={onReply}
                  onVote={onVote}
                  onReport={onReport}
                  currentUserId={currentUserId}
                  depth={depth + 1}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export const ChatView: React.FC<ChatViewProps> = ({
  argument,
  debateId,
  onBack,
  onAddComment,
  onVoteOnComment,
  onReportComment,
  onArgumentUpdate
}) => {
  const { user } = useAuth();
  const { socket, joinArgumentChat, leaveArgumentChat } = useSocket();
  const [newMessage, setNewMessage] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [argument.comments]);

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [replyingTo]);

  // Join argument chat room when component mounts
  useEffect(() => {
    if (socket && argument.id) {
      joinArgumentChat(debateId, argument.id);

      return () => {
        leaveArgumentChat(debateId, argument.id);
      };
    }
  }, [socket, argument.id, debateId, joinArgumentChat, leaveArgumentChat]);

  // Listen for real-time comment events
  useEffect(() => {
    if (!socket) return;

    const handleCommentAdded = (data: { argumentId: string; comment: Comment; parentCommentId?: string }) => {
      if (data.argumentId === argument.id) {
        // Skip if this comment was added by the current user (optimistic update already handled it)
        if (data.comment.author.id === user?.id) {
          return;
        }

        const updatedArgument = { ...argument };

        if (!updatedArgument.comments) {
          updatedArgument.comments = [];
        }

        if (data.parentCommentId) {
          // Add as reply to existing comment
          const addReplyToComment = (comments: Comment[]): boolean => {
            for (const comment of comments) {
              if (comment.id === data.parentCommentId) {
                if (!comment.replies) comment.replies = [];
                comment.replies.push(data.comment);
                return true;
              }
              if (comment.replies && addReplyToComment(comment.replies)) {
                return true;
              }
            }
            return false;
          };
          addReplyToComment(updatedArgument.comments);
        } else {
          // Add as top-level comment
          updatedArgument.comments.push(data.comment);
        }

        onArgumentUpdate(updatedArgument);
      }
    };

    const handleCommentVoteUpdated = (data: {
      argumentId: string;
      commentId: string;
      upvotes: number;
      downvotes: number;
      votes: number;
      voterId?: string;
    }) => {
      if (data.argumentId === argument.id) {
        // Skip if this vote was cast by the current user (optimistic update already handled it)
        if (data.voterId === user?.id) {
          return;
        }

        const updatedArgument = { ...argument };

        const updateCommentVote = (comments: Comment[]): boolean => {
          for (const comment of comments) {
            if (comment.id === data.commentId) {
              comment.upvotes = data.upvotes;
              comment.downvotes = data.downvotes;
              comment.votes = data.votes;
              return true;
            }
            if (comment.replies && updateCommentVote(comment.replies)) {
              return true;
            }
          }
          return false;
        };

        if (updatedArgument.comments) {
          updateCommentVote(updatedArgument.comments);
          onArgumentUpdate(updatedArgument);
        }
      }
    };

    socket.on('comment-added', handleCommentAdded);
    socket.on('comment-vote-updated', handleCommentVoteUpdated);

    return () => {
      socket.off('comment-added', handleCommentAdded);
      socket.off('comment-vote-updated', handleCommentVoteUpdated);
    };
  }, [socket, argument, onArgumentUpdate, user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      await onAddComment(argument.id, newMessage.trim(), replyingTo || undefined);
      setNewMessage('');
      setReplyingTo(null);
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReply = (parentId: string) => {
    setReplyingTo(parentId);
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  };

  const handleVote = (commentId: string, voteType: 'up' | 'down') => {
    onVoteOnComment(argument.id, commentId, voteType);
  };

  const handleReport = (commentId: string) => {
    onReportComment(argument.id, commentId);
  };

  const getReplyingToComment = (commentId: string, comments: Comment[]): Comment | null => {
    for (const comment of comments) {
      if (comment.id === commentId) return comment;
      if (comment.replies) {
        const found = getReplyingToComment(commentId, comment.replies);
        if (found) return found;
      }
    }
    return null;
  };

  const replyingToComment = replyingTo ? getReplyingToComment(replyingTo, argument.comments || []) : null;

  return (
    <div className="fixed inset-y-0 right-0 w-96 bg-brand-background border-l border-brand-border shadow-2xl z-50 flex flex-col transform transition-transform duration-300 ease-in-out">
      {/* Header */}
      <div className="flex items-center gap-3 p-4 border-b border-brand-border bg-brand-surface">
        <button
          onClick={onBack}
          className="text-brand-text-secondary hover:text-brand-text transition-colors p-1 rounded"
          title="Close chat"
        >
          <ArrowLeftIcon className="w-5 h-5" />
        </button>

        <div className="flex items-center gap-2 flex-1 min-w-0">
          <ChatIcon className="w-5 h-5 text-brand-primary flex-shrink-0" />
          <div className="min-w-0 flex-1">
            <h1 className="text-lg font-semibold text-brand-text truncate">Discussion</h1>
            <p className="text-xs text-brand-text-secondary truncate">
              {argument.author.name} • {argument.side === 'for' ? 'Supporting' : 'Challenging'}
            </p>
          </div>
        </div>
      </div>

      {/* Argument Context */}
      <div className="bg-brand-surface/50 p-3 border-b border-brand-border">
        <div className="flex items-center gap-2 mb-2">
          <ParticipantIcon participant={argument.author} size="xs" />
          <div className="min-w-0 flex-1">
            <span className="font-medium text-brand-text text-sm">{argument.author.name}</span>
            <span className={`ml-2 text-xs px-1.5 py-0.5 rounded ${
              argument.side === 'for' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
            }`}>
              {argument.side === 'for' ? 'FOR' : 'AGAINST'}
            </span>
          </div>
        </div>
        <p className="text-brand-text text-sm leading-relaxed line-clamp-3">{argument.text}</p>
      </div>

      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto p-3 space-y-2">
        {argument.comments && argument.comments.length > 0 ? (
          argument.comments.map(comment => (
            <ChatMessage
              key={comment.id}
              comment={comment}
              onReply={handleReply}
              onVote={handleVote}
              onReport={handleReport}
              currentUserId={user?.id}
            />
          ))
        ) : (
          <div className="text-center py-8 text-brand-text-secondary">
            <ChatIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No messages yet. Start the conversation!</p>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="border-t border-brand-border p-3 bg-brand-surface/30">
        {replyingTo && replyingToComment && (
          <div className="bg-brand-surface/50 rounded-lg p-3 mb-3 border-l-4 border-brand-primary">
            <div className="flex items-center justify-between mb-1">
              <span className="text-xs text-brand-text-secondary">
                Replying to {replyingToComment.author.name}
              </span>
              <button
                onClick={() => setReplyingTo(null)}
                className="text-brand-text-secondary hover:text-brand-text text-xs"
              >
                Cancel
              </button>
            </div>
            <p className="text-sm text-brand-text-light truncate">
              {replyingToComment.text}
            </p>
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="flex gap-3">
          <div className="flex-1">
            <textarea
              ref={textareaRef}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder={replyingTo ? "Write a reply..." : "Write a message..."}
              className="w-full bg-brand-surface border border-brand-surface-light rounded-lg p-3 text-brand-text resize-none focus:outline-none focus:ring-2 focus:ring-brand-primary/50"
              rows={2}
              maxLength={500}
              disabled={isSubmitting}
            />
            <div className="flex items-center justify-between mt-2">
              <span className="text-xs text-brand-text-secondary">
                {newMessage.length}/500
              </span>
              <button
                type="submit"
                disabled={!newMessage.trim() || isSubmitting}
                className="px-4 py-2 bg-brand-primary hover:bg-brand-primary/80 disabled:bg-brand-primary/30 text-white rounded-lg transition-colors text-sm font-medium"
              >
                {isSubmitting ? 'Sending...' : 'Send'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};
