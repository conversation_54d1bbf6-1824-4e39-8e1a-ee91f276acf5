"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const auth_1 = require("../middleware/auth");
const validate_1 = require("../middleware/validate");
const moderationController_1 = require("../controllers/moderationController");
const router = (0, express_1.Router)();
router.get('/queue', auth_1.auth, [
    (0, express_validator_1.query)('status').optional().isIn(['pending', 'reviewed', 'resolved', 'dismissed']),
    (0, express_validator_1.query)('page').optional().isInt({ min: 1 }),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }),
    validate_1.validate
], moderationController_1.getModerationQueue);
router.get('/stats', auth_1.auth, moderationController_1.getModerationStats);
router.put('/:id/review', auth_1.auth, [
    (0, express_validator_1.param)('id').isMongoId(),
    (0, express_validator_1.body)('status').isIn(['reviewed', 'resolved', 'dismissed']),
    (0, express_validator_1.body)('moderatorNotes').optional().isString().isLength({ max: 1000 }),
    (0, express_validator_1.body)('actionTaken').optional().isIn(['none', 'warning', 'comment_removed', 'user_suspended']),
    validate_1.validate
], moderationController_1.reviewModerationItem);
router.post('/cleanup', auth_1.auth, moderationController_1.triggerCleanup);
router.get('/cleanup/stats', auth_1.auth, moderationController_1.getCleanupStats);
exports.default = router;
//# sourceMappingURL=moderation.js.map