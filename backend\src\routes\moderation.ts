import { Router } from 'express';
import { param, body, query } from 'express-validator';
import { auth } from '../middleware/auth';
import { validate } from '../middleware/validate';
import {
  getModerationQueue,
  getModerationStats,
  reviewModerationItem,
  triggerCleanup,
  getCleanupStats
} from '../controllers/moderationController';

const router = Router();

// GET /api/moderation/queue - Get moderation queue
router.get('/queue', auth, [
  query('status').optional().isIn(['pending', 'reviewed', 'resolved', 'dismissed']),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  validate
], getModerationQueue);

// GET /api/moderation/stats - Get moderation statistics
router.get('/stats', auth, getModerationStats);

// PUT /api/moderation/:id/review - Review a moderation item
router.put('/:id/review', auth, [
  param('id').isMongoId(),
  body('status').isIn(['reviewed', 'resolved', 'dismissed']),
  body('moderatorNotes').optional().isString().isLength({ max: 1000 }),
  body('actionTaken').optional().isIn(['none', 'warning', 'comment_removed', 'user_suspended']),
  validate
], reviewModerationItem);

// POST /api/moderation/cleanup - Manually trigger cleanup
router.post('/cleanup', auth, triggerCleanup);

// GET /api/moderation/cleanup/stats - Get cleanup statistics
router.get('/cleanup/stats', auth, getCleanupStats);

export default router;
