import Debate from '../models/Debate';
import { ModerationItem } from '../models/ModerationQueue';

export class MessageCleanupService {
  private static readonly MESSAGE_RETENTION_HOURS = 24;
  private static readonly MAX_COMMENTS_PER_ARGUMENT = 100; // Limit per argument thread

  /**
   * Clean up old messages only when needed to make room for new messages
   * Called before adding new comments when limit is reached
   */
  static async cleanupIfNeeded(debateId: string, argumentId: string): Promise<void> {
    try {
      const debate = await Debate.findById(debateId);
      if (!debate) return;

      const argument = this.findArgument(debate.arguments, argumentId);
      if (!argument || !argument.comments) return;

      const totalComments = this.countTotalComments(argument.comments);

      // Only cleanup if we're at or near the limit
      if (totalComments >= this.MAX_COMMENTS_PER_ARGUMENT) {
        console.log(`Cleaning up old messages in argument ${argumentId} (${totalComments} comments)`);
        await this.cleanupArgumentComments(debate, argument);
      }
    } catch (error) {
      console.error('Error during conditional cleanup:', error);
      // Don't throw - allow new message to be added even if cleanup fails
    }
  }

  /**
   * Clean up messages older than 24 hours (for manual/admin use)
   * Reported messages are moved to moderation queue before deletion
   */
  static async cleanupOldMessages(): Promise<void> {
    try {
      console.log('Starting message cleanup process...');
      
      const cutoffDate = new Date();
      cutoffDate.setHours(cutoffDate.getHours() - this.MESSAGE_RETENTION_HOURS);
      
      // Find all debates with comments
      const debates = await Debate.find({
        'arguments.comments': { $exists: true, $ne: [] }
      });

      let totalProcessed = 0;
      let totalReported = 0;
      let totalDeleted = 0;

      for (const debate of debates) {
        const result = await this.processDebateComments(debate, cutoffDate);
        totalProcessed += result.processed;
        totalReported += result.reported;
        totalDeleted += result.deleted;
      }

      console.log(`Message cleanup completed:`, {
        totalProcessed,
        totalReported,
        totalDeleted,
        cutoffDate: cutoffDate.toISOString()
      });

    } catch (error) {
      console.error('Error during message cleanup:', error);
      throw error;
    }
  }

  /**
   * Process comments in a single debate
   */
  private static async processDebateComments(debate: any, cutoffDate: Date) {
    let processed = 0;
    let reported = 0;
    let deleted = 0;
    let debateModified = false;

    // Process each argument's comments
    for (const argument of debate.arguments) {
      if (!argument.comments || argument.comments.length === 0) continue;

      const result = await this.processCommentTree(
        argument.comments,
        cutoffDate,
        debate,
        argument
      );

      processed += result.processed;
      reported += result.reported;
      deleted += result.deleted;

      if (result.modified) {
        debateModified = true;
      }
    }

    // Save the debate if any comments were removed
    if (debateModified) {
      await debate.save();
    }

    return { processed, reported, deleted };
  }

  /**
   * Recursively process comment tree and remove old comments
   */
  private static async processCommentTree(
    comments: any[],
    cutoffDate: Date,
    debate: any,
    argument: any,
    parentContext: any[] = []
  ): Promise<{ processed: number; reported: number; deleted: number; modified: boolean }> {
    let processed = 0;
    let reported = 0;
    let deleted = 0;
    let modified = false;

    // Process comments in reverse order to safely remove items
    for (let i = comments.length - 1; i >= 0; i--) {
      const comment = comments[i];
      processed++;

      const commentDate = new Date(comment.createdAt);
      const isOld = commentDate < cutoffDate;
      const isReported = comment.reportedBy && comment.reportedBy.length > 0;

      // Build context tree for this comment
      const contextTree = [...parentContext, comment];

      if (isOld) {
        // If comment is reported, save to moderation queue first
        if (isReported) {
          await this.saveToModerationQueue(comment, debate, argument, contextTree);
          reported++;
        }

        // Process replies first (depth-first)
        if (comment.replies && comment.replies.length > 0) {
          const replyResult = await this.processCommentTree(
            comment.replies,
            cutoffDate,
            debate,
            argument,
            contextTree
          );
          processed += replyResult.processed;
          reported += replyResult.reported;
          deleted += replyResult.deleted;
        }

        // Remove the old comment
        comments.splice(i, 1);
        deleted++;
        modified = true;
      } else {
        // Comment is not old, but check its replies
        if (comment.replies && comment.replies.length > 0) {
          const replyResult = await this.processCommentTree(
            comment.replies,
            cutoffDate,
            debate,
            argument,
            contextTree
          );
          processed += replyResult.processed;
          reported += replyResult.reported;
          deleted += replyResult.deleted;
          
          if (replyResult.modified) {
            modified = true;
          }
        }
      }
    }

    return { processed, reported, deleted, modified };
  }

  /**
   * Save reported comment to moderation queue
   */
  private static async saveToModerationQueue(
    comment: any,
    debate: any,
    argument: any,
    contextTree: any[]
  ): Promise<void> {
    try {
      // Create moderation items for each user who reported this comment
      for (const reporterId of comment.reportedBy) {
        const moderationItem = new ModerationItem({
          reportedBy: reporterId,
          reportedAt: new Date(),
          debateId: debate.id,
          debateTitle: debate.title,
          argumentId: argument.id,
          argumentText: argument.text,
          commentId: comment.id,
          commentText: comment.text,
          commentAuthor: comment.author,
          commentCreatedAt: comment.createdAt,
          contextTree: contextTree.map(c => ({
            id: c.id,
            text: c.text,
            author: c.author,
            createdAt: c.createdAt,
            upvotes: c.upvotes || 0,
            downvotes: c.downvotes || 0
          })),
          status: 'pending'
        });

        await moderationItem.save();
      }
    } catch (error) {
      console.error('Error saving to moderation queue:', error);
      // Don't throw - we still want to delete the comment even if moderation save fails
    }
  }

  /**
   * Get cleanup statistics
   */
  static async getCleanupStats(): Promise<{
    totalComments: number;
    oldComments: number;
    reportedComments: number;
  }> {
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - this.MESSAGE_RETENTION_HOURS);

    const debates = await Debate.find({
      'arguments.comments': { $exists: true, $ne: [] }
    });

    let totalComments = 0;
    let oldComments = 0;
    let reportedComments = 0;

    for (const debate of debates) {
      for (const argument of debate.arguments) {
        if (!argument.comments) continue;
        
        const stats = this.countCommentsRecursive(argument.comments, cutoffDate);
        totalComments += stats.total;
        oldComments += stats.old;
        reportedComments += stats.reported;
      }
    }

    return { totalComments, oldComments, reportedComments };
  }

  private static countCommentsRecursive(comments: any[], cutoffDate: Date): {
    total: number;
    old: number;
    reported: number;
  } {
    let total = 0;
    let old = 0;
    let reported = 0;

    for (const comment of comments) {
      total++;
      
      const commentDate = new Date(comment.createdAt);
      if (commentDate < cutoffDate) {
        old++;
      }
      
      if (comment.reportedBy && comment.reportedBy.length > 0) {
        reported++;
      }

      if (comment.replies && comment.replies.length > 0) {
        const replyStats = this.countCommentsRecursive(comment.replies, cutoffDate);
        total += replyStats.total;
        old += replyStats.old;
        reported += replyStats.reported;
      }
    }

    return { total, old, reported };
  }

  /**
   * Find argument by ID recursively
   */
  private static findArgument(args: any[], argumentId: string): any {
    for (const arg of args) {
      if (arg.id === argumentId) return arg;
      if (arg.subArguments) {
        const found = this.findArgument(arg.subArguments, argumentId);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * Count total comments in a tree
   */
  private static countTotalComments(comments: any[]): number {
    let count = 0;
    for (const comment of comments) {
      count++;
      if (comment.replies && comment.replies.length > 0) {
        count += this.countTotalComments(comment.replies);
      }
    }
    return count;
  }

  /**
   * Clean up comments in a specific argument when limit is reached
   */
  private static async cleanupArgumentComments(debate: any, argument: any): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - this.MESSAGE_RETENTION_HOURS);

    const result = await this.processCommentTree(
      argument.comments,
      cutoffDate,
      debate,
      argument
    );

    if (result.modified) {
      await debate.save();
      console.log(`Cleaned up ${result.deleted} old messages, preserved ${result.reported} reported messages`);
    }
  }
}
