import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from './AuthContext';

interface SocketContextType {
  socket: Socket | null;
  isConnected: boolean;
  joinArgumentChat: (debateId: string, argumentId: string) => void;
  leaveArgumentChat: (debateId: string, argumentId: string) => void;
  joinDebate: (debateId: string) => void;
  leaveDebate: (debateId: string) => void;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

interface SocketProviderProps {
  children: ReactNode;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    // Only connect if user is authenticated
    if (user) {
      const token = localStorage.getItem('token');
      const newSocket = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {
        withCredentials: true,
        transports: ['websocket', 'polling'],
        auth: {
          token: token
        }
      });

      newSocket.on('connect', () => {
        console.log('Connected to WebSocket server');
        setIsConnected(true);
      });

      newSocket.on('disconnect', () => {
        console.log('Disconnected from WebSocket server');
        setIsConnected(false);
      });

      newSocket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        setIsConnected(false);
      });

      setSocket(newSocket);

      return () => {
        newSocket.close();
        setSocket(null);
        setIsConnected(false);
      };
    } else {
      // Disconnect if user logs out
      if (socket) {
        socket.close();
        setSocket(null);
        setIsConnected(false);
      }
    }
  }, [user]);

  const joinArgumentChat = (debateId: string, argumentId: string) => {
    if (socket && isConnected) {
      socket.emit('join-argument-chat', { debateId, argumentId });
    }
  };

  const leaveArgumentChat = (debateId: string, argumentId: string) => {
    if (socket && isConnected) {
      socket.emit('leave-argument-chat', { debateId, argumentId });
    }
  };

  const joinDebate = (debateId: string) => {
    if (socket && isConnected) {
      socket.emit('join-debate', debateId);
    }
  };

  const leaveDebate = (debateId: string) => {
    if (socket && isConnected) {
      socket.emit('leave-debate', debateId);
    }
  };

  const value: SocketContextType = {
    socket,
    isConnected,
    joinArgumentChat,
    leaveArgumentChat,
    joinDebate,
    leaveDebate
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};

export const useSocket = (): SocketContextType => {
  const context = useContext(SocketContext);
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};
