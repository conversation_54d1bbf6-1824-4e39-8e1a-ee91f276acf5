import React from 'react';
import { useAuth } from '../../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  fallback 
}) => {
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return (
      <div className="w-full p-4 md:p-8">
        <div className="text-center py-12">
          <div className="bg-brand-surface rounded-lg p-8">
            <div className="text-brand-primary text-4xl mb-4">🔒</div>
            <h2 className="text-2xl font-bold text-white mb-4">Authentication Required</h2>
            <p className="text-brand-text-secondary mb-6">
              You need to be logged in to access this page.
            </p>
            {fallback || (
              <div className="space-x-4">
                <button className="bg-brand-primary hover:bg-brand-primary-dark text-white px-6 py-2 rounded-lg transition-colors">
                  Sign In
                </button>
                <button className="bg-brand-surface-light hover:bg-brand-surface text-white px-6 py-2 rounded-lg transition-colors">
                  Create Account
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};
