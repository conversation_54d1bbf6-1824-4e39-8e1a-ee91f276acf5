import { Document } from 'mongoose';
export interface IParticipant {
    id: string;
    name: string;
    avatarUrl: string;
}
export interface IModerationComment {
    id: string;
    text: string;
    author: IParticipant;
    createdAt: Date;
    upvotes: number;
    downvotes: number;
}
export interface IModerationItem extends Document {
    reportedBy: string;
    reportedAt: Date;
    debateId: string;
    debateTitle: string;
    argumentId: string;
    argumentText: string;
    commentId: string;
    commentText: string;
    commentAuthor: IParticipant;
    commentCreatedAt: Date;
    contextTree: IModerationComment[];
    status: 'pending' | 'reviewed' | 'resolved' | 'dismissed';
    moderatorId?: string;
    moderatorNotes?: string;
    actionTaken?: 'none' | 'warning' | 'comment_removed' | 'user_suspended';
    reviewedAt?: Date;
}
export declare const ModerationItem: import("mongoose").Model<IModerationItem, {}, {}, {}, Document<unknown, {}, IModerationItem, {}, {}> & IModerationItem & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=ModerationQueue.d.ts.map