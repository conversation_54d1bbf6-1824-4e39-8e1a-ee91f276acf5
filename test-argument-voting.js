const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testArgumentVoting() {
  try {
    console.log('🧪 Testing Argument Voting API...\n');

    // First, get all debates to find one with arguments
    console.log('📋 Fetching debates...');
    const debatesResponse = await axios.get(`${BASE_URL}/debates`);
    const debates = debatesResponse.data.data;
    
    if (!debates || debates.length === 0) {
      console.log('❌ No debates found');
      return;
    }

    // Find a debate with arguments
    let testDebate = null;
    let testArgument = null;
    
    for (const debate of debates) {
      if (debate.arguments && debate.arguments.length > 0) {
        testDebate = debate;
        testArgument = debate.arguments[0];
        break;
      }
    }

    if (!testDebate || !testArgument) {
      console.log('❌ No debates with arguments found');
      return;
    }

    console.log(`✅ Found test debate: "${testDebate.title}"`);
    console.log(`✅ Found test argument: "${testArgument.text.substring(0, 50)}..."`);
    console.log(`📊 Current votes - Up: ${testArgument.upvotes || 0}, Down: ${testArgument.downvotes || 0}, Net: ${testArgument.votes || 0}`);

    // Test voting without authentication (should fail)
    console.log('\n🔒 Testing voting without authentication...');
    try {
      await axios.post(`${BASE_URL}/debates/${testDebate.id}/arguments/${testArgument.id}/vote`, {
        vote: 'up'
      });
      console.log('❌ Voting without auth should have failed');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Correctly rejected unauthenticated request');
      } else {
        console.log('❌ Unexpected error:', error.response?.data || error.message);
      }
    }

    // Test with invalid vote type (should fail)
    console.log('\n❌ Testing invalid vote type...');
    try {
      await axios.post(`${BASE_URL}/debates/${testDebate.id}/arguments/${testArgument.id}/vote`, {
        vote: 'invalid'
      }, {
        headers: {
          'Authorization': 'Bearer fake-token'
        }
      });
      console.log('❌ Invalid vote type should have failed');
    } catch (error) {
      if (error.response && (error.response.status === 400 || error.response.status === 401)) {
        console.log('✅ Correctly rejected invalid vote type or auth');
      } else {
        console.log('❌ Unexpected error:', error.response?.data || error.message);
      }
    }

    // Test with missing vote parameter (should fail)
    console.log('\n❌ Testing missing vote parameter...');
    try {
      await axios.post(`${BASE_URL}/debates/${testDebate.id}/arguments/${testArgument.id}/vote`, {
        // No vote parameter
      }, {
        headers: {
          'Authorization': 'Bearer fake-token'
        }
      });
      console.log('❌ Missing vote parameter should have failed');
    } catch (error) {
      if (error.response && (error.response.status === 400 || error.response.status === 401)) {
        console.log('✅ Correctly rejected missing vote parameter or auth');
      } else {
        console.log('❌ Unexpected error:', error.response?.data || error.message);
      }
    }

    console.log('\n✅ Argument voting API validation tests completed!');
    console.log('\n📝 Summary:');
    console.log('- Authentication is required ✅');
    console.log('- Vote parameter validation is working ✅');
    console.log('- Invalid vote types are rejected ✅');
    console.log('\n💡 To test actual voting, you need to:');
    console.log('1. Log in through the frontend');
    console.log('2. Use the browser dev tools to see the actual API calls');
    console.log('3. Check if the vote parameter is being sent correctly');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testArgumentVoting();
