import React, { useState, useEffect } from 'react';
import { Debate } from '../../types';
import { DebateCard } from './DebateCard';

interface FeaturedDebatesCarouselProps {
  debates: Debate[];
  onSelect: (id: string) => void;
}

export const FeaturedDebatesCarousel: React.FC<FeaturedDebatesCarouselProps> = ({ 
  debates, 
  onSelect 
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Get featured debates (top 5 by engagement)
  const featuredDebates = debates
    .slice()
    .sort((a, b) => {
      // Calculate engagement score: total votes + arguments count
      const aEngagement = ((a.supportVotes || 0) + (a.opposeVotes || 0)) + (a.arguments?.length || 0);
      const bEngagement = ((b.supportVotes || 0) + (b.opposeVotes || 0)) + (b.arguments?.length || 0);
      return bEngagement - aEngagement;
    })
    .slice(0, 5);

  // Auto-advance carousel
  useEffect(() => {
    if (!isAutoPlaying || featuredDebates.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % featuredDebates.length);
    }, 5000); // Change every 5 seconds

    return () => clearInterval(interval);
  }, [isAutoPlaying, featuredDebates.length]);

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
    // Resume auto-play after 10 seconds
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  const goToPrevious = () => {
    setCurrentIndex((prev) => (prev - 1 + featuredDebates.length) % featuredDebates.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  const goToNext = () => {
    setCurrentIndex((prev) => (prev + 1) % featuredDebates.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  if (featuredDebates.length === 0) {
    return null;
  }

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold text-white flex items-center gap-2">
          <span className="text-yellow-400">⭐</span>
          Featured Debates
        </h2>
        <div className="flex items-center gap-2 text-sm text-brand-text-secondary">
          <span className={`w-2 h-2 rounded-full ${isAutoPlaying ? 'bg-green-400' : 'bg-gray-400'}`}></span>
          {isAutoPlaying ? 'Auto-playing' : 'Paused'}
        </div>
      </div>

      <div className="relative">
        {/* Main carousel container */}
        <div className="overflow-hidden rounded-xl">
          <div 
            className="flex transition-transform duration-500 ease-in-out"
            style={{ transform: `translateX(-${currentIndex * 100}%)` }}
          >
            {featuredDebates.map((debate, index) => (
              <div key={debate.id} className="w-full flex-shrink-0">
                <div className="relative">
                  <DebateCard debate={debate} onSelect={onSelect} />
                  {/* Featured badge */}
                  <div className="absolute top-4 right-4 bg-yellow-500 text-black px-3 py-1 rounded-full text-sm font-bold flex items-center gap-1">
                    <span>⭐</span>
                    Featured
                  </div>
                  {/* Engagement stats */}
                  <div className="absolute bottom-4 left-4 bg-black/70 text-white px-3 py-1 rounded-lg text-sm">
                    {((debate.supportVotes || 0) + (debate.opposeVotes || 0))} votes • {debate.arguments?.length || 0} arguments
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation arrows */}
        {featuredDebates.length > 1 && (
          <>
            <button
              onClick={goToPrevious}
              className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors z-10"
              aria-label="Previous debate"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button
              onClick={goToNext}
              className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors z-10"
              aria-label="Next debate"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </>
        )}

        {/* Dots indicator */}
        {featuredDebates.length > 1 && (
          <div className="flex justify-center mt-4 gap-2">
            {featuredDebates.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === currentIndex 
                    ? 'bg-brand-primary' 
                    : 'bg-gray-600 hover:bg-gray-500'
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
