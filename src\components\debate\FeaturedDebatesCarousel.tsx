import React from 'react';
import { Debate } from '../../types';

interface FeaturedDebatesCarouselProps {
  debates: Debate[];
  onSelect: (id: string) => void;
}

interface FeaturedDebateCardProps {
  debate: Debate;
  onSelect: (id: string) => void;
  rank: number;
}

const FeaturedDebateCard: React.FC<FeaturedDebateCardProps> = ({ debate, onSelect, rank }) => {
  const totalVotes = (debate.supportVotes || 0) + (debate.opposeVotes || 0);
  const argumentsCount = debate.arguments?.length || 0;
  const engagementScore = totalVotes + argumentsCount;

  return (
    <div
      className="group relative bg-brand-surface rounded-xl overflow-hidden cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-2xl"
      onClick={() => onSelect(debate.id)}
    >
      {/* Rank badge */}
      <div className="absolute top-4 left-4 z-20">
        <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-3 py-1 rounded-full text-sm font-bold flex items-center gap-1">
          <span>#{rank}</span>
          <span>⭐</span>
        </div>
      </div>

      {/* Category badge */}
      <div className="absolute top-4 right-4 z-20">
        <div className="bg-brand-primary/80 text-white px-3 py-1 rounded-full text-xs font-medium capitalize">
          {debate.category}
        </div>
      </div>

      {/* Main content */}
      <div className="p-6 h-full flex flex-col">
        <div className="flex-1">
          <h3 className="text-xl font-bold text-white mb-3 line-clamp-2 group-hover:text-brand-primary transition-colors">
            {debate.title}
          </h3>

          <p className="text-brand-text-secondary text-sm mb-4 line-clamp-3">
            {debate.description}
          </p>

          <div className="flex items-center gap-4 text-sm text-brand-text-light mb-4">
            <div className="flex items-center gap-1">
              <span className="text-green-400">👍</span>
              <span>{debate.supportVotes || 0}</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-red-400">👎</span>
              <span>{debate.opposeVotes || 0}</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-blue-400">💬</span>
              <span>{argumentsCount}</span>
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="border-t border-brand-surface-light pt-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <img
                src={debate.proponent.avatarUrl}
                alt={debate.proponent.name}
                className="w-8 h-8 rounded-full"
              />
              <span className="text-sm text-brand-text-light">{debate.proponent.name}</span>
            </div>

            <div className="text-xs text-brand-text-secondary">
              {engagementScore} engagement
            </div>
          </div>
        </div>
      </div>

      {/* Hover overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
    </div>
  );
};

export const FeaturedDebatesCarousel: React.FC<FeaturedDebatesCarouselProps> = ({
  debates,
  onSelect
}) => {
  // Get featured debates (top 3 by engagement)
  const featuredDebates = debates
    .slice()
    .sort((a, b) => {
      // Calculate engagement score: total votes + arguments count
      const aEngagement = ((a.supportVotes || 0) + (a.opposeVotes || 0)) + (a.arguments?.length || 0);
      const bEngagement = ((b.supportVotes || 0) + (b.opposeVotes || 0)) + (b.arguments?.length || 0);
      return bEngagement - aEngagement;
    })
    .slice(0, 3);

  if (featuredDebates.length === 0) {
    return null;
  }

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-3xl font-bold text-white flex items-center gap-3">
          <span className="text-yellow-400 text-4xl">⭐</span>
          Featured Debates
        </h2>
        <div className="text-sm text-brand-text-secondary">
          Top {featuredDebates.length} most engaging debates
        </div>
      </div>

      {/* Grid layout - 3 debates side by side like Twitch */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {featuredDebates.map((debate, index) => (
          <FeaturedDebateCard
            key={debate.id}
            debate={debate}
            onSelect={onSelect}
            rank={index + 1}
          />
        ))}
      </div>

      {/* Show message if less than 3 debates */}
      {featuredDebates.length < 3 && (
        <div className="mt-6 text-center">
          <p className="text-brand-text-secondary text-sm">
            {featuredDebates.length === 0
              ? "No debates available yet. Be the first to start a debate!"
              : `Only ${featuredDebates.length} debate${featuredDebates.length === 1 ? '' : 's'} available. Create more debates to fill the featured section!`
            }
          </p>
        </div>
      )}
    </div>
  );
};
