const mongoose = require('mongoose');
const User = require('../dist/models/User').default;

// Load environment variables
require('dotenv').config();

async function makeUserAdmin() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/agora-debate');
    console.log('Connected to MongoDB');

    // Get username from command line arguments
    const username = process.argv[2];
    
    if (!username) {
      console.log('Usage: node makeAdmin.js <username>');
      console.log('Example: node makeAdmin.js johndoe');
      process.exit(1);
    }

    // Find user by username
    const user = await User.findOne({ username: username });
    
    if (!user) {
      console.log(`User with username "${username}" not found.`);
      
      // List all users to help
      const allUsers = await User.find({}).select('username email role');
      console.log('\nAvailable users:');
      allUsers.forEach(u => {
        console.log(`- ${u.username} (${u.email}) - Role: ${u.role}`);
      });
      
      process.exit(1);
    }

    // Update user role to admin
    user.role = 'admin';
    await user.save();

    console.log(`✅ Successfully made "${username}" an admin!`);
    console.log(`User details:`);
    console.log(`- Username: ${user.username}`);
    console.log(`- Email: ${user.email}`);
    console.log(`- Role: ${user.role}`);
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

makeUserAdmin();
