import express from 'express';
import { body, param } from 'express-validator';
import {
  initializeVoiceDebate,
  joinVoiceQueue,
  leaveVoiceQueue,
  nextSpeaker,
  allocateTime,
  giveTime,
  getTimeBlocks,
  grantTimeBlocks,
  getVoiceDebateState,
  updateVoiceSettings,
  reportMutualExclusion,
  releaseMutualExclusion
} from '../controllers/voiceDebateController';
import { auth } from '../middleware/auth';
import { validate } from '../middleware/validate';

const router = express.Router();

// GET /api/debates/:id/voice - Get voice debate state
router.get('/:id/voice', [
  auth,
  param('id').isMongoId(),
  validate
], getVoiceDebateState);

// POST /api/debates/:id/voice/initialize - Initialize voice debate
router.post('/:id/voice/initialize', [
  auth,
  param('id').isMongoId(),
  body('settings').optional().isObject(),
  body('settings.defaultSpeakingTime').optional().isInt({ min: 30, max: 600 }),
  body('settings.maxSpeakersPerSide').optional().isInt({ min: 1, max: 20 }),
  body('settings.allowSpectatorVoice').optional().isBoolean(),
  body('settings.autoRotateSpeakers').optional().isBoolean(),
  body('settings.mutualExclusionEnabled').optional().isBoolean(),
  validate
], initializeVoiceDebate);

// POST /api/debates/:id/voice/join - Join voice queue
router.post('/:id/voice/join', [
  auth,
  param('id').isMongoId(),
  body('side').isIn(['FOR', 'AGAINST']),
  body('timeAllocation').optional().isInt({ min: 30, max: 600 }),
  validate
], joinVoiceQueue);

// POST /api/debates/:id/voice/leave - Leave voice queue
router.post('/:id/voice/leave', [
  auth,
  param('id').isMongoId(),
  validate
], leaveVoiceQueue);

// POST /api/debates/:id/voice/next-speaker - Rotate to next speaker
router.post('/:id/voice/next-speaker', [
  auth,
  param('id').isMongoId(),
  validate
], nextSpeaker);

// POST /api/debates/:id/voice/give-time - Use time block to give time to speaker
router.post('/:id/voice/give-time', [
  auth,
  param('id').isMongoId(),
  body('speakerId').isString().notEmpty(),
  validate
], giveTime);

// GET /api/debates/:id/voice/time-blocks - Get user's time block balance
router.get('/:id/voice/time-blocks', [
  auth,
  param('id').isMongoId(),
  validate
], getTimeBlocks);

// POST /api/debates/:id/voice/grant-time-blocks - Grant time blocks to user (admin only)
router.post('/:id/voice/grant-time-blocks', [
  auth,
  param('id').isMongoId(),
  body('userId').isString().notEmpty(),
  body('timeBlocks').isInt({ min: 1, max: 100 }),
  validate
], grantTimeBlocks);

// POST /api/debates/:id/voice/allocate-time - Allocate additional time (moderators only)
router.post('/:id/voice/allocate-time', [
  auth,
  param('id').isMongoId(),
  body('speakerId').isString().notEmpty(),
  body('additionalTime').isInt({ min: 1, max: 300 }),
  validate
], allocateTime);

// PUT /api/debates/:id/voice/settings - Update voice settings
router.put('/:id/voice/settings', [
  auth,
  param('id').isMongoId(),
  body('settings').isObject(),
  body('settings.defaultSpeakingTime').optional().isInt({ min: 30, max: 600 }),
  body('settings.maxSpeakersPerSide').optional().isInt({ min: 1, max: 20 }),
  body('settings.allowSpectatorVoice').optional().isBoolean(),
  body('settings.autoRotateSpeakers').optional().isBoolean(),
  body('settings.mutualExclusionEnabled').optional().isBoolean(),
  validate
], updateVoiceSettings);

// POST /api/debates/:id/voice/mutual-exclusion - Report mutual exclusion
router.post('/:id/voice/mutual-exclusion', [
  auth,
  param('id').isMongoId(),
  validate
], reportMutualExclusion);

// POST /api/debates/:id/voice/mutual-exclusion/release - Release mutual exclusion
router.post('/:id/voice/mutual-exclusion/release', [
  auth,
  param('id').isMongoId(),
  validate
], releaseMutualExclusion);

export default router;
