import axios from 'axios';
import { AuthResponse, LoginCredentials, RegisterCredentials, User } from '../types';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://localhost:5000/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests if available
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle token expiration
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      console.error('Authentication failed:', error.config?.url);
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      // Only reload if it's not an evidence voting request
      if (!error.config?.url?.includes('/evidence/') || !error.config?.url?.includes('/vote')) {
        // Redirect to login or refresh the page
        window.location.reload();
      }
    }
    return Promise.reject(error);
  }
);

// Auth API functions
export const authAPI = {
  register: async (credentials: RegisterCredentials): Promise<AuthResponse> => {
    const response = await api.post('/auth/register', credentials);
    return response.data;
  },

  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },

  getProfile: async (): Promise<{ success: boolean; data: { user: User } }> => {
    const response = await api.get('/auth/profile');
    return response.data;
  },

  updateProfile: async (updates: Partial<User>): Promise<{ success: boolean; data: { user: User } }> => {
    const response = await api.put('/auth/profile', updates);
    return response.data;
  },
};

// Users API functions
export const usersAPI = {
  getUsers: async (params?: { page?: number; limit?: number; search?: string }) => {
    const response = await api.get('/users', { params });
    return response.data;
  },

  getUserById: async (id: string) => {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },

  getUserDebates: async (id: string) => {
    const response = await api.get(`/users/${id}/debates`);
    return response.data;
  },
};

// Debates API functions (to be implemented)
export const debatesAPI = {
  getDebates: async () => {
    const response = await api.get('/debates');
    return response.data;
  },

  getDebateById: async (id: string) => {
    const response = await api.get(`/debates/${id}`);
    return response.data;
  },

  createDebate: async (debate: any) => {
    const response = await api.post('/debates', debate);
    return response.data;
  },

  joinDebate: async (id: string) => {
    const response = await api.post(`/debates/${id}/join`);
    return response.data;
  },

  voteOnDebate: async (id: string, position: string) => {
    const response = await api.post(`/debates/${id}/vote`, { position });
    return response.data;
  },

  addArgument: async (debateId: string, argument: any) => {
    const response = await api.post(`/debates/${debateId}/arguments`, argument);
    return response.data;
  },

  voteOnArgument: async (debateId: string, argumentId: string, vote: 'up' | 'down') => {
    const response = await api.post(`/debates/${debateId}/arguments/${argumentId}/vote`, { vote });
    return response.data;
  },

  addSubArgument: async (debateId: string, parentArgumentId: string, subArgument: any) => {
    const response = await api.post(`/debates/${debateId}/arguments/${parentArgumentId}/sub-arguments`, subArgument);
    return response.data;
  },

  addEvidence: async (debateId: string, argumentId: string, evidence: any) => {
    const response = await api.post(`/debates/${debateId}/arguments/${argumentId}/evidence`, evidence);
    return response.data;
  },

  voteOnEvidence: async (debateId: string, argumentId: string, evidenceId: string, vote: 'accurate' | 'inaccurate') => {
    const response = await api.post(`/debates/${debateId}/arguments/${argumentId}/evidence/${evidenceId}/vote`, { vote });
    return response.data;
  },

  // Report content
  reportContent: async (contentId: string, contentType: 'argument' | 'evidence' | 'debate', reason: string) => {
    const response = await api.post('/debates/report', {
      contentId,
      contentType,
      reason
    });
    return response.data;
  },

  // Get content reports (moderators only)
  getContentReports: async () => {
    const response = await api.get('/debates/reports');
    return response.data;
  },

  // Add comment to argument
  addComment: async (debateId: string, argumentId: string, comment: { text: string; parentCommentId?: string }) => {
    const response = await api.post(`/debates/${debateId}/arguments/${argumentId}/comments`, comment);
    return response.data;
  },

  // Vote on comment
  voteOnComment: async (debateId: string, argumentId: string, commentId: string, vote: 'up' | 'down') => {
    const response = await api.post(`/debates/${debateId}/arguments/${argumentId}/comments/${commentId}/vote`, { vote });
    return response.data;
  },

  // Report comment
  reportComment: async (debateId: string, argumentId: string, commentId: string) => {
    const response = await api.post(`/debates/${debateId}/arguments/${argumentId}/comments/${commentId}/report`);
    return response.data;
  },

  // Voice debate methods
  initializeVoiceDebate: async (debateId: string, settings?: any) => {
    const response = await api.post(`/debates/${debateId}/voice/initialize`, { settings });
    return response.data;
  },

  joinVoiceQueue: async (debateId: string, side: 'FOR' | 'AGAINST', timeAllocation?: number) => {
    const response = await api.post(`/debates/${debateId}/voice/join`, { side, timeAllocation });
    return response.data;
  },

  leaveVoiceQueue: async (debateId: string) => {
    const response = await api.post(`/debates/${debateId}/voice/leave`);
    return response.data;
  },

  nextSpeaker: async (debateId: string) => {
    const response = await api.post(`/debates/${debateId}/voice/next-speaker`);
    return response.data;
  },

  giveTime: async (debateId: string, speakerId: string) => {
    const response = await api.post(`/debates/${debateId}/voice/give-time`, {
      speakerId
    });
    return response.data;
  },

  getTimeBlocks: async (debateId: string) => {
    const response = await api.get(`/debates/${debateId}/voice/time-blocks`);
    return response.data;
  },

  grantTimeBlocks: async (debateId: string, userId: string, timeBlocks: number) => {
    const response = await api.post(`/debates/${debateId}/voice/grant-time-blocks`, {
      userId,
      timeBlocks
    });
    return response.data;
  },

  allocateTime: async (debateId: string, speakerId: string, additionalTime: number) => {
    const response = await api.post(`/debates/${debateId}/voice/allocate-time`, { speakerId, additionalTime });
    return response.data;
  },

  getVoiceDebateState: async (debateId: string) => {
    const response = await api.get(`/debates/${debateId}/voice`);
    return response.data;
  },

  updateVoiceSettings: async (debateId: string, settings: any) => {
    const response = await api.put(`/debates/${debateId}/voice/settings`, { settings });
    return response.data;
  },

  reportMutualExclusion: async (debateId: string, speakingParticipants: string[]) => {
    const response = await api.post(`/debates/${debateId}/voice/mutual-exclusion`, { speakingParticipants });
    return response.data;
  },
};

export default api;
