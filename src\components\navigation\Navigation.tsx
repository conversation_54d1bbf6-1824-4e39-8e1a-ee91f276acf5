import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { getDisplayName } from '../../utils/userDisplay';
import { UserMenu } from '../auth/UserMenu';

interface NavigationProps {
  currentView: string;
  onNavigate: (page: 'debates' | 'profile' | 'dashboard' | 'create-debate' | 'admin') => void;
  onAuthModalOpen: (mode: 'login' | 'register') => void;
}

export const Navigation: React.FC<NavigationProps> = ({ 
  currentView, 
  onNavigate, 
  onAuthModalOpen 
}) => {
  const { isAuthenticated, user } = useAuth();

  return (
    <nav className="bg-brand-surface border-b border-brand-surface-light">
      <div className="w-full px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo and main navigation */}
          <div className="flex items-center space-x-8">
            <button
              onClick={() => onNavigate('debates')}
              className="text-2xl font-bold text-white tracking-tight hover:text-brand-primary transition-colors"
            >
              <span className="text-brand-primary">Agora:</span> The Debate Platform
            </button>
            
            {/* Main navigation links */}
            <div className="hidden md:flex items-center space-x-6">
              <button
                onClick={() => onNavigate('debates')}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  currentView === 'debates' || currentView === 'debate'
                    ? 'text-brand-primary bg-brand-primary/10'
                    : 'text-brand-text-secondary hover:text-white'
                }`}
              >
                Debates
              </button>
              
              {isAuthenticated && (
                <>
                  <button
                    onClick={() => onNavigate('dashboard')}
                    className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      currentView === 'dashboard'
                        ? 'text-brand-primary bg-brand-primary/10'
                        : 'text-brand-text-secondary hover:text-white'
                    }`}
                  >
                    Dashboard
                  </button>

                  {user?.role === 'admin' && (
                    <button
                      onClick={() => onNavigate('admin')}
                      className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                        currentView === 'admin'
                          ? 'text-brand-primary bg-brand-primary/10'
                          : 'text-brand-text-secondary hover:text-white'
                      }`}
                    >
                      Admin
                    </button>
                  )}

                  <button
                    onClick={() => onNavigate('create-debate')}
                    className="bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                  >
                    Create Debate
                  </button>
                </>
              )}
            </div>
          </div>

          {/* User menu or auth buttons */}
          <div className="flex items-center space-x-4">
            {isAuthenticated && user ? (
              <UserMenu onNavigate={onNavigate} />
            ) : (
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => onAuthModalOpen('login')}
                  className="text-brand-text-secondary hover:text-white transition-colors text-sm font-medium"
                >
                  Sign In
                </button>
                <button
                  onClick={() => onAuthModalOpen('register')}
                  className="bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                >
                  Sign Up
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Mobile navigation */}
      {isAuthenticated && (
        <div className="md:hidden border-t border-brand-surface-light">
          <div className="px-4 py-3 space-y-1">
            <button
              onClick={() => onNavigate('debates')}
              className={`block w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                currentView === 'debates' || currentView === 'debate'
                  ? 'text-brand-primary bg-brand-primary/10'
                  : 'text-brand-text-secondary hover:text-white'
              }`}
            >
              Debates
            </button>
            <button
              onClick={() => onNavigate('dashboard')}
              className={`block w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                currentView === 'dashboard'
                  ? 'text-brand-primary bg-brand-primary/10'
                  : 'text-brand-text-secondary hover:text-white'
              }`}
            >
              Dashboard
            </button>
            <button
              onClick={() => onNavigate('profile')}
              className={`block w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                currentView === 'profile'
                  ? 'text-brand-primary bg-brand-primary/10'
                  : 'text-brand-text-secondary hover:text-white'
              }`}
            >
              Profile
            </button>
            <button
              onClick={() => onNavigate('create-debate')}
              className="block w-full text-left px-3 py-2 rounded-md text-sm font-medium bg-brand-primary hover:bg-brand-primary-dark text-white transition-colors"
            >
              Create Debate
            </button>
          </div>
        </div>
      )}
    </nav>
  );
};
