{"name": "agora-debate-platform", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "build": "vite build", "preview": "vite preview", "dev:frontend": "vite --host 0.0.0.0", "build:frontend": "vite build", "dev:backend": "cd backend && npm run dev", "build:backend": "cd backend && npm run build", "start:backend": "cd backend && npm start", "install:backend": "cd backend && npm install", "install:all": "npm install && npm run install:backend", "dev:all": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "build:all": "npm run build:frontend && npm run build:backend", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix"}, "dependencies": {"@types/react-router-dom": "^5.3.3", "@types/uuid": "^10.0.0", "axios": "^1.11.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.7.1", "socket.io-client": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "~5.8.2", "vite": "^6.2.0"}}