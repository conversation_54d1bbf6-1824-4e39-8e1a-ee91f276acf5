export declare class MessageCleanupService {
    private static readonly MESSAGE_RETENTION_HOURS;
    private static readonly MAX_COMMENTS_PER_ARGUMENT;
    static cleanupIfNeeded(debateId: string, argumentId: string): Promise<void>;
    static cleanupOldMessages(): Promise<void>;
    private static processDebateComments;
    private static processCommentTree;
    private static saveToModerationQueue;
    static getCleanupStats(): Promise<{
        totalComments: number;
        oldComments: number;
        reportedComments: number;
    }>;
    private static countCommentsRecursive;
    private static findArgument;
    private static countTotalComments;
    private static cleanupArgumentComments;
}
//# sourceMappingURL=messageCleanupService.d.ts.map