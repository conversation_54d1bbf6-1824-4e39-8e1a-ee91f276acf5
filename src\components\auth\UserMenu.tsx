import React, { useState, useRef, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { getDisplayName } from '../../utils/userDisplay';

interface UserMenuProps {
  onNavigate?: (page: 'profile' | 'dashboard' | 'debates' | 'admin') => void;
}

export const UserMenu: React.FC<UserMenuProps> = ({ onNavigate }) => {
  const { user, logout } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  if (!user) return null;

  const handleNavigation = (page: 'profile' | 'dashboard' | 'debates' | 'admin') => {
    setIsOpen(false);
    onNavigate?.(page);
  };

  const handleLogout = () => {
    setIsOpen(false);
    logout();
  };

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 text-white hover:text-brand-primary transition-colors"
      >
        <span className="hidden md:block flex-1 text-right">{getDisplayName(user)}</span>
        <svg
          className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
        <img
          src={user.avatarUrl || `https://picsum.photos/seed/${user.username}/40`}
          alt={getDisplayName(user)}
          className="w-8 h-8 rounded-full"
        />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-brand-surface border border-brand-surface-light rounded-lg shadow-lg py-1 z-50">
          <div className="px-4 py-2 border-b border-brand-surface-light">
            <p className="text-sm font-medium text-white">{getDisplayName(user)}</p>
            <p className="text-xs text-brand-text-light">@{user.username}</p>
            <p className="text-xs text-brand-text-light">Reputation: {user.reputation}</p>
          </div>
          
          <button
            onClick={() => handleNavigation('dashboard')}
            className="w-full text-left px-4 py-2 text-sm text-brand-text hover:bg-brand-surface-light transition-colors"
          >
            Dashboard
          </button>

          <button
            onClick={() => handleNavigation('profile')}
            className="w-full text-left px-4 py-2 text-sm text-brand-text hover:bg-brand-surface-light transition-colors"
          >
            Profile
          </button>

          {user.role === 'admin' && (
            <button
              onClick={() => handleNavigation('admin')}
              className="w-full text-left px-4 py-2 text-sm text-purple-400 hover:bg-brand-surface-light transition-colors"
            >
              Admin Dashboard
            </button>
          )}

          <button
            onClick={() => handleNavigation('debates')}
            className="w-full text-left px-4 py-2 text-sm text-brand-text hover:bg-brand-surface-light transition-colors"
          >
            Browse Debates
          </button>

          <button
            onClick={handleLogout}
            className="w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-brand-surface-light transition-colors"
          >
            Sign Out
          </button>
        </div>
      )}
    </div>
  );
};
