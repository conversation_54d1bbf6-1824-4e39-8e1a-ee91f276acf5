import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { AdminStats } from '../../types';
import { adminAPI } from '../../utils/adminApi';
import { AdminStatsOverview } from './AdminStatsOverview';
import { UserManagement } from './UserManagement';
import { ModerationQueue } from './ModerationQueue';

type AdminView = 'overview' | 'users' | 'moderation' | 'settings';

export const AdminDashboard: React.FC = () => {
  const { user } = useAuth();
  const [currentView, setCurrentView] = useState<AdminView>('overview');
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await adminAPI.getStats();
      if (response.success) {
        setStats(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load admin stats');
    } finally {
      setLoading(false);
    }
  };

  // Check if user has admin access
  if (user?.role !== 'admin') {
    return (
      <div className="min-h-screen bg-brand-bg flex items-center justify-center">
        <div className="bg-brand-surface rounded-lg p-8 text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
          <p className="text-brand-text-secondary">You don't have permission to access the admin dashboard.</p>
        </div>
      </div>
    );
  }

  const navigationItems = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'users', label: 'User Management', icon: '👥' },
    { id: 'moderation', label: 'Moderation Queue', icon: '🛡️' },
    { id: 'settings', label: 'Settings', icon: '⚙️' }
  ];

  return (
    <div className="min-h-screen bg-brand-bg">
      <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Admin Dashboard</h1>
          <p className="text-brand-text-secondary">Manage users, moderate content, and monitor system health</p>
        </div>

        {/* Navigation */}
        <div className="bg-brand-surface rounded-lg p-6 mb-8">
          <div className="flex flex-wrap gap-4">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setCurrentView(item.id as AdminView)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  currentView === item.id
                    ? 'bg-brand-primary text-white'
                    : 'bg-brand-surface-light hover:bg-brand-surface text-brand-text-secondary hover:text-white'
                }`}
              >
                <span>{item.icon}</span>
                <span>{item.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="space-y-8">
          {loading && (
            <div className="bg-brand-surface rounded-lg p-8 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-primary mx-auto mb-4"></div>
              <p className="text-brand-text-secondary">Loading admin data...</p>
            </div>
          )}

          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
              <p className="text-red-400">{error}</p>
              <button
                onClick={loadStats}
                className="mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Retry
              </button>
            </div>
          )}

          {!loading && !error && (
            <>
              {currentView === 'overview' && stats && (
                <AdminStatsOverview stats={stats} onRefresh={loadStats} />
              )}

              {currentView === 'users' && (
                <UserManagement />
              )}

              {currentView === 'moderation' && (
                <ModerationQueue />
              )}

              {currentView === 'settings' && (
                <div className="bg-brand-surface rounded-lg p-8 text-center">
                  <h2 className="text-xl font-bold text-white mb-4">Settings</h2>
                  <p className="text-brand-text-secondary">Settings panel coming soon...</p>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};
