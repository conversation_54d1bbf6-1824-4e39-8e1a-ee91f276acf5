export interface Category {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
}

export const DEBATE_CATEGORIES: Category[] = [
  {
    id: 'politics',
    name: 'Politics',
    description: 'Political discussions and policy debates',
    icon: '🏛️',
    color: 'bg-blue-600'
  },
  {
    id: 'technology',
    name: 'Technology',
    description: 'Tech trends, AI, and digital innovation',
    icon: '💻',
    color: 'bg-purple-600'
  },
  {
    id: 'science',
    name: 'Science',
    description: 'Scientific theories and discoveries',
    icon: '🔬',
    color: 'bg-green-600'
  },
  {
    id: 'philosophy',
    name: 'Philosophy',
    description: 'Philosophical questions and ethics',
    icon: '🤔',
    color: 'bg-indigo-600'
  },
  {
    id: 'economics',
    name: 'Economics',
    description: 'Economic policies and market discussions',
    icon: '💰',
    color: 'bg-yellow-600'
  },
  {
    id: 'environment',
    name: 'Environment',
    description: 'Climate change and environmental issues',
    icon: '🌍',
    color: 'bg-emerald-600'
  },
  {
    id: 'health',
    name: 'Health',
    description: 'Healthcare, medicine, and wellness',
    icon: '🏥',
    color: 'bg-red-600'
  },
  {
    id: 'education',
    name: 'Education',
    description: 'Educational systems and learning',
    icon: '📚',
    color: 'bg-orange-600'
  },
  {
    id: 'sports',
    name: 'Sports',
    description: 'Sports debates and athletic discussions',
    icon: '⚽',
    color: 'bg-teal-600'
  },
  {
    id: 'entertainment',
    name: 'Entertainment',
    description: 'Movies, music, and pop culture',
    icon: '🎬',
    color: 'bg-pink-600'
  }
];

export const CATEGORY_IDS = DEBATE_CATEGORIES.map(category => category.id);

export const getCategoryById = (id: string): Category | undefined => {
  return DEBATE_CATEGORIES.find(category => category.id === id);
};

export const getCategoryName = (id: string): string => {
  const category = getCategoryById(id);
  return category ? category.name : 'Unknown';
};
