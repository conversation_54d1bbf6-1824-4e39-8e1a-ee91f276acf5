import { Router } from 'express';
import { param, body, query } from 'express-validator';
import { auth, requireRole } from '../middleware/auth';
import { validate } from '../middleware/validate';
import {
  getUsers,
  getUserDetails,
  updateUserRole,
  suspendUser,
  getAdminStats
} from '../controllers/adminController';

const router = Router();

// All admin routes require authentication and admin role
router.use(auth);
router.use(requireRole(['admin']));

// GET /api/admin/stats - Get admin dashboard statistics
router.get('/stats', getAdminStats);

// GET /api/admin/users - Get all users with pagination and filtering
router.get('/users', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('role').optional().isIn(['all', 'user', 'moderator', 'admin']),
  query('search').optional().isString().isLength({ max: 100 }),
  query('sortBy').optional().isIn(['createdAt', 'username', 'email', 'role', 'reputation']),
  query('sortOrder').optional().isIn(['asc', 'desc']),
  validate
], getUsers);

// GET /api/admin/users/:id - Get user details
router.get('/users/:id', [
  param('id').isMongoId(),
  validate
], getUserDetails);

// PUT /api/admin/users/:id/role - Update user role
router.put('/users/:id/role', [
  param('id').isMongoId(),
  body('role').isIn(['user', 'moderator', 'admin']),
  validate
], updateUserRole);

// PUT /api/admin/users/:id/suspend - Suspend/unsuspend user
router.put('/users/:id/suspend', [
  param('id').isMongoId(),
  body('suspended').isBoolean(),
  body('reason').optional().isString().isLength({ min: 1, max: 500 }),
  validate
], suspendUser);

export default router;
