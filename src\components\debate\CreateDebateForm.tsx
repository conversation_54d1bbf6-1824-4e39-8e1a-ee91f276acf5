import React, { useState, useEffect } from 'react';
import { debatesAPI, usersAPI } from '../../utils/api';
import { useAuth } from '../../contexts/AuthContext';
import { ValidationSummary, QualityGuidelines, useContentValidation } from '../ui/ValidationFeedback';
import { User, Reference } from '../../types';
import { PlusIcon, XIcon, LinkIcon } from '../ui/icons';
import { DEBATE_CATEGORIES } from '../../constants/categories';

interface CreateDebateFormProps {
  onSuccess: (debateId: string) => void;
  onCancel: () => void;
}

export const CreateDebateForm: React.FC<CreateDebateFormProps> = ({ onSuccess, onCancel }) => {
  const { user } = useAuth();
  const { validateArgument } = useContentValidation();
  
  // Form state
  const [formData, setFormData] = useState({
    title: '',
    category: '',
    type: 'open' as 'one-on-one' | 'open',
    claim: {
      text: '',
      references: [] as Reference[]
    },
    opponent: {
      id: '',
      name: '',
      avatarUrl: ''
    }
  });

  // UI state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showOpponentSearch, setShowOpponentSearch] = useState(false);
  const [opponentSearchQuery, setOpponentSearchQuery] = useState('');
  const [availableOpponents, setAvailableOpponents] = useState<User[]>([]);
  const [isLoadingOpponents, setIsLoadingOpponents] = useState(false);
  const [showAddReference, setShowAddReference] = useState(false);
  const [newReference, setNewReference] = useState({
    url: '',
    title: '',
    description: '',
    type: 'other' as Reference['type']
  });

  // Search for potential opponents
  const searchOpponents = async (query: string) => {
    if (query.length < 2) {
      setAvailableOpponents([]);
      return;
    }

    setIsLoadingOpponents(true);
    try {
      const response = await usersAPI.getUsers({ search: query, limit: 10 });
      if (response.success) {
        // Filter out current user
        const opponents = response.data.filter((u: User) => u.id !== user?.id);
        setAvailableOpponents(opponents);
      }
    } catch (error) {
      console.error('Error searching opponents:', error);
    } finally {
      setIsLoadingOpponents(false);
    }
  };

  // Debounced opponent search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (opponentSearchQuery) {
        searchOpponents(opponentSearchQuery);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [opponentSearchQuery]);

  const handleInputChange = (field: string, value: string) => {
    if (field.startsWith('claim.')) {
      const claimField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        claim: {
          ...prev.claim,
          [claimField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const selectOpponent = (opponent: User) => {
    setFormData(prev => ({
      ...prev,
      opponent: {
        id: opponent.id,
        name: opponent.name,
        avatarUrl: opponent.avatarUrl || `https://picsum.photos/seed/${opponent.username}/40`
      }
    }));
    setShowOpponentSearch(false);
    setOpponentSearchQuery('');
    setAvailableOpponents([]);
  };

  const addReference = () => {
    if (!newReference.url || !newReference.title) return;

    const reference: Reference = {
      id: Date.now().toString(),
      url: newReference.url,
      title: newReference.title,
      description: newReference.description,
      type: newReference.type,
      credibilityScore: 5
    };

    setFormData(prev => ({
      ...prev,
      claim: {
        ...prev.claim,
        references: [...prev.claim.references, reference]
      }
    }));

    setNewReference({
      url: '',
      title: '',
      description: '',
      type: 'other'
    });
    setShowAddReference(false);
  };

  const removeReference = (referenceId: string) => {
    setFormData(prev => ({
      ...prev,
      claim: {
        ...prev.claim,
        references: prev.claim.references.filter(ref => ref.id !== referenceId)
      }
    }));
  };

  const validateForm = () => {
    const errors: string[] = [];

    if (formData.title.length < 5) {
      errors.push('Title must be at least 5 characters long');
    }
    if (formData.title.length > 200) {
      errors.push('Title must be less than 200 characters');
    }
    if (!formData.category) {
      errors.push('Please select a category for your debate');
    }
    if (formData.claim.text.length < 20) {
      errors.push('Main position must be at least 20 characters long');
    }
    if (formData.claim.text.length > 800) {
      errors.push('Main position must be less than 800 characters');
    }
    // Only require opponent for one-on-one debates
    if (formData.type === 'one-on-one' && !formData.opponent.id) {
      errors.push('Please select an opponent for one-on-one debates');
    }

    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationErrors = validateForm();
    if (validationErrors.length > 0) {
      setError(validationErrors.join(', '));
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Create payload without description field
      const payload = {
        title: formData.title,
        description: formData.claim.text, // Use claim text as description for backend compatibility
        category: formData.category,
        type: formData.type,
        claim: formData.claim,
        opponent: formData.type === 'one-on-one' ? formData.opponent : undefined
      };

      const response = await debatesAPI.createDebate(payload);
      if (response.success) {
        setSuccess('Debate created successfully!');
        setTimeout(() => {
          onSuccess(response.data.id);
        }, 1000);
      } else {
        setError(response.message || 'Failed to create debate');
      }
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to create debate');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Validation for claim text
  const claimValidation = validateArgument(formData.claim.text);

  return (
    <div className="w-full p-4 md:p-8">
      <div className="bg-brand-surface rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-3xl font-bold text-white">Create New Debate</h2>
          <button
            onClick={onCancel}
            className="text-brand-text-secondary hover:text-white transition-colors"
          >
            <XIcon className="w-6 h-6" />
          </button>
        </div>

        {error && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6">
            <p className="text-red-400">{error}</p>
          </div>
        )}

        {success && (
          <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 mb-6">
            <p className="text-green-400">{success}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-brand-text mb-2">
              Debate Title *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              className="w-full px-4 py-3 bg-brand-bg border border-gray-600 rounded-lg text-brand-text placeholder-brand-text-secondary focus:outline-none focus:ring-2 focus:ring-brand-primary focus:border-transparent"
              placeholder="Enter a compelling debate title..."
              maxLength={200}
              required
            />
            <div className="flex justify-between mt-1">
              <span className="text-xs text-brand-text-secondary">
                {formData.title.length < 5 ? `Need ${5 - formData.title.length} more characters` : 'Good length'}
              </span>
              <span className="text-xs text-brand-text-secondary">
                {formData.title.length}/200
              </span>
            </div>
          </div>

          {/* Category Selection */}
          <div>
            <label className="block text-sm font-medium text-brand-text mb-2">
              Category *
            </label>
            <select
              value={formData.category}
              onChange={(e) => handleInputChange('category', e.target.value)}
              className="w-full px-4 py-3 bg-brand-bg border border-gray-600 rounded-lg text-brand-text focus:outline-none focus:ring-2 focus:ring-brand-primary focus:border-transparent"
              required
            >
              <option value="">Select a category</option>
              {DEBATE_CATEGORIES.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.icon} {category.name} - {category.description}
                </option>
              ))}
            </select>
            <p className="text-xs text-brand-text-secondary mt-1">
              Categories help organize debates and make them easier to discover
            </p>
          </div>

          {/* Main Position/Claim */}
          <div>
            <label className="block text-sm font-medium text-brand-text mb-2">
              Main Position *
            </label>
            <ValidationSummary
              errors={claimValidation.errors}
              warnings={claimValidation.warnings}
            />
            <textarea
              value={formData.claim.text}
              onChange={(e) => handleInputChange('claim.text', e.target.value)}
              className="w-full px-4 py-3 bg-brand-bg border border-gray-600 rounded-lg text-brand-text placeholder-brand-text-secondary focus:outline-none focus:ring-2 focus:ring-brand-primary focus:border-transparent"
              placeholder="State your main position or claim clearly. This is what the debate will be about..."
              rows={4}
              maxLength={800}
              required
            />
            <div className="flex justify-between mt-1">
              <span className="text-xs text-brand-text-secondary">
                {formData.claim.text.length < 20 ? `Need ${20 - formData.claim.text.length} more characters` : 'Good length'}
              </span>
              <span className="text-xs text-brand-text-secondary">
                {formData.claim.text.length}/800
              </span>
            </div>
          </div>

          {/* Debate Type */}
          <div>
            <label className="block text-sm font-medium text-brand-text mb-2">
              Debate Type *
            </label>
            <div className="space-y-3">
              <label className="flex items-start gap-3 cursor-pointer">
                <input
                  type="radio"
                  name="debateType"
                  value="open"
                  checked={formData.type === 'open'}
                  onChange={(e) => {
                    handleInputChange('type', e.target.value);
                    // Clear opponent when switching to open debate
                    if (e.target.value === 'open') {
                      setFormData(prev => ({
                        ...prev,
                        opponent: { id: '', name: '', avatarUrl: '' }
                      }));
                      setOpponentSearchQuery('');
                      setShowOpponentSearch(false);
                    }
                  }}
                  className="mt-1 text-brand-primary focus:ring-brand-primary"
                />
                <div>
                  <div className="font-medium text-brand-text">Open Debate</div>
                  <div className="text-sm text-brand-text-secondary">
                    Anyone can join and add arguments to support either side. Great for exploring different perspectives on a topic.
                  </div>
                </div>
              </label>

              <label className="flex items-start gap-3 cursor-pointer">
                <input
                  type="radio"
                  name="debateType"
                  value="one-on-one"
                  checked={formData.type === 'one-on-one'}
                  onChange={(e) => handleInputChange('type', e.target.value)}
                  className="mt-1 text-brand-primary focus:ring-brand-primary"
                />
                <div>
                  <div className="font-medium text-brand-text">One-on-One Debate</div>
                  <div className="text-sm text-brand-text-secondary">
                    A structured debate between you and a specific opponent. Only participants can add arguments, others can vote.
                  </div>
                </div>
              </label>
            </div>
          </div>



          {/* References */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <label className="block text-sm font-medium text-brand-text">
                Supporting References
              </label>
              <button
                type="button"
                onClick={() => setShowAddReference(true)}
                className="flex items-center gap-1 text-brand-primary hover:text-brand-primary-light text-sm transition-colors"
              >
                <PlusIcon className="w-4 h-4" />
                Add Reference
              </button>
            </div>

            {formData.claim.references.length > 0 && (
              <div className="space-y-2 mb-4">
                {formData.claim.references.map((ref) => (
                  <div key={ref.id} className="bg-brand-bg/50 rounded-lg p-3 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <LinkIcon className="w-4 h-4 text-brand-primary" />
                      <div>
                        <p className="text-sm font-medium text-brand-text">{ref.title}</p>
                        <p className="text-xs text-brand-text-secondary">{ref.url}</p>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeReference(ref.id)}
                      className="text-red-400 hover:text-red-300 transition-colors"
                    >
                      <XIcon className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {showAddReference && (
              <div className="bg-brand-bg/30 rounded-lg p-4 mb-4">
                <div className="space-y-3">
                  <div>
                    <label className="block text-xs font-medium text-brand-text mb-1">URL *</label>
                    <input
                      type="url"
                      value={newReference.url}
                      onChange={(e) => setNewReference(prev => ({ ...prev, url: e.target.value }))}
                      className="w-full px-3 py-2 bg-brand-bg border border-gray-600 rounded text-brand-text text-sm focus:outline-none focus:ring-1 focus:ring-brand-primary"
                      placeholder="https://example.com/source"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-brand-text mb-1">Title *</label>
                    <input
                      type="text"
                      value={newReference.title}
                      onChange={(e) => setNewReference(prev => ({ ...prev, title: e.target.value }))}
                      className="w-full px-3 py-2 bg-brand-bg border border-gray-600 rounded text-brand-text text-sm focus:outline-none focus:ring-1 focus:ring-brand-primary"
                      placeholder="Reference title"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-brand-text mb-1">Type</label>
                    <select
                      value={newReference.type}
                      onChange={(e) => setNewReference(prev => ({ ...prev, type: e.target.value as Reference['type'] }))}
                      className="w-full px-3 py-2 bg-brand-bg border border-gray-600 rounded text-brand-text text-sm focus:outline-none focus:ring-1 focus:ring-brand-primary"
                    >
                      <option value="academic">Academic</option>
                      <option value="news">News</option>
                      <option value="government">Government</option>
                      <option value="organization">Organization</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div className="flex gap-2">
                    <button
                      type="button"
                      onClick={addReference}
                      disabled={!newReference.url || !newReference.title}
                      className="px-3 py-1 bg-brand-primary text-white rounded text-sm hover:bg-brand-primary-dark disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      Add
                    </button>
                    <button
                      type="button"
                      onClick={() => setShowAddReference(false)}
                      className="px-3 py-1 border border-gray-600 text-brand-text rounded text-sm hover:bg-gray-700 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Opponent Selection - Only for one-on-one debates */}
          {formData.type === 'one-on-one' && (
            <div>
              <label className="block text-sm font-medium text-brand-text mb-2">
                Select Opponent *
              </label>

              {formData.opponent.id ? (
                <div className="bg-brand-bg/50 rounded-lg p-4 flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <img
                    src={formData.opponent.avatarUrl}
                    alt={formData.opponent.name}
                    className="w-10 h-10 rounded-full"
                  />
                  <div>
                    <p className="font-medium text-brand-text">{formData.opponent.name}</p>
                    <p className="text-sm text-brand-text-secondary">Selected opponent</p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => {
                    setFormData(prev => ({ ...prev, opponent: { id: '', name: '', avatarUrl: '' } }));
                    setShowOpponentSearch(true);
                  }}
                  className="text-brand-primary hover:text-brand-primary-light text-sm transition-colors"
                >
                  Change
                </button>
                </div>
              ) : (
                <div className="relative">
                  <input
                    type="text"
                    value={opponentSearchQuery}
                    onChange={(e) => {
                      setOpponentSearchQuery(e.target.value);
                      setShowOpponentSearch(true);
                    }}
                    onFocus={() => setShowOpponentSearch(true)}
                    className="w-full px-4 py-3 bg-brand-bg border border-gray-600 rounded-lg text-brand-text placeholder-brand-text-secondary focus:outline-none focus:ring-2 focus:ring-brand-primary focus:border-transparent"
                    placeholder="Search for users to debate with..."
                  />

                  {showOpponentSearch && (
                    <div className="absolute top-full left-0 right-0 mt-1 bg-brand-surface border border-gray-600 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                      {isLoadingOpponents ? (
                        <div className="p-4 text-center text-brand-text-secondary">
                          Searching...
                        </div>
                      ) : availableOpponents.length > 0 ? (
                        availableOpponents.map((opponent) => (
                          <button
                            key={opponent.id}
                            type="button"
                            onClick={() => selectOpponent(opponent)}
                            className="w-full p-3 text-left hover:bg-brand-bg/50 transition-colors flex items-center gap-3"
                          >
                            <img
                              src={opponent.avatarUrl || `https://picsum.photos/seed/${opponent.username}/40`}
                              alt={opponent.name}
                              className="w-8 h-8 rounded-full"
                            />
                            <div>
                              <p className="font-medium text-brand-text">{opponent.name}</p>
                              <p className="text-sm text-brand-text-secondary">@{opponent.username}</p>
                            </div>
                          </button>
                        ))
                      ) : opponentSearchQuery.length >= 2 ? (
                        <div className="p-4 text-center text-brand-text-secondary">
                          No users found matching "{opponentSearchQuery}"
                        </div>
                      ) : (
                        <div className="p-4 text-center text-brand-text-secondary">
                          Type at least 2 characters to search
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Quality Guidelines */}
          <QualityGuidelines />

          {/* Submit Buttons */}
          <div className="flex gap-4 pt-6">
            <button
              type="button"
              onClick={onCancel}
              className="flex-1 px-6 py-3 border border-gray-600 text-brand-text rounded-lg hover:bg-gray-700 transition-colors"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || validateForm().length > 0}
              className="flex-1 px-6 py-3 bg-brand-primary text-white rounded-lg hover:bg-brand-primary-dark disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? 'Creating Debate...' : 'Create Debate'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
