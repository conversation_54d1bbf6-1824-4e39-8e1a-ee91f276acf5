"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.io = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const dotenv_1 = __importDefault(require("dotenv"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const User_1 = __importDefault(require("./models/User"));
const voiceDebateService_1 = require("./services/voiceDebateService");
const database_1 = __importDefault(require("./config/database"));
const debates_1 = __importDefault(require("./routes/debates"));
const voiceDebate_1 = __importDefault(require("./routes/voiceDebate"));
const users_1 = __importDefault(require("./routes/users"));
const auth_1 = __importDefault(require("./routes/auth"));
const admin_1 = __importDefault(require("./routes/admin"));
const errorHandler_1 = require("./middleware/errorHandler");
const notFound_1 = require("./middleware/notFound");
dotenv_1.default.config();
(0, database_1.default)();
const app = (0, express_1.default)();
const server = (0, http_1.createServer)(app);
const io = new socket_io_1.Server(server, {
    cors: {
        origin: process.env.FRONTEND_URL || ["http://localhost:3000", "http://localhost:3001", "http://localhost:3002"],
        methods: ["GET", "POST"]
    }
});
exports.io = io;
const socketToUser = new Map();
const userToDebates = new Map();
const PORT = process.env.PORT || 5000;
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: process.env.FRONTEND_URL || ["http://localhost:3000", "http://localhost:3001", "http://localhost:3002"],
    credentials: true
}));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true }));
app.use('/api/debates', debates_1.default);
app.use('/api/debates', voiceDebate_1.default);
app.use('/api/users', users_1.default);
app.use('/api/auth', auth_1.default);
app.use('/api/admin', admin_1.default);
app.use('/api/moderation', require('./routes/moderation').default);
app.get('/api/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development'
    });
});
io.on('connection', async (socket) => {
    console.log('User connected:', socket.id);
    try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        if (token) {
            const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'fallback-secret');
            const user = await User_1.default.findById(decoded.userId);
            if (user) {
                socketToUser.set(socket.id, user.id);
                console.log(`Socket ${socket.id} authenticated as user ${user.id}`);
            }
        }
    }
    catch (error) {
        console.log(`Socket ${socket.id} authentication failed:`, error);
    }
    socket.on('join-debate', (debateId) => {
        socket.join(`debate-${debateId}`);
        console.log(`User ${socket.id} joined debate ${debateId}`);
        const userId = socketToUser.get(socket.id);
        if (userId) {
            if (!userToDebates.has(userId)) {
                userToDebates.set(userId, new Set());
            }
            userToDebates.get(userId).add(debateId);
        }
    });
    socket.on('leave-debate', async (debateId) => {
        socket.leave(`debate-${debateId}`);
        console.log(`User ${socket.id} left debate ${debateId}`);
        const userId = socketToUser.get(socket.id);
        if (userId) {
            try {
                const result = await voiceDebateService_1.VoiceDebateService.leaveVoiceQueue(debateId, userId);
                if (result.success) {
                    console.log(`Automatically removed user ${userId} from voice queue for debate ${debateId}`);
                }
                else {
                    console.log(`User ${userId} was not in voice queue for debate ${debateId}: ${result.message}`);
                }
            }
            catch (error) {
                console.log(`Failed to remove user ${userId} from voice queue:`, error);
            }
            const userDebates = userToDebates.get(userId);
            if (userDebates) {
                userDebates.delete(debateId);
                if (userDebates.size === 0) {
                    userToDebates.delete(userId);
                }
            }
        }
    });
    socket.on('join-argument-chat', (data) => {
        const roomName = `argument-${data.argumentId}`;
        socket.join(roomName);
        console.log(`User ${socket.id} joined argument chat ${data.argumentId}`);
    });
    socket.on('leave-argument-chat', (data) => {
        const roomName = `argument-${data.argumentId}`;
        socket.leave(roomName);
        console.log(`User ${socket.id} left argument chat ${data.argumentId}`);
    });
    socket.on('join-voice-debate', (debateId) => {
        socket.join(`voice-${debateId}`);
        console.log(`User ${socket.id} joined voice debate ${debateId}`);
    });
    socket.on('leave-voice-debate', (debateId) => {
        socket.leave(`voice-${debateId}`);
        console.log(`User ${socket.id} left voice debate ${debateId}`);
    });
    socket.on('voice-speaking-state', (data) => {
        socket.to(`voice-${data.debateId}`).emit('participant-speaking-state', {
            participantId: data.participantId,
            isSpeaking: data.isSpeaking
        });
    });
    socket.on('voice-mute-state', (data) => {
        socket.to(`voice-${data.debateId}`).emit('participant-mute-state', {
            participantId: data.participantId,
            isMuted: data.isMuted
        });
    });
    socket.on('disconnect', async () => {
        console.log('User disconnected:', socket.id);
        const userId = socketToUser.get(socket.id);
        if (userId) {
            const userDebates = userToDebates.get(userId);
            if (userDebates) {
                for (const debateId of userDebates) {
                    try {
                        const result = await voiceDebateService_1.VoiceDebateService.leaveVoiceQueue(debateId, userId);
                        if (result.success) {
                            console.log(`Automatically removed disconnected user ${userId} from voice queue for debate ${debateId}`);
                        }
                        else {
                            console.log(`Disconnected user ${userId} was not in voice queue for debate ${debateId}: ${result.message}`);
                        }
                    }
                    catch (error) {
                        console.log(`Failed to remove disconnected user ${userId} from voice queue:`, error);
                    }
                }
            }
            socketToUser.delete(socket.id);
            userToDebates.delete(userId);
        }
    });
});
app.use(notFound_1.notFound);
app.use(errorHandler_1.errorHandler);
server.listen(Number(PORT), '0.0.0.0', () => {
    console.log(`🚀 Server running on port ${PORT} (all interfaces)`);
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
});
//# sourceMappingURL=server.js.map