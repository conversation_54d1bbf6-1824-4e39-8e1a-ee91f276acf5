import React, { useState, useEffect } from 'react';
import { User, UsersPaginationResponse } from '../../types';
import { adminAPI } from '../../utils/adminApi';
import { getDisplayName } from '../../utils/userDisplay';
import { SuspendUserModal, UnsuspendUserModal, ErrorModal } from './AdminModal';

export const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    role: 'all',
    search: '',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });
  const [suspendModal, setSuspendModal] = useState<{ isOpen: boolean; user: User | null }>({
    isOpen: false,
    user: null
  });
  const [unsuspendModal, setUnsuspendModal] = useState<{ isOpen: boolean; user: User | null }>({
    isOpen: false,
    user: null
  });
  const [errorModal, setErrorModal] = useState<{ isOpen: boolean; title: string; message: string }>({
    isOpen: false,
    title: '',
    message: ''
  });

  useEffect(() => {
    loadUsers();
  }, [pagination.page, filters]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await adminAPI.getUsers({
        page: pagination.page,
        limit: pagination.limit,
        ...filters
      });
      
      if (response.success) {
        setUsers(response.data.users);
        setPagination(response.data.pagination);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleRoleChange = async (userId: string, newRole: 'user' | 'moderator' | 'admin') => {
    try {
      const response = await adminAPI.updateUserRole(userId, newRole);
      if (response.success) {
        setUsers(users.map(user =>
          user.id === userId ? { ...user, role: newRole } : user
        ));
      }
    } catch (err) {
      setErrorModal({
        isOpen: true,
        title: 'Role Update Failed',
        message: err instanceof Error ? err.message : 'Failed to update user role'
      });
    }
  };

  const handleSuspendUser = async (userId: string, reason: string) => {
    try {
      const response = await adminAPI.suspendUser(userId, true, reason);
      if (response.success) {
        setUsers(users.map(user =>
          user.id === userId ? {
            ...user,
            suspended: true,
            suspensionReason: reason,
            suspendedAt: new Date().toISOString()
          } : user
        ));
      }
    } catch (err) {
      setErrorModal({
        isOpen: true,
        title: 'Suspension Failed',
        message: err instanceof Error ? err.message : 'Failed to suspend user'
      });
      throw err; // Re-throw to handle in modal
    }
  };

  const handleUnsuspendUser = async (userId: string) => {
    try {
      const response = await adminAPI.suspendUser(userId, false);
      if (response.success) {
        setUsers(users.map(user =>
          user.id === userId ? {
            ...user,
            suspended: false,
            suspensionReason: undefined,
            suspendedAt: undefined
          } : user
        ));
      }
    } catch (err) {
      setErrorModal({
        isOpen: true,
        title: 'Unsuspension Failed',
        message: err instanceof Error ? err.message : 'Failed to unsuspend user'
      });
      throw err; // Re-throw to handle in modal
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">User Management</h2>
        <button
          onClick={loadUsers}
          className="bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg transition-colors"
        >
          Refresh
        </button>
      </div>

      {/* Filters */}
      <div className="bg-brand-surface rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-brand-text-secondary text-sm font-medium mb-2">
              Search Users
            </label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              placeholder="Username, email, or name..."
              className="w-full bg-brand-surface-light border border-brand-border rounded-lg px-3 py-2 text-white placeholder-brand-text-secondary focus:outline-none focus:ring-2 focus:ring-brand-primary"
            />
          </div>
          
          <div>
            <label className="block text-brand-text-secondary text-sm font-medium mb-2">
              Role Filter
            </label>
            <select
              value={filters.role}
              onChange={(e) => handleFilterChange('role', e.target.value)}
              className="w-full bg-brand-surface-light border border-brand-border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-brand-primary"
            >
              <option value="all">All Roles</option>
              <option value="user">Users</option>
              <option value="moderator">Moderators</option>
              <option value="admin">Admins</option>
            </select>
          </div>

          <div>
            <label className="block text-brand-text-secondary text-sm font-medium mb-2">
              Sort By
            </label>
            <select
              value={filters.sortBy}
              onChange={(e) => handleFilterChange('sortBy', e.target.value)}
              className="w-full bg-brand-surface-light border border-brand-border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-brand-primary"
            >
              <option value="createdAt">Join Date</option>
              <option value="username">Username</option>
              <option value="email">Email</option>
              <option value="reputation">Reputation</option>
            </select>
          </div>

          <div>
            <label className="block text-brand-text-secondary text-sm font-medium mb-2">
              Order
            </label>
            <select
              value={filters.sortOrder}
              onChange={(e) => handleFilterChange('sortOrder', e.target.value)}
              className="w-full bg-brand-surface-light border border-brand-border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-brand-primary"
            >
              <option value="desc">Descending</option>
              <option value="asc">Ascending</option>
            </select>
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-brand-surface rounded-lg overflow-hidden">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary mx-auto mb-4"></div>
            <p className="text-brand-text-secondary">Loading users...</p>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <p className="text-red-400 mb-4">{error}</p>
            <button
              onClick={loadUsers}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Retry
            </button>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-brand-surface-light">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-brand-text-secondary uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-brand-text-secondary uppercase tracking-wider">
                      Role
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-brand-text-secondary uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-brand-text-secondary uppercase tracking-wider">
                      Reputation
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-brand-text-secondary uppercase tracking-wider">
                      Joined
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-brand-text-secondary uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-brand-border">
                  {users.map((user) => (
                    <UserRow
                      key={user.id}
                      user={user}
                      onRoleChange={handleRoleChange}
                      onSuspendClick={(user) => setSuspendModal({ isOpen: true, user })}
                      onUnsuspendClick={(user) => setUnsuspendModal({ isOpen: true, user })}
                    />
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="px-6 py-4 border-t border-brand-border">
                <div className="flex items-center justify-between">
                  <div className="text-brand-text-secondary text-sm">
                    Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                    {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                    {pagination.total} users
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page === 1}
                      className="px-3 py-1 rounded bg-brand-surface-light text-brand-text-secondary hover:bg-brand-primary hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      Previous
                    </button>
                    <span className="px-3 py-1 text-white">
                      Page {pagination.page} of {pagination.pages}
                    </span>
                    <button
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page === pagination.pages}
                      className="px-3 py-1 rounded bg-brand-surface-light text-brand-text-secondary hover:bg-brand-primary hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Modals */}
      <SuspendUserModal
        isOpen={suspendModal.isOpen}
        onClose={() => setSuspendModal({ isOpen: false, user: null })}
        onConfirm={(reason) => suspendModal.user && handleSuspendUser(suspendModal.user.id, reason)}
        userName={suspendModal.user ? getDisplayName(suspendModal.user) : ''}
      />

      <UnsuspendUserModal
        isOpen={unsuspendModal.isOpen}
        onClose={() => setUnsuspendModal({ isOpen: false, user: null })}
        onConfirm={() => unsuspendModal.user && handleUnsuspendUser(unsuspendModal.user.id)}
        userName={unsuspendModal.user ? getDisplayName(unsuspendModal.user) : ''}
      />

      <ErrorModal
        isOpen={errorModal.isOpen}
        onClose={() => setErrorModal({ isOpen: false, title: '', message: '' })}
        title={errorModal.title}
        message={errorModal.message}
      />
    </div>
  );
};

interface UserRowProps {
  user: User;
  onRoleChange: (userId: string, role: 'user' | 'moderator' | 'admin') => void;
  onSuspendClick: (user: User) => void;
  onUnsuspendClick: (user: User) => void;
}

const UserRow: React.FC<UserRowProps> = ({ user, onRoleChange, onSuspendClick, onUnsuspendClick }) => {
  const [showActions, setShowActions] = useState(false);

  const handleSuspend = () => {
    setShowActions(false);
    onSuspendClick(user);
  };

  const handleUnsuspend = () => {
    setShowActions(false);
    onUnsuspendClick(user);
  };

  return (
    <tr className="hover:bg-brand-surface-light">
      <td className="px-6 py-4 whitespace-nowrap">
        <div>
          <div className="text-white font-medium">{getDisplayName(user)}</div>
          <div className="text-brand-text-secondary text-sm">@{user.username}</div>
          <div className="text-brand-text-secondary text-sm">{user.email}</div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <select
          value={user.role}
          onChange={(e) => onRoleChange(user.id, e.target.value as 'user' | 'moderator' | 'admin')}
          className={`text-xs font-medium px-2 py-1 rounded border-0 ${
            user.role === 'admin' ? 'bg-purple-500/20 text-purple-400' :
            user.role === 'moderator' ? 'bg-green-500/20 text-green-400' :
            'bg-blue-500/20 text-blue-400'
          }`}
        >
          <option value="user">User</option>
          <option value="moderator">Moderator</option>
          <option value="admin">Admin</option>
        </select>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          user.suspended ? 'bg-red-500/20 text-red-400' : 'bg-green-500/20 text-green-400'
        }`}>
          {user.suspended ? 'Suspended' : 'Active'}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-white">
        {user.reputation}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-brand-text-secondary">
        {new Date(user.createdAt).toLocaleDateString()}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="relative">
          <button
            onClick={() => setShowActions(!showActions)}
            className="text-brand-text-secondary hover:text-white"
          >
            ⋮
          </button>
          {showActions && (
            <div className="absolute right-0 mt-2 w-48 bg-brand-surface border border-brand-border rounded-lg shadow-lg z-10">
              <div className="py-1">
                {user.suspended ? (
                  <button
                    onClick={handleUnsuspend}
                    className="block w-full text-left px-4 py-2 text-sm text-green-400 hover:bg-brand-surface-light"
                  >
                    Unsuspend User
                  </button>
                ) : (
                  <button
                    onClick={handleSuspend}
                    className="block w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-brand-surface-light"
                  >
                    Suspend User
                  </button>
                )}
                <button
                  onClick={() => setShowActions(false)}
                  className="block w-full text-left px-4 py-2 text-sm text-brand-text-secondary hover:bg-brand-surface-light"
                >
                  View Details
                </button>
              </div>
            </div>
          )}
        </div>
      </td>
    </tr>
  );
};
