import express from 'express';
import { body, param, query } from 'express-validator';
import {
  getDebates,
  getDebateById,
  createDebate,
  updateDebate,
  deleteDebate,
  joinDebate,
  voteOnDebate,
  addArgument,
  voteOnArgument,
  addSubArgument,
  addEvidence,
  voteOnEvidence,
  addComment,
  voteOnComment,
  reportComment,
  recalculateAllDebateVoteTotals
} from '../controllers/debateController';
import { auth } from '../middleware/auth';
import { validate } from '../middleware/validate';
import {
  validateArgumentQuality,
  rateLimitContent,
  validateArgumentDepth,
  reportContent,
  getContentReports
} from '../middleware/moderation';

const router = express.Router();

// GET /api/debates - Get all debates with optional filtering
router.get('/', [
  query('status').optional().isIn(['draft', 'active', 'completed', 'cancelled']),
  query('isLive').optional().isBoolean(),
  query('category').optional().isIn(['politics', 'technology', 'science', 'philosophy', 'economics', 'environment', 'health', 'education', 'sports', 'entertainment']),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  validate
], getDebates);

// GET /api/debates/:id - Get specific debate
router.get('/:id', [
  param('id').isMongoId(),
  validate
], getDebateById);

// POST /api/debates - Create new debate
router.post('/', [
  auth,
  body('title').trim().isLength({ min: 5, max: 200 }),
  body('description').optional().trim().isLength({ min: 1, max: 1000 }),
  body('category').isIn(['politics', 'technology', 'science', 'philosophy', 'economics', 'environment', 'health', 'education', 'sports', 'entertainment']).withMessage('Category is required and must be a valid category'),
  body('claim.text').trim().isLength({ min: 20, max: 800 }),
  body('claim.references').optional().isArray(),
  body('type').isIn(['one-on-one', 'open']),
  body('opponent.id').optional().isString(),
  validate
], createDebate);

// PUT /api/debates/:id - Update debate
router.put('/:id', [
  auth,
  param('id').isMongoId(),
  body('title').optional().trim().isLength({ min: 5, max: 200 }),
  body('description').optional().trim().isLength({ min: 1, max: 1000 }),
  body('status').optional().isIn(['draft', 'active', 'completed', 'cancelled']),
  validate
], updateDebate);

// DELETE /api/debates/:id - Delete debate
router.delete('/:id', [
  auth,
  param('id').isMongoId(),
  validate
], deleteDebate);

// POST /api/debates/:id/join - Join debate
router.post('/:id/join', [
  auth,
  param('id').isMongoId(),
  validate
], joinDebate);

// POST /api/debates/:id/vote - Vote on debate
router.post('/:id/vote', [
  auth,
  param('id').isMongoId(),
  body('position').isIn(['support', 'oppose']),
  validate
], voteOnDebate);

// POST /api/debates/:id/arguments - Add argument to debate
router.post('/:id/arguments', [
  auth,
  rateLimitContent(5, 30), // Max 5 arguments per 30 minutes
  param('id').isMongoId(),
  body('text').trim().isLength({ min: 10, max: 1000 }),
  body('side').isIn(['FOR', 'AGAINST']),
  body('references').optional().isArray(),
  validate,
  validateArgumentQuality
], addArgument);

// POST /api/debates/:debateId/arguments/:argumentId/vote - Vote on argument
router.post('/:debateId/arguments/:argumentId/vote', [
  auth,
  param('debateId').isMongoId(),
  param('argumentId').isString(),
  body('vote').isIn(['up', 'down']),
  validate
], voteOnArgument);

// POST /api/debates/:debateId/arguments/:argumentId/sub-arguments - Add sub-argument
router.post('/:debateId/arguments/:argumentId/sub-arguments', [
  auth,
  rateLimitContent(10, 30), // Max 10 sub-arguments per 30 minutes
  validateArgumentDepth(5), // Max 5 levels deep
  param('debateId').isMongoId(),
  param('argumentId').isString(),
  body('text').trim().isLength({ min: 10, max: 1000 }),
  body('isChallenge').optional().isBoolean(),
  body('references').optional().isArray(),
  body('evidence').optional().isArray(),
  validate,
  validateArgumentQuality
], addSubArgument);

// POST /api/debates/:debateId/arguments/:argumentId/evidence - Add evidence to argument
router.post('/:debateId/arguments/:argumentId/evidence', [
  auth,
  rateLimitContent(15, 60), // Max 15 pieces of evidence per hour
  param('debateId').isMongoId(),
  param('argumentId').isString(),
  body('type').isIn(['statistic', 'study', 'expert_opinion', 'case_study', 'historical_fact', 'other']),
  body('content').trim().isLength({ min: 10, max: 2000 }),
  body('source.url').isURL(),
  body('source.title').trim().isLength({ min: 1, max: 200 }),
  body('source.type').optional().isIn(['academic', 'news', 'government', 'organization', 'other']),
  validate
], addEvidence);

// POST /api/debates/:debateId/arguments/:argumentId/evidence/:evidenceId/vote - Vote on evidence accuracy
router.post('/:debateId/arguments/:argumentId/evidence/:evidenceId/vote', [
  auth,
  param('debateId').isMongoId(),
  param('argumentId').isString(),
  param('evidenceId').isString(),
  body('vote').isIn(['accurate', 'inaccurate']),
  validate
], voteOnEvidence);

// POST /api/debates/report - Report inappropriate content
router.post('/report', [
  auth,
  body('contentId').notEmpty(),
  body('contentType').isIn(['argument', 'evidence', 'debate']),
  body('reason').isIn(['inappropriate_language', 'harassment', 'spam', 'misinformation', 'off_topic', 'personal_attack', 'other']),
  validate
], reportContent);

// GET /api/debates/reports - Get content reports (moderators only)
router.get('/reports', [
  auth
], getContentReports);

// Migration route to recalculate vote totals
router.post('/recalculate-votes', [
  auth
], recalculateAllDebateVoteTotals);

// POST /api/debates/:debateId/arguments/:argumentId/comments - Add comment to argument
router.post('/:debateId/arguments/:argumentId/comments', [
  auth,
  rateLimitContent(10, 30), // Max 10 comments per 30 minutes
  param('debateId').isMongoId(),
  param('argumentId').isString(),
  body('text').trim().isLength({ min: 1, max: 500 }),
  body('parentCommentId').optional().isString(),
  validate
], addComment);

// POST /api/debates/:debateId/arguments/:argumentId/comments/:commentId/vote - Vote on comment
router.post('/:debateId/arguments/:argumentId/comments/:commentId/vote', [
  auth,
  param('debateId').isMongoId(),
  param('argumentId').isString(),
  param('commentId').isString(),
  body('vote').isIn(['up', 'down']),
  validate
], voteOnComment);

// POST /api/debates/:debateId/arguments/:argumentId/comments/:commentId/report
router.post('/:debateId/arguments/:argumentId/comments/:commentId/report', auth, [
  param('debateId').isMongoId(),
  param('argumentId').isString(),
  param('commentId').isString(),
  validate
], reportComment);

export default router;
