"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModerationItem = void 0;
const mongoose_1 = require("mongoose");
const ParticipantSchema = new mongoose_1.Schema({
    id: { type: String, required: true },
    name: { type: String, required: true },
    avatarUrl: { type: String, required: true }
});
const ModerationCommentSchema = new mongoose_1.Schema({
    id: { type: String, required: true },
    text: { type: String, required: true },
    author: { type: ParticipantSchema, required: true },
    createdAt: { type: Date, required: true },
    upvotes: { type: Number, default: 0 },
    downvotes: { type: Number, default: 0 }
});
const ModerationItemSchema = new mongoose_1.Schema({
    reportedBy: { type: String, required: true },
    reportedAt: { type: Date, required: true, default: Date.now },
    debateId: { type: String, required: true },
    debateTitle: { type: String, required: true },
    argumentId: { type: String, required: true },
    argumentText: { type: String, required: true },
    commentId: { type: String, required: true },
    commentText: { type: String, required: true },
    commentAuthor: { type: ParticipantSchema, required: true },
    commentCreatedAt: { type: Date, required: true },
    contextTree: [ModerationCommentSchema],
    status: {
        type: String,
        enum: ['pending', 'reviewed', 'resolved', 'dismissed'],
        default: 'pending'
    },
    moderatorId: { type: String },
    moderatorNotes: { type: String },
    actionTaken: {
        type: String,
        enum: ['none', 'warning', 'comment_removed', 'user_suspended']
    },
    reviewedAt: { type: Date }
}, {
    timestamps: true
});
ModerationItemSchema.index({ status: 1, reportedAt: 1 });
ModerationItemSchema.index({ debateId: 1, commentId: 1 });
ModerationItemSchema.index({ reportedBy: 1 });
exports.ModerationItem = (0, mongoose_1.model)('ModerationItem', ModerationItemSchema);
//# sourceMappingURL=ModerationQueue.js.map