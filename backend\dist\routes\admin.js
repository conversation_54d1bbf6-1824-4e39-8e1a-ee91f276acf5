"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const auth_1 = require("../middleware/auth");
const validate_1 = require("../middleware/validate");
const adminController_1 = require("../controllers/adminController");
const router = (0, express_1.Router)();
router.use(auth_1.auth);
router.use((0, auth_1.requireRole)(['admin']));
router.get('/stats', adminController_1.getAdminStats);
router.get('/users', [
    (0, express_validator_1.query)('page').optional().isInt({ min: 1 }),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }),
    (0, express_validator_1.query)('role').optional().isIn(['all', 'user', 'moderator', 'admin']),
    (0, express_validator_1.query)('search').optional().isString().isLength({ max: 100 }),
    (0, express_validator_1.query)('sortBy').optional().isIn(['createdAt', 'username', 'email', 'role', 'reputation']),
    (0, express_validator_1.query)('sortOrder').optional().isIn(['asc', 'desc']),
    validate_1.validate
], adminController_1.getUsers);
router.get('/users/:id', [
    (0, express_validator_1.param)('id').isMongoId(),
    validate_1.validate
], adminController_1.getUserDetails);
router.put('/users/:id/role', [
    (0, express_validator_1.param)('id').isMongoId(),
    (0, express_validator_1.body)('role').isIn(['user', 'moderator', 'admin']),
    validate_1.validate
], adminController_1.updateUserRole);
router.put('/users/:id/suspend', [
    (0, express_validator_1.param)('id').isMongoId(),
    (0, express_validator_1.body)('suspended').isBoolean(),
    (0, express_validator_1.body)('reason').optional().isString().isLength({ min: 1, max: 500 }),
    validate_1.validate
], adminController_1.suspendUser);
exports.default = router;
//# sourceMappingURL=admin.js.map