import { Schema, model, Document } from 'mongoose';

export interface IParticipant {
  id: string;
  name: string;
  avatarUrl: string;
}

export interface IModerationComment {
  id: string;
  text: string;
  author: IParticipant;
  createdAt: Date;
  upvotes: number;
  downvotes: number;
}

export interface IModerationItem extends Document {
  reportedBy: string;
  reportedAt: Date;
  debateId: string;
  debateTitle: string;
  argumentId: string;
  argumentText: string;
  commentId: string;
  commentText: string;
  commentAuthor: IParticipant;
  commentCreatedAt: Date;
  // Full comment tree context for moderation
  contextTree: IModerationComment[];
  status: 'pending' | 'reviewed' | 'resolved' | 'dismissed';
  moderatorId?: string;
  moderatorNotes?: string;
  actionTaken?: 'none' | 'warning' | 'comment_removed' | 'user_suspended';
  reviewedAt?: Date;
}

const ParticipantSchema = new Schema({
  id: { type: String, required: true },
  name: { type: String, required: true },
  avatarUrl: { type: String, required: true }
});

const ModerationCommentSchema = new Schema({
  id: { type: String, required: true },
  text: { type: String, required: true },
  author: { type: ParticipantSchema, required: true },
  createdAt: { type: Date, required: true },
  upvotes: { type: Number, default: 0 },
  downvotes: { type: Number, default: 0 }
});

const ModerationItemSchema = new Schema({
  reportedBy: { type: String, required: true },
  reportedAt: { type: Date, required: true, default: Date.now },
  debateId: { type: String, required: true },
  debateTitle: { type: String, required: true },
  argumentId: { type: String, required: true },
  argumentText: { type: String, required: true },
  commentId: { type: String, required: true },
  commentText: { type: String, required: true },
  commentAuthor: { type: ParticipantSchema, required: true },
  commentCreatedAt: { type: Date, required: true },
  contextTree: [ModerationCommentSchema],
  status: { 
    type: String, 
    enum: ['pending', 'reviewed', 'resolved', 'dismissed'], 
    default: 'pending' 
  },
  moderatorId: { type: String },
  moderatorNotes: { type: String },
  actionTaken: { 
    type: String, 
    enum: ['none', 'warning', 'comment_removed', 'user_suspended'] 
  },
  reviewedAt: { type: Date }
}, {
  timestamps: true
});

// Index for efficient querying
ModerationItemSchema.index({ status: 1, reportedAt: 1 });
ModerationItemSchema.index({ debateId: 1, commentId: 1 });
ModerationItemSchema.index({ reportedBy: 1 });

export const ModerationItem = model<IModerationItem>('ModerationItem', ModerationItemSchema);
