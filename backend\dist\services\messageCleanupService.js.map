{"version": 3, "file": "messageCleanupService.js", "sourceRoot": "", "sources": ["../../src/services/messageCleanupService.ts"], "names": [], "mappings": ";;;;;;AAAA,8DAAsC;AACtC,+DAA2D;AAE3D,MAAa,qBAAqB;IAQhC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,UAAkB;QAC/D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,gBAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC/C,IAAI,CAAC,MAAM;gBAAE,OAAO;YAEpB,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YACjE,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ;gBAAE,OAAO;YAE5C,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAGjE,IAAI,aAAa,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;gBACpD,OAAO,CAAC,GAAG,CAAC,wCAAwC,UAAU,KAAK,aAAa,YAAY,CAAC,CAAC;gBAC9F,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAE5D,CAAC;IACH,CAAC;IAMD,MAAM,CAAC,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YAEnD,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAG1E,MAAM,OAAO,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC;gBAChC,oBAAoB,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE;aACjD,CAAC,CAAC;YAEH,IAAI,cAAc,GAAG,CAAC,CAAC;YACvB,IAAI,aAAa,GAAG,CAAC,CAAC;YACtB,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBACpE,cAAc,IAAI,MAAM,CAAC,SAAS,CAAC;gBACnC,aAAa,IAAI,MAAM,CAAC,QAAQ,CAAC;gBACjC,YAAY,IAAI,MAAM,CAAC,OAAO,CAAC;YACjC,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE;gBACxC,cAAc;gBACd,aAAa;gBACb,YAAY;gBACZ,UAAU,EAAE,UAAU,CAAC,WAAW,EAAE;aACrC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,MAAW,EAAE,UAAgB;QACtE,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,cAAc,GAAG,KAAK,CAAC;QAG3B,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACxC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAS;YAEnE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAC1C,QAAQ,CAAC,QAAQ,EACjB,UAAU,EACV,MAAM,EACN,QAAQ,CACT,CAAC;YAEF,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC;YAC9B,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC;YAC5B,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC;YAE1B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACpB,cAAc,GAAG,IAAI,CAAC;YACxB,CAAC;QACH,CAAC;QAGD,IAAI,cAAc,EAAE,CAAC;YACnB,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;QACtB,CAAC;QAED,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;IAC1C,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,kBAAkB,CACrC,QAAe,EACf,UAAgB,EAChB,MAAW,EACX,QAAa,EACb,gBAAuB,EAAE;QAEzB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,QAAQ,GAAG,KAAK,CAAC;QAGrB,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC5B,SAAS,EAAE,CAAC;YAEZ,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAChD,MAAM,KAAK,GAAG,WAAW,GAAG,UAAU,CAAC;YACvC,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;YAGvE,MAAM,WAAW,GAAG,CAAC,GAAG,aAAa,EAAE,OAAO,CAAC,CAAC;YAEhD,IAAI,KAAK,EAAE,CAAC;gBAEV,IAAI,UAAU,EAAE,CAAC;oBACf,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;oBACzE,QAAQ,EAAE,CAAC;gBACb,CAAC;gBAGD,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAClD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAC/C,OAAO,CAAC,OAAO,EACf,UAAU,EACV,MAAM,EACN,QAAQ,EACR,WAAW,CACZ,CAAC;oBACF,SAAS,IAAI,WAAW,CAAC,SAAS,CAAC;oBACnC,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC;oBACjC,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC;gBACjC,CAAC;gBAGD,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACtB,OAAO,EAAE,CAAC;gBACV,QAAQ,GAAG,IAAI,CAAC;YAClB,CAAC;iBAAM,CAAC;gBAEN,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAClD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAC/C,OAAO,CAAC,OAAO,EACf,UAAU,EACV,MAAM,EACN,QAAQ,EACR,WAAW,CACZ,CAAC;oBACF,SAAS,IAAI,WAAW,CAAC,SAAS,CAAC;oBACnC,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC;oBACjC,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC;oBAE/B,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;wBACzB,QAAQ,GAAG,IAAI,CAAC;oBAClB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IACpD,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,qBAAqB,CACxC,OAAY,EACZ,MAAW,EACX,QAAa,EACb,WAAkB;QAElB,IAAI,CAAC;YAEH,KAAK,MAAM,UAAU,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBAC5C,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC;oBACxC,UAAU,EAAE,UAAU;oBACtB,UAAU,EAAE,IAAI,IAAI,EAAE;oBACtB,QAAQ,EAAE,MAAM,CAAC,EAAE;oBACnB,WAAW,EAAE,MAAM,CAAC,KAAK;oBACzB,UAAU,EAAE,QAAQ,CAAC,EAAE;oBACvB,YAAY,EAAE,QAAQ,CAAC,IAAI;oBAC3B,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,WAAW,EAAE,OAAO,CAAC,IAAI;oBACzB,aAAa,EAAE,OAAO,CAAC,MAAM;oBAC7B,gBAAgB,EAAE,OAAO,CAAC,SAAS;oBACnC,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;wBACjC,EAAE,EAAE,CAAC,CAAC,EAAE;wBACR,IAAI,EAAE,CAAC,CAAC,IAAI;wBACZ,MAAM,EAAE,CAAC,CAAC,MAAM;wBAChB,SAAS,EAAE,CAAC,CAAC,SAAS;wBACtB,OAAO,EAAE,CAAC,CAAC,OAAO,IAAI,CAAC;wBACvB,SAAS,EAAE,CAAC,CAAC,SAAS,IAAI,CAAC;qBAC5B,CAAC,CAAC;oBACH,MAAM,EAAE,SAAS;iBAClB,CAAC,CAAC;gBAEH,MAAM,cAAc,CAAC,IAAI,EAAE,CAAC;YAC9B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAE5D,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,eAAe;QAK1B,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAE1E,MAAM,OAAO,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC;YAChC,oBAAoB,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE;SACjD,CAAC,CAAC;QAEH,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,gBAAgB,GAAG,CAAC,CAAC;QAEzB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;gBACxC,IAAI,CAAC,QAAQ,CAAC,QAAQ;oBAAE,SAAS;gBAEjC,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBACzE,aAAa,IAAI,KAAK,CAAC,KAAK,CAAC;gBAC7B,WAAW,IAAI,KAAK,CAAC,GAAG,CAAC;gBACzB,gBAAgB,IAAI,KAAK,CAAC,QAAQ,CAAC;YACrC,CAAC;QACH,CAAC;QAED,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;IAC1D,CAAC;IAEO,MAAM,CAAC,sBAAsB,CAAC,QAAe,EAAE,UAAgB;QAKrE,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAI,QAAQ,GAAG,CAAC,CAAC;QAEjB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,KAAK,EAAE,CAAC;YAER,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAChD,IAAI,WAAW,GAAG,UAAU,EAAE,CAAC;gBAC7B,GAAG,EAAE,CAAC;YACR,CAAC;YAED,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxD,QAAQ,EAAE,CAAC;YACb,CAAC;YAED,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClD,MAAM,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;gBAC5E,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC;gBAC1B,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC;gBACtB,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC;YAClC,CAAC;QACH,CAAC;QAED,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;IAClC,CAAC;IAKO,MAAM,CAAC,YAAY,CAAC,IAAW,EAAE,UAAkB;QACzD,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,IAAI,GAAG,CAAC,EAAE,KAAK,UAAU;gBAAE,OAAO,GAAG,CAAC;YACtC,IAAI,GAAG,CAAC,YAAY,EAAE,CAAC;gBACrB,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;gBAC9D,IAAI,KAAK;oBAAE,OAAO,KAAK,CAAC;YAC1B,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKO,MAAM,CAAC,kBAAkB,CAAC,QAAe;QAC/C,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,KAAK,EAAE,CAAC;YACR,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClD,KAAK,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,MAAW,EAAE,QAAa;QACrE,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAE1E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAC1C,QAAQ,CAAC,QAAQ,EACjB,UAAU,EACV,MAAM,EACN,QAAQ,CACT,CAAC;QAEF,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACpB,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,cAAc,MAAM,CAAC,OAAO,4BAA4B,MAAM,CAAC,QAAQ,oBAAoB,CAAC,CAAC;QAC3G,CAAC;IACH,CAAC;;AA/UH,sDAgVC;AA/UyB,6CAAuB,GAAG,EAAE,CAAC;AAC7B,+CAAyB,GAAG,GAAG,CAAC"}