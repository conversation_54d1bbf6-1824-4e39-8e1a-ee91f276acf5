import * as cron from 'node-cron';
import { MessageCleanupService } from './messageCleanupService';

export class SchedulerService {
  private static cleanupJob: cron.ScheduledTask | null = null;

  /**
   * Start the scheduler service
   */
  static start(): void {
    console.log('Starting scheduler service...');

    // Run cleanup every hour
    this.cleanupJob = cron.schedule('0 * * * *', async () => {
      console.log('Running scheduled message cleanup...');
      try {
        await MessageCleanupService.cleanupOldMessages();
      } catch (error) {
        console.error('Scheduled cleanup failed:', error);
      }
    });

    console.log('Scheduler service started. Message cleanup will run every hour.');

    // Run initial cleanup on startup (after a short delay)
    setTimeout(async () => {
      console.log('Running initial message cleanup...');
      try {
        await MessageCleanupService.cleanupOldMessages();
      } catch (error) {
        console.error('Initial cleanup failed:', error);
      }
    }, 5000); // 5 second delay to allow server to fully start
  }

  /**
   * Stop the scheduler service
   */
  static stop(): void {
    if (this.cleanupJob) {
      this.cleanupJob.stop();
      this.cleanupJob = null;
      console.log('Scheduler service stopped.');
    }
  }

  /**
   * Get scheduler status
   */
  static getStatus(): {
    isRunning: boolean;
    nextRun?: string;
  } {
    return {
      isRunning: this.cleanupJob !== null,
      nextRun: this.cleanupJob ? 'Every hour at minute 0' : undefined
    };
  }

  /**
   * Manually trigger cleanup (for testing/admin purposes)
   */
  static async triggerCleanup(): Promise<void> {
    console.log('Manually triggering message cleanup...');
    await MessageCleanupService.cleanupOldMessages();
  }
}
