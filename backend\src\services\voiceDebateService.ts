import { IDebate, IVoiceSpeaker, IParticipant } from '../models/Debate';
import Debate from '../models/Debate';
import User, { IUser } from '../models/User';
import { io } from '../server';

export class VoiceDebateService {
  private static speakerTimers: Map<string, NodeJS.Timeout> = new Map();

  // Time block constants
  private static readonly DEFAULT_TIME_BLOCKS = 5;
  private static readonly TIME_BLOCK_DURATION = 60; // 1 minute in seconds
  private static timeTrackingIntervals: Map<string, NodeJS.Timeout> = new Map();

  /**
   * Initialize voice debate for a debate
   */
  static async initializeVoiceDebate(debateId: string, settings?: Partial<any>): Promise<IDebate | null> {
    try {
      const debate = await Debate.findById(debateId);
      if (!debate) return null;

      const defaultSettings = {
        isVoiceEnabled: true,
        defaultSpeakingTime: 120, // 2 minutes
        maxSpeakersPerSide: 5,
        allowSpectatorVoice: debate.type === 'open',
        autoRotateSpeakers: true,
        mutualExclusionEnabled: true
      };

      debate.voiceDebate = {
        settings: { ...defaultSettings, ...settings },
        speakerQueue: {
          FOR: [],
          AGAINST: []
        },
        isVoiceActive: false
      };

      await debate.save();
      return debate;
    } catch (error) {
      console.error('Error initializing voice debate:', error);
      return null;
    }
  }

  /**
   * Add a speaker to the voice debate queue
   */
  static async joinVoiceQueue(
    debateId: string, 
    participant: IParticipant, 
    side: 'FOR' | 'AGAINST',
    timeAllocation?: number
  ): Promise<{ success: boolean; message: string; speaker?: IVoiceSpeaker }> {
    try {
      const debate = await Debate.findById(debateId);
      if (!debate || !debate.voiceDebate) {
        return { success: false, message: 'Voice debate not available' };
      }

      // Check permissions for one-on-one debates
      if (debate.type === 'one-on-one') {
        const isAuthorized = participant.id === debate.proponent.id || 
                           (debate.opponent && participant.id === debate.opponent.id);
        if (!isAuthorized) {
          return { success: false, message: 'Only debate participants can join voice in one-on-one debates' };
        }
      }

      // Check if speaker is already in queue
      const existingSpeaker = [
        ...debate.voiceDebate.speakerQueue.FOR,
        ...debate.voiceDebate.speakerQueue.AGAINST
      ].find(speaker => speaker.participant.id === participant.id);

      if (existingSpeaker) {
        return { success: false, message: 'Already in voice queue' };
      }

      // Check queue limits
      const currentQueueSize = debate.voiceDebate.speakerQueue[side].length;
      if (currentQueueSize >= debate.voiceDebate.settings.maxSpeakersPerSide) {
        return { success: false, message: `Queue is full for ${side} side` };
      }

      // Create new speaker
      const allocatedTime = timeAllocation || debate.voiceDebate.settings.defaultSpeakingTime;
      const newSpeaker: IVoiceSpeaker = {
        participant,
        side,
        timeAllocated: allocatedTime,
        timeUsed: 0,
        timeRemaining: allocatedTime,
        isSpeaking: false,
        isMuted: false,
        joinedAt: new Date()
      };

      // Add to queue
      debate.voiceDebate.speakerQueue[side].push(newSpeaker);

      // Auto-start voice debate when both sides have speakers
      const forQueue = debate.voiceDebate.speakerQueue.FOR;
      const againstQueue = debate.voiceDebate.speakerQueue.AGAINST;

      if (!debate.voiceDebate.isVoiceActive && forQueue.length > 0 && againstQueue.length > 0) {
        // Start voice debate - both speakers can talk initially
        debate.voiceDebate.isVoiceActive = true;
        debate.voiceDebate.voiceStartedAt = new Date();

        // Set both first speakers as active (mutual exclusion will handle conflicts)
        forQueue[0].isSpeaking = true;
        forQueue[0].isMuted = false;
        againstQueue[0].isSpeaking = true;
        againstQueue[0].isMuted = false;

        // Start timers for both speakers
        this.startSpeakerTimer(debateId, forQueue[0]);
        this.startSpeakerTimer(debateId, againstQueue[0]);
      }

      await debate.save();

      // Emit real-time update
      io.to(`debate-${debateId}`).emit('voice-queue-updated', {
        debateId,
        speakerQueue: debate.voiceDebate.speakerQueue,
        currentSpeaker: debate.voiceDebate.currentSpeaker,
        isVoiceActive: debate.voiceDebate.isVoiceActive,
        voiceStartedAt: debate.voiceDebate.voiceStartedAt,
        newSpeaker
      });

      return { success: true, message: 'Joined voice queue successfully', speaker: newSpeaker };
    } catch (error) {
      console.error('Error joining voice queue:', error);
      return { success: false, message: 'Failed to join voice queue' };
    }
  }

  /**
   * Remove a speaker from the voice debate queue
   */
  static async leaveVoiceQueue(debateId: string, participantId: string): Promise<{ success: boolean; message: string }> {
    try {
      const debate = await Debate.findById(debateId);
      if (!debate || !debate.voiceDebate) {
        return { success: false, message: 'Voice debate not available' };
      }

      // Remove from both queues
      let removed = false;
      ['FOR', 'AGAINST'].forEach(side => {
        const queue = debate.voiceDebate!.speakerQueue[side as 'FOR' | 'AGAINST'];
        const index = queue.findIndex(speaker => speaker.participant.id === participantId);
        if (index !== -1) {
          queue.splice(index, 1);
          removed = true;
        }
      });

      // If current speaker is leaving, stop their timer and move to next
      if (debate.voiceDebate.currentSpeaker?.participant.id === participantId) {
        this.stopSpeakerTimer(debateId);
        debate.voiceDebate.currentSpeaker = undefined;
        await this.rotateToNextSpeaker(debateId);
      }

      if (removed) {
        // Check if both sides still have speakers after removal
        const forQueue = debate.voiceDebate.speakerQueue.FOR;
        const againstQueue = debate.voiceDebate.speakerQueue.AGAINST;

        if (forQueue.length === 0 || againstQueue.length === 0) {
          // One side is empty - deactivate voice debate
          debate.voiceDebate.isVoiceActive = false;

          // Mute any remaining speakers
          [...forQueue, ...againstQueue].forEach(speaker => {
            speaker.isSpeaking = false;
            speaker.isMuted = true;
          });
        }

        await debate.save();

        // Emit real-time update
        console.log(`Emitting voice-queue-updated for debate ${debateId}, removed participant ${participantId}`);
        console.log('Updated speaker queues:', {
          FOR: debate.voiceDebate.speakerQueue.FOR.map(s => ({ id: s.participant.id, name: s.participant.name })),
          AGAINST: debate.voiceDebate.speakerQueue.AGAINST.map(s => ({ id: s.participant.id, name: s.participant.name }))
        });

        io.to(`debate-${debateId}`).emit('voice-queue-updated', {
          debateId,
          speakerQueue: debate.voiceDebate.speakerQueue,
          isVoiceActive: debate.voiceDebate.isVoiceActive,
          removedParticipantId: participantId
        });

        return { success: true, message: 'Left voice queue successfully' };
      }

      return { success: false, message: 'Not in voice queue' };
    } catch (error) {
      console.error('Error leaving voice queue:', error);
      return { success: false, message: 'Failed to leave voice queue' };
    }
  }

  /**
   * Handle mutual exclusion when both speakers are talking
   */
  static async handleMutualExclusion(debateId: string): Promise<{ success: boolean; message: string }> {
    try {
      const debate = await Debate.findById(debateId);
      if (!debate || !debate.voiceDebate) {
        return { success: false, message: 'Voice debate not available' };
      }

      if (!debate.voiceDebate.settings.mutualExclusionEnabled) {
        return { success: false, message: 'Mutual exclusion not enabled' };
      }

      const forQueue = debate.voiceDebate.speakerQueue.FOR;
      const againstQueue = debate.voiceDebate.speakerQueue.AGAINST;

      // Find currently speaking speakers
      const forSpeaker = forQueue.find(s => s.isSpeaking);
      const againstSpeaker = againstQueue.find(s => s.isSpeaking);

      // If both are speaking, mute both
      if (forSpeaker && againstSpeaker && !forSpeaker.isMuted && !againstSpeaker.isMuted) {
        forSpeaker.isMuted = true;
        againstSpeaker.isMuted = true;

        await debate.save();

        // Emit mutual exclusion event
        io.to(`debate-${debateId}`).emit('mutual-exclusion-triggered', {
          debateId,
          mutedSpeakers: [forSpeaker.participant.id, againstSpeaker.participant.id],
          message: 'Both speakers muted due to simultaneous talking'
        });

        return { success: true, message: 'Both speakers muted due to simultaneous talking' };
      }

      return { success: false, message: 'Mutual exclusion not triggered' };
    } catch (error) {
      console.error('Error handling mutual exclusion:', error);
      return { success: false, message: 'Error handling mutual exclusion' };
    }
  }

  /**
   * Unmute speakers when they stop talking simultaneously
   */
  static async handleMutualExclusionRelease(debateId: string): Promise<{ success: boolean; message: string }> {
    try {
      const debate = await Debate.findById(debateId);
      if (!debate || !debate.voiceDebate) {
        return { success: false, message: 'Voice debate not available' };
      }

      const forQueue = debate.voiceDebate.speakerQueue.FOR;
      const againstQueue = debate.voiceDebate.speakerQueue.AGAINST;

      // Find currently muted speakers
      const forSpeaker = forQueue.find(s => s.isSpeaking && s.isMuted);
      const againstSpeaker = againstQueue.find(s => s.isSpeaking && s.isMuted);

      // If both were muted, unmute them so they can try again
      if (forSpeaker && againstSpeaker) {
        forSpeaker.isMuted = false;
        againstSpeaker.isMuted = false;

        await debate.save();

        // Emit unmute event
        io.to(`debate-${debateId}`).emit('mutual-exclusion-released', {
          debateId,
          unmutedSpeakers: [forSpeaker.participant.id, againstSpeaker.participant.id],
          message: 'Speakers unmuted - can resume talking'
        });

        return { success: true, message: 'Speakers unmuted' };
      }

      return { success: false, message: 'No muted speakers to release' };
    } catch (error) {
      console.error('Error releasing mutual exclusion:', error);
      return { success: false, message: 'Error releasing mutual exclusion' };
    }
  }

  /**
   * Start speaking for the next speaker in queue
   */
  static async rotateToNextSpeaker(debateId: string): Promise<{ success: boolean; speaker?: IVoiceSpeaker }> {
    try {
      const debate = await Debate.findById(debateId);
      if (!debate || !debate.voiceDebate) {
        return { success: false };
      }

      // Find next speaker (alternate between sides if possible)
      let nextSpeaker: IVoiceSpeaker | undefined;
      const { FOR, AGAINST } = debate.voiceDebate.speakerQueue;
      
      // Simple round-robin: alternate between sides
      const currentSide = debate.voiceDebate.currentSpeaker?.side;
      if (currentSide === 'FOR' && AGAINST.length > 0) {
        nextSpeaker = AGAINST[0];
      } else if (currentSide === 'AGAINST' && FOR.length > 0) {
        nextSpeaker = FOR[0];
      } else if (FOR.length > 0) {
        nextSpeaker = FOR[0];
      } else if (AGAINST.length > 0) {
        nextSpeaker = AGAINST[0];
      }

      if (nextSpeaker) {
        // Set as current speaker
        nextSpeaker.isSpeaking = true;
        nextSpeaker.isMuted = false;
        debate.voiceDebate.currentSpeaker = nextSpeaker;

        // Start timer for this speaker
        this.startSpeakerTimer(debateId, nextSpeaker);

        await debate.save();

        // Emit real-time update
        io.to(`debate-${debateId}`).emit('speaker-changed', {
          debateId,
          currentSpeaker: nextSpeaker,
          speakerQueue: debate.voiceDebate.speakerQueue
        });

        return { success: true, speaker: nextSpeaker };
      }

      return { success: false };
    } catch (error) {
      console.error('Error rotating to next speaker:', error);
      return { success: false };
    }
  }

  /**
   * Start timer for current speaker
   */
  private static startSpeakerTimer(debateId: string, speaker: IVoiceSpeaker): void {
    // Clear existing timer if any
    this.stopSpeakerTimer(debateId);

    const timerId = setTimeout(async () => {
      await this.handleSpeakerTimeUp(debateId, speaker.participant.id);
    }, speaker.timeRemaining * 1000);

    this.speakerTimers.set(debateId, timerId);

    // Start real-time time tracking
    this.startTimeTracking(debateId, speaker);

    // Emit timer start event
    io.to(`debate-${debateId}`).emit('speaker-timer-started', {
      debateId,
      speakerId: speaker.participant.id,
      timeRemaining: speaker.timeRemaining
    });
  }

  /**
   * Start real-time time tracking for current speaker
   */
  private static startTimeTracking(debateId: string, speaker: IVoiceSpeaker): void {
    // Clear existing tracking if any
    this.stopTimeTracking(debateId);

    const startTime = Date.now();
    const initialTimeRemaining = speaker.timeRemaining;

    const trackingInterval = setInterval(async () => {
      try {
        const debate = await Debate.findById(debateId);
        if (!debate || !debate.voiceDebate || !debate.voiceDebate.currentSpeaker) {
          this.stopTimeTracking(debateId);
          return;
        }

        const currentSpeaker = debate.voiceDebate.currentSpeaker;
        if (currentSpeaker.participant.id !== speaker.participant.id) {
          this.stopTimeTracking(debateId);
          return;
        }

        // Calculate elapsed time and update remaining time
        const elapsedSeconds = Math.floor((Date.now() - startTime) / 1000);
        const newTimeRemaining = Math.max(0, initialTimeRemaining - elapsedSeconds);

        currentSpeaker.timeRemaining = newTimeRemaining;
        currentSpeaker.timeUsed = currentSpeaker.timeAllocated - newTimeRemaining;

        await debate.save();

        // Emit time update every second
        io.to(`debate-${debateId}`).emit('speaker-time-update', {
          debateId,
          speakerId: currentSpeaker.participant.id,
          timeRemaining: newTimeRemaining,
          timeUsed: currentSpeaker.timeUsed
        });

        // Warning when 30 seconds left
        if (newTimeRemaining === 30) {
          io.to(`debate-${debateId}`).emit('speaker-time-warning', {
            debateId,
            speakerId: currentSpeaker.participant.id,
            timeRemaining: newTimeRemaining
          });
        }

        // Warning when 10 seconds left
        if (newTimeRemaining === 10) {
          io.to(`debate-${debateId}`).emit('speaker-time-critical', {
            debateId,
            speakerId: currentSpeaker.participant.id,
            timeRemaining: newTimeRemaining
          });
        }

      } catch (error) {
        console.error('Error in time tracking:', error);
        this.stopTimeTracking(debateId);
      }
    }, 1000); // Update every second

    this.timeTrackingIntervals.set(debateId, trackingInterval);
  }

  /**
   * Stop time tracking for current speaker
   */
  private static stopTimeTracking(debateId: string): void {
    const intervalId = this.timeTrackingIntervals.get(debateId);
    if (intervalId) {
      clearInterval(intervalId);
      this.timeTrackingIntervals.delete(debateId);
    }
  }

  /**
   * Stop timer for current speaker
   */
  private static stopSpeakerTimer(debateId: string): void {
    const timerId = this.speakerTimers.get(debateId);
    if (timerId) {
      clearTimeout(timerId);
      this.speakerTimers.delete(debateId);
    }

    // Also stop time tracking
    this.stopTimeTracking(debateId);
  }

  /**
   * Handle when speaker's time is up
   */
  private static async handleSpeakerTimeUp(debateId: string, speakerId: string): Promise<void> {
    try {
      const debate = await Debate.findById(debateId);
      if (!debate || !debate.voiceDebate) return;

      let speakerSide: 'FOR' | 'AGAINST' | null = null;
      let speakerRemoved = false;

      // Find and remove the speaker from their queue
      ['FOR', 'AGAINST'].forEach(side => {
        const queue = debate.voiceDebate!.speakerQueue[side as 'FOR' | 'AGAINST'];
        const index = queue.findIndex(speaker => speaker.participant.id === speakerId);
        if (index !== -1) {
          queue.splice(index, 1);
          speakerSide = side as 'FOR' | 'AGAINST';
          speakerRemoved = true;
        }
      });

      if (!speakerRemoved) return;

      // Check if both sides still have speakers
      const forQueue = debate.voiceDebate.speakerQueue.FOR;
      const againstQueue = debate.voiceDebate.speakerQueue.AGAINST;

      if (forQueue.length === 0 || againstQueue.length === 0) {
        // One side is empty - deactivate voice debate
        debate.voiceDebate.isVoiceActive = false;

        // Mute any remaining speakers
        [...forQueue, ...againstQueue].forEach(speaker => {
          speaker.isSpeaking = false;
          speaker.isMuted = true;
        });

        await debate.save();

        // Emit voice debate stopped event
        io.to(`debate-${debateId}`).emit('voice-debate-stopped', {
          debateId,
          reason: 'One side has no speakers remaining',
          speakerQueue: debate.voiceDebate.speakerQueue,
          isVoiceActive: false
        });
      } else {
        // Both sides have speakers - activate next speaker from the same side
        const nextSpeaker = speakerSide === 'FOR' ? forQueue[0] : againstQueue[0];

        if (nextSpeaker) {
          nextSpeaker.isSpeaking = true;
          nextSpeaker.isMuted = false;

          // Start timer for new speaker
          this.startSpeakerTimer(debateId, nextSpeaker);
        }

        await debate.save();

        // Emit speaker rotation event
        io.to(`debate-${debateId}`).emit('speaker-rotated', {
          debateId,
          removedSpeakerId: speakerId,
          newSpeaker: nextSpeaker,
          speakerQueue: debate.voiceDebate.speakerQueue,
          isVoiceActive: debate.voiceDebate.isVoiceActive
        });
      }
    } catch (error) {
      console.error('Error handling speaker time up:', error);
    }
  }

  /**
   * Allocate additional time to a speaker
   */
  static async allocateTime(
    debateId: string, 
    speakerId: string, 
    additionalTime: number,
    allocatedBy: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      const debate = await Debate.findById(debateId);
      if (!debate || !debate.voiceDebate) {
        return { success: false, message: 'Voice debate not available' };
      }

      // Find speaker in queue
      let targetSpeaker: IVoiceSpeaker | undefined;
      ['FOR', 'AGAINST'].forEach(side => {
        const speaker = debate.voiceDebate!.speakerQueue[side as 'FOR' | 'AGAINST']
          .find(s => s.participant.id === speakerId);
        if (speaker) targetSpeaker = speaker;
      });

      // Also check current speaker
      if (debate.voiceDebate.currentSpeaker?.participant.id === speakerId) {
        targetSpeaker = debate.voiceDebate.currentSpeaker;
      }

      if (!targetSpeaker) {
        return { success: false, message: 'Speaker not found in voice debate' };
      }

      // Allocate time
      targetSpeaker.timeAllocated += additionalTime;
      targetSpeaker.timeRemaining += additionalTime;

      // If this is the current speaker, restart timer
      if (targetSpeaker.isSpeaking) {
        this.startSpeakerTimer(debateId, targetSpeaker);
      }

      await debate.save();

      // Emit time allocation event
      io.to(`debate-${debateId}`).emit('time-allocated', {
        debateId,
        speakerId,
        additionalTime,
        newTimeRemaining: targetSpeaker.timeRemaining,
        allocatedBy
      });

      return { success: true, message: `Allocated ${additionalTime} seconds to speaker` };
    } catch (error) {
      console.error('Error allocating time:', error);
      return { success: false, message: 'Failed to allocate time' };
    }
  }

  /**
   * Pause time tracking for current speaker (when muted)
   */
  static async pauseTimeTracking(debateId: string): Promise<void> {
    this.stopTimeTracking(debateId);

    // Emit pause event
    io.to(`debate-${debateId}`).emit('speaker-time-paused', {
      debateId
    });
  }

  /**
   * Resume time tracking for current speaker (when unmuted)
   */
  static async resumeTimeTracking(debateId: string): Promise<void> {
    try {
      const debate = await Debate.findById(debateId);
      if (!debate || !debate.voiceDebate || !debate.voiceDebate.currentSpeaker) {
        return;
      }

      const currentSpeaker = debate.voiceDebate.currentSpeaker;
      if (!currentSpeaker.isMuted && currentSpeaker.isSpeaking) {
        this.startTimeTracking(debateId, currentSpeaker);

        // Emit resume event
        io.to(`debate-${debateId}`).emit('speaker-time-resumed', {
          debateId,
          speakerId: currentSpeaker.participant.id,
          timeRemaining: currentSpeaker.timeRemaining
        });
      }
    } catch (error) {
      console.error('Error resuming time tracking:', error);
    }
  }

  /**
   * Handle mutual exclusion (multiple speakers talking) - Legacy function
   */
  static async handleMutualExclusionLegacy(debateId: string, speakingParticipants: string[]): Promise<void> {
    try {
      const debate = await Debate.findById(debateId);
      if (!debate || !debate.voiceDebate || !debate.voiceDebate.settings.mutualExclusionEnabled) {
        return;
      }

      if (speakingParticipants.length > 1) {
        // Pause time tracking when mutual exclusion occurs
        this.pauseTimeTracking(debateId);

        // Mute all speakers
        ['FOR', 'AGAINST'].forEach(side => {
          debate.voiceDebate!.speakerQueue[side as 'FOR' | 'AGAINST'].forEach(speaker => {
            if (speakingParticipants.includes(speaker.participant.id)) {
              speaker.isMuted = true;
              speaker.isSpeaking = false;
            }
          });
        });

        if (debate.voiceDebate.currentSpeaker &&
            speakingParticipants.includes(debate.voiceDebate.currentSpeaker.participant.id)) {
          debate.voiceDebate.currentSpeaker.isMuted = true;
          debate.voiceDebate.currentSpeaker.isSpeaking = false;
        }

        await debate.save();

        // Emit mutual exclusion event
        io.to(`debate-${debateId}`).emit('mutual-exclusion-triggered', {
          debateId,
          mutedParticipants: speakingParticipants
        });
      }
    } catch (error) {
      console.error('Error handling mutual exclusion:', error);
    }
  }

  /**
   * Initialize time blocks for a user in a debate
   */
  static async initializeTimeBlocks(userId: string, debateId: string): Promise<{ success: boolean; message: string }> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        return { success: false, message: 'User not found' };
      }

      // Check if user already has time blocks for this debate
      const existingBalance = user.timeBlockBalances.find(balance => balance.debateId === debateId);
      if (existingBalance) {
        return { success: true, message: 'Time blocks already initialized' };
      }

      // Add time blocks for this debate
      user.timeBlockBalances.push({
        debateId,
        timeBlocksRemaining: this.DEFAULT_TIME_BLOCKS,
        timeBlocksUsed: 0,
        lastUpdated: new Date()
      });

      await user.save();
      return { success: true, message: `Initialized ${this.DEFAULT_TIME_BLOCKS} time blocks` };
    } catch (error) {
      console.error('Error initializing time blocks:', error);
      return { success: false, message: 'Failed to initialize time blocks' };
    }
  }

  /**
   * Get user's time block balance for a debate
   */
  static async getTimeBlockBalance(userId: string, debateId: string): Promise<{ timeBlocksRemaining: number; timeBlocksUsed: number } | null> {
    try {
      const user = await User.findById(userId);
      if (!user) return null;

      const balance = user.timeBlockBalances.find(balance => balance.debateId === debateId);
      return balance ? {
        timeBlocksRemaining: balance.timeBlocksRemaining,
        timeBlocksUsed: balance.timeBlocksUsed
      } : null;
    } catch (error) {
      console.error('Error getting time block balance:', error);
      return null;
    }
  }

  /**
   * Use a time block to allocate time to a speaker
   */
  static async useTimeBlock(userId: string, debateId: string, speakerId: string): Promise<{ success: boolean; message: string }> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        return { success: false, message: 'User not found' };
      }

      // Find user's time block balance for this debate
      const balanceIndex = user.timeBlockBalances.findIndex(balance => balance.debateId === debateId);
      if (balanceIndex === -1) {
        // Initialize time blocks if not found
        const initResult = await this.initializeTimeBlocks(userId, debateId);
        if (!initResult.success) {
          return initResult;
        }
        // Retry after initialization
        return this.useTimeBlock(userId, debateId, speakerId);
      }

      const balance = user.timeBlockBalances[balanceIndex];
      if (balance.timeBlocksRemaining <= 0) {
        return { success: false, message: 'No time blocks remaining' };
      }

      // Use the time block to allocate time
      const allocateResult = await this.allocateTime(debateId, speakerId, this.TIME_BLOCK_DURATION, userId);
      if (!allocateResult.success) {
        return allocateResult;
      }

      // Deduct the time block
      balance.timeBlocksRemaining -= 1;
      balance.timeBlocksUsed += 1;
      balance.lastUpdated = new Date();

      await user.save();

      // Emit time block usage event
      io.to(`debate-${debateId}`).emit('time-block-used', {
        debateId,
        userId,
        speakerId,
        timeBlocksRemaining: balance.timeBlocksRemaining,
        timeAllocated: this.TIME_BLOCK_DURATION
      });

      return { success: true, message: `Used 1 time block (${balance.timeBlocksRemaining} remaining)` };
    } catch (error) {
      console.error('Error using time block:', error);
      return { success: false, message: 'Failed to use time block' };
    }
  }
}
