
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Agora: The Debate Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'brand-bg': '#111827',
              'brand-surface': '#1F2937',
              'brand-surface-light': '#374151',
              'brand-primary': '#6366F1',
              'brand-primary-hover': '#4F46E5',
              'brand-primary-dark': '#4338CA',
              'brand-primary-light': '#818CF8',
              'brand-secondary': '#A855F7',
              'brand-text': '#E5E7EB',
              'brand-text-light': '#9CA3AF',
              'success': '#22C55E',
              'warning': '#F97316',
              'danger': '#EF4444',
            },
          }
        }
      }
    </script>

  <script type="importmap">
{
  "imports": {
    "react-dom/": "https://esm.sh/react-dom@^19.1.1/",
    "react/": "https://esm.sh/react@^19.1.1/",
    "react": "https://esm.sh/react@^19.1.1"
  }
}
</script>
  <script type="module" crossorigin src="/assets/index-BensujS2.js"></script>
  <link rel="stylesheet" crossorigin href="/assets/index-DNy-DI7m.css">
</head>
  <body class="bg-brand-bg text-brand-text">
    <div id="root"></div>
  </body>
</html>
