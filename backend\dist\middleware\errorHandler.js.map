{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;AAOO,MAAM,YAAY,GAAG,CAC1B,GAAa,EACb,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,KAAK,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;IACvB,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;IAG5B,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAGnB,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,oBAAoB,CAAC;QACrC,KAAK,GAAG,EAAE,GAAG,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IACjD,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,YAAY,IAAK,GAAW,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;QAC7D,MAAM,OAAO,GAAG,+BAA+B,CAAC;QAChD,KAAK,GAAG,EAAE,GAAG,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IACjD,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QACnC,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAE,GAAW,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7F,KAAK,GAAG,EAAE,GAAG,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IACjD,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACrC,MAAM,OAAO,GAAG,eAAe,CAAC;QAChC,KAAK,GAAG,EAAE,GAAG,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IACjD,CAAC;IAED,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACrC,MAAM,OAAO,GAAG,eAAe,CAAC;QAChC,KAAK,GAAG,EAAE,GAAG,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IACjD,CAAC;IAED,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;QACvC,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,cAAc;QACtC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC;KACpE,CAAC,CAAC;AACL,CAAC,CAAC;AA9CW,QAAA,YAAY,gBA8CvB"}