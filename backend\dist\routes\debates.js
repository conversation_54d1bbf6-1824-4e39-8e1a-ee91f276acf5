"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const debateController_1 = require("../controllers/debateController");
const auth_1 = require("../middleware/auth");
const validate_1 = require("../middleware/validate");
const moderation_1 = require("../middleware/moderation");
const router = express_1.default.Router();
router.get('/', [
    (0, express_validator_1.query)('status').optional().isIn(['draft', 'active', 'completed', 'cancelled']),
    (0, express_validator_1.query)('isLive').optional().isBoolean(),
    (0, express_validator_1.query)('category').optional().isIn(['politics', 'technology', 'science', 'philosophy', 'economics', 'environment', 'health', 'education', 'sports', 'entertainment']),
    (0, express_validator_1.query)('page').optional().isInt({ min: 1 }),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }),
    validate_1.validate
], debateController_1.getDebates);
router.get('/:id', [
    (0, express_validator_1.param)('id').isMongoId(),
    validate_1.validate
], debateController_1.getDebateById);
router.post('/', [
    auth_1.auth,
    (0, express_validator_1.body)('title').trim().isLength({ min: 5, max: 200 }),
    (0, express_validator_1.body)('description').optional().trim().isLength({ min: 1, max: 1000 }),
    (0, express_validator_1.body)('category').isIn(['politics', 'technology', 'science', 'philosophy', 'economics', 'environment', 'health', 'education', 'sports', 'entertainment']).withMessage('Category is required and must be a valid category'),
    (0, express_validator_1.body)('claim.text').trim().isLength({ min: 20, max: 800 }),
    (0, express_validator_1.body)('claim.references').optional().isArray(),
    (0, express_validator_1.body)('type').isIn(['one-on-one', 'open']),
    (0, express_validator_1.body)('opponent.id').optional().isString(),
    validate_1.validate
], debateController_1.createDebate);
router.put('/:id', [
    auth_1.auth,
    (0, express_validator_1.param)('id').isMongoId(),
    (0, express_validator_1.body)('title').optional().trim().isLength({ min: 5, max: 200 }),
    (0, express_validator_1.body)('description').optional().trim().isLength({ min: 1, max: 1000 }),
    (0, express_validator_1.body)('status').optional().isIn(['draft', 'active', 'completed', 'cancelled']),
    validate_1.validate
], debateController_1.updateDebate);
router.delete('/:id', [
    auth_1.auth,
    (0, express_validator_1.param)('id').isMongoId(),
    validate_1.validate
], debateController_1.deleteDebate);
router.post('/:id/join', [
    auth_1.auth,
    (0, express_validator_1.param)('id').isMongoId(),
    validate_1.validate
], debateController_1.joinDebate);
router.post('/:id/vote', [
    auth_1.auth,
    (0, express_validator_1.param)('id').isMongoId(),
    (0, express_validator_1.body)('position').isIn(['support', 'oppose']),
    validate_1.validate
], debateController_1.voteOnDebate);
router.post('/:id/arguments', [
    auth_1.auth,
    (0, moderation_1.rateLimitContent)(5, 30),
    (0, express_validator_1.param)('id').isMongoId(),
    (0, express_validator_1.body)('text').trim().isLength({ min: 10, max: 1000 }),
    (0, express_validator_1.body)('side').isIn(['FOR', 'AGAINST']),
    (0, express_validator_1.body)('references').optional().isArray(),
    validate_1.validate,
    moderation_1.validateArgumentQuality
], debateController_1.addArgument);
router.post('/:debateId/arguments/:argumentId/vote', [
    auth_1.auth,
    (0, express_validator_1.param)('debateId').isMongoId(),
    (0, express_validator_1.param)('argumentId').isString(),
    (0, express_validator_1.body)('vote').isIn(['up', 'down']),
    validate_1.validate
], debateController_1.voteOnArgument);
router.post('/:debateId/arguments/:argumentId/sub-arguments', [
    auth_1.auth,
    (0, moderation_1.rateLimitContent)(10, 30),
    (0, moderation_1.validateArgumentDepth)(5),
    (0, express_validator_1.param)('debateId').isMongoId(),
    (0, express_validator_1.param)('argumentId').isString(),
    (0, express_validator_1.body)('text').trim().isLength({ min: 10, max: 1000 }),
    (0, express_validator_1.body)('isChallenge').optional().isBoolean(),
    (0, express_validator_1.body)('references').optional().isArray(),
    (0, express_validator_1.body)('evidence').optional().isArray(),
    validate_1.validate,
    moderation_1.validateArgumentQuality
], debateController_1.addSubArgument);
router.post('/:debateId/arguments/:argumentId/evidence', [
    auth_1.auth,
    (0, moderation_1.rateLimitContent)(15, 60),
    (0, express_validator_1.param)('debateId').isMongoId(),
    (0, express_validator_1.param)('argumentId').isString(),
    (0, express_validator_1.body)('type').isIn(['statistic', 'study', 'expert_opinion', 'case_study', 'historical_fact', 'other']),
    (0, express_validator_1.body)('content').trim().isLength({ min: 10, max: 2000 }),
    (0, express_validator_1.body)('source.url').isURL(),
    (0, express_validator_1.body)('source.title').trim().isLength({ min: 1, max: 200 }),
    (0, express_validator_1.body)('source.type').optional().isIn(['academic', 'news', 'government', 'organization', 'other']),
    validate_1.validate
], debateController_1.addEvidence);
router.post('/:debateId/arguments/:argumentId/evidence/:evidenceId/vote', [
    auth_1.auth,
    (0, express_validator_1.param)('debateId').isMongoId(),
    (0, express_validator_1.param)('argumentId').isString(),
    (0, express_validator_1.param)('evidenceId').isString(),
    (0, express_validator_1.body)('vote').isIn(['accurate', 'inaccurate']),
    validate_1.validate
], debateController_1.voteOnEvidence);
router.post('/report', [
    auth_1.auth,
    (0, express_validator_1.body)('contentId').notEmpty(),
    (0, express_validator_1.body)('contentType').isIn(['argument', 'evidence', 'debate']),
    (0, express_validator_1.body)('reason').isIn(['inappropriate_language', 'harassment', 'spam', 'misinformation', 'off_topic', 'personal_attack', 'other']),
    validate_1.validate
], moderation_1.reportContent);
router.get('/reports', [
    auth_1.auth
], moderation_1.getContentReports);
router.post('/recalculate-votes', [
    auth_1.auth
], debateController_1.recalculateAllDebateVoteTotals);
router.post('/:debateId/arguments/:argumentId/comments', [
    auth_1.auth,
    (0, moderation_1.rateLimitContent)(10, 30),
    (0, express_validator_1.param)('debateId').isMongoId(),
    (0, express_validator_1.param)('argumentId').isString(),
    (0, express_validator_1.body)('text').trim().isLength({ min: 1, max: 500 }),
    (0, express_validator_1.body)('parentCommentId').optional().isString(),
    validate_1.validate
], debateController_1.addComment);
router.post('/:debateId/arguments/:argumentId/comments/:commentId/vote', [
    auth_1.auth,
    (0, express_validator_1.param)('debateId').isMongoId(),
    (0, express_validator_1.param)('argumentId').isString(),
    (0, express_validator_1.param)('commentId').isString(),
    (0, express_validator_1.body)('vote').isIn(['up', 'down']),
    validate_1.validate
], debateController_1.voteOnComment);
router.post('/:debateId/arguments/:argumentId/comments/:commentId/report', auth_1.auth, [
    (0, express_validator_1.param)('debateId').isMongoId(),
    (0, express_validator_1.param)('argumentId').isString(),
    (0, express_validator_1.param)('commentId').isString(),
    validate_1.validate
], debateController_1.reportComment);
exports.default = router;
//# sourceMappingURL=debates.js.map