
import React, { useState } from 'react';
import { useDebateState } from './hooks/useDebateState';
import { DebateCard } from './components/debate/DebateCard';
import { DebateView } from './components/debate/DebateView';
import { CreateDebateForm } from './components/debate/CreateDebateForm';
import { FeaturedDebatesCarousel } from './components/debate/FeaturedDebatesCarousel';
import { DebateCategories } from './components/debate/DebateCategories';
import { AuthModal } from './components/auth';
import { UserProfile, UserDashboard } from './components/user';
import { AdminDashboard } from './components/admin/AdminDashboard';
import { Navigation } from './components/navigation';
import { ProtectedRoute } from './components/routing';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { SocketProvider } from './contexts/SocketContext';
import { VoteSide, DebatePosition } from './types';

type CurrentView = 'debates' | 'debate' | 'profile' | 'dashboard' | 'create-debate' | 'admin';

const AppContent: React.FC = () => {
  const { debates, selectedDebate, selectDebate, castVote, castDebateVote, loading, error, refetch } = useDebateState();
  const { isAuthenticated } = useAuth();
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authModalMode, setAuthModalMode] = useState<'login' | 'register'>('login');
  const [currentView, setCurrentView] = useState<CurrentView>('debates');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const handleVote = (side: VoteSide, argumentId?: string) => {
    if (!isAuthenticated) {
      setAuthModalMode('login');
      setIsAuthModalOpen(true);
      return;
    }

    if (selectedDebate) {
      castVote(selectedDebate.id, side, argumentId);
    }
  };

  const handleDebateVote = (position: DebatePosition) => {
    if (!isAuthenticated) {
      setAuthModalMode('login');
      setIsAuthModalOpen(true);
      return;
    }

    if (selectedDebate) {
      castDebateVote(selectedDebate.id, position);
    }
  };

  const openAuthModal = (mode: 'login' | 'register') => {
    setAuthModalMode(mode);
    setIsAuthModalOpen(true);
  };

  const handleNavigation = (page: 'profile' | 'dashboard' | 'debates' | 'create-debate' | 'admin') => {
    if (page === 'debates') {
      setCurrentView('debates');
      selectDebate(null);
      setSelectedCategory(null); // Clear category filter when navigating to debates
    } else {
      setCurrentView(page);
    }
  };

  const handleCategorySelect = (categoryId: string | null) => {
    setSelectedCategory(categoryId);
  };

  const handleDebateSelect = (debateId: string) => {
    selectDebate(debateId);
    setCurrentView('debate');
  };

  const handleBackToDebates = () => {
    selectDebate(null);
    setCurrentView('debates');
  };

  const handleAddSubArgument = (parentId: string, text: string, isChallenge: boolean) => {
    // This will be handled by the DebateView component directly
    console.log('Add sub-argument:', { parentId, text, isChallenge });
  };

  const handleAddEvidence = (argumentId: string, evidence: any) => {
    // This will be handled by the DebateView component directly
    console.log('Add evidence:', { argumentId, evidence });
  };

  return (
    <div className="min-h-screen bg-brand-bg font-sans">
      <Navigation
        currentView={currentView}
        onNavigate={handleNavigation}
        onAuthModalOpen={openAuthModal}
      />

      <main>
        {currentView === 'profile' ? (
          <ProtectedRoute fallback={
            <div className="space-x-4">
              <button
                onClick={() => openAuthModal('login')}
                className="bg-brand-primary hover:bg-brand-primary-dark text-white px-6 py-2 rounded-lg transition-colors"
              >
                Sign In
              </button>
              <button
                onClick={() => openAuthModal('register')}
                className="bg-brand-surface-light hover:bg-brand-surface text-white px-6 py-2 rounded-lg transition-colors"
              >
                Create Account
              </button>
            </div>
          }>
            <UserProfile />
          </ProtectedRoute>
        ) : currentView === 'dashboard' ? (
          <ProtectedRoute fallback={
            <div className="space-x-4">
              <button
                onClick={() => openAuthModal('login')}
                className="bg-brand-primary hover:bg-brand-primary-dark text-white px-6 py-2 rounded-lg transition-colors"
              >
                Sign In
              </button>
              <button
                onClick={() => openAuthModal('register')}
                className="bg-brand-surface-light hover:bg-brand-surface text-white px-6 py-2 rounded-lg transition-colors"
              >
                Create Account
              </button>
            </div>
          }>
            <UserDashboard onDebateSelect={handleDebateSelect} onNavigate={handleNavigation} />
          </ProtectedRoute>
        ) : currentView === 'create-debate' ? (
          <ProtectedRoute fallback={
            <div className="space-x-4">
              <button
                onClick={() => openAuthModal('login')}
                className="bg-brand-primary hover:bg-brand-primary-dark text-white px-6 py-2 rounded-lg transition-colors"
              >
                Sign In
              </button>
              <button
                onClick={() => openAuthModal('register')}
                className="bg-brand-surface-light hover:bg-brand-surface text-white px-6 py-2 rounded-lg transition-colors"
              >
                Create Account
              </button>
            </div>
          }>
            <CreateDebateForm
              onSuccess={(debateId) => {
                // Navigate to the newly created debate
                handleDebateSelect(debateId);
                // Refresh debates list to include the new debate
                refetch();
              }}
              onCancel={() => handleNavigation('debates')}
            />
          </ProtectedRoute>
        ) : currentView === 'admin' ? (
          <ProtectedRoute fallback={
            <div className="space-x-4">
              <button
                onClick={() => openAuthModal('login')}
                className="bg-brand-primary hover:bg-brand-primary-dark text-white px-6 py-2 rounded-lg transition-colors"
              >
                Sign In
              </button>
              <button
                onClick={() => openAuthModal('register')}
                className="bg-brand-surface-light hover:bg-brand-surface text-white px-6 py-2 rounded-lg transition-colors"
              >
                Create Account
              </button>
            </div>
          }>
            <AdminDashboard />
          </ProtectedRoute>
        ) : currentView === 'debate' && selectedDebate ? (
          <DebateView
            debate={selectedDebate}
            onBack={handleBackToDebates}
            onVote={handleVote}
            onDebateVote={handleDebateVote}
            onAddSubArgument={handleAddSubArgument}
            onAddEvidence={handleAddEvidence}
            onRefresh={refetch}
          />
        ) : loading ? (
          <div className="w-full p-4 md:p-8">
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-primary mx-auto mb-4"></div>
                <p className="text-brand-text-secondary">Loading debates...</p>
              </div>
            </div>
          </div>
        ) : error ? (
          <div className="w-full p-4 md:p-8">
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <div className="text-red-500 text-xl mb-4">⚠️</div>
                <p className="text-red-400 mb-4">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Retry
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="w-full p-4 md:p-8">
            {/* Featured Debates Carousel */}
            {debates.length > 0 && !selectedCategory && (
              <FeaturedDebatesCarousel
                debates={debates}
                onSelect={handleDebateSelect}
              />
            )}

            {/* Debate Categories */}
            <DebateCategories
              onCategorySelect={handleCategorySelect}
              selectedCategory={selectedCategory}
            />

            <h2 className="text-3xl font-extrabold text-white mb-6">
              {selectedCategory ? 'Filtered Debates' : 'Open Debates'}
            </h2>
            {debates.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-brand-text-secondary text-lg mb-4">No debates available yet.</p>
                <p className="text-brand-text-secondary">Be the first to start a debate!</p>
              </div>
            ) : (
              <div className="space-y-6">
                {debates
                  .filter(debate => !selectedCategory || debate.category === selectedCategory)
                  .map((debate) => (
                    <DebateCard key={debate.id} debate={debate} onSelect={handleDebateSelect} />
                  ))}
                {debates.filter(debate => !selectedCategory || debate.category === selectedCategory).length === 0 && selectedCategory && (
                  <div className="text-center py-12">
                    <p className="text-brand-text-secondary text-lg mb-4">No debates found in this category.</p>
                    <p className="text-brand-text-secondary">Try selecting a different category or create a new debate!</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </main>

      <footer className="bg-brand-surface mt-12 py-6">
        <div className="w-full px-4 sm:px-6 lg:px-8 text-center text-brand-text-light text-sm">
          <p>&copy; {new Date().getFullYear()} Agora Platform. All rights reserved.</p>
        </div>
      </footer>

      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        initialMode={authModalMode}
      />
    </div>
  );
};

const App: React.FC = () => {
  return (
    <AuthProvider>
      <SocketProvider>
        <AppContent />
      </SocketProvider>
    </AuthProvider>
  );
};

export default App;
