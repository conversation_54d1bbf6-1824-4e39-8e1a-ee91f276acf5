"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const ReferenceSchema = new mongoose_1.Schema({
    id: { type: String, required: true },
    url: { type: String, required: true },
    title: { type: String, required: true },
    description: { type: String },
    type: {
        type: String,
        enum: ['academic', 'news', 'government', 'organization', 'other'],
        default: 'other'
    },
    credibilityScore: { type: Number, min: 1, max: 10 }
});
const ParticipantSchema = new mongoose_1.Schema({
    id: { type: String, required: true },
    name: { type: String, required: true },
    avatarUrl: { type: String, required: true }
});
const EvidenceSchema = new mongoose_1.Schema({
    id: { type: String, required: true },
    type: {
        type: String,
        enum: ['statistic', 'study', 'expert_opinion', 'case_study', 'historical_fact', 'other'],
        required: true
    },
    content: { type: String, required: true },
    source: { type: ReferenceSchema, required: true },
    verificationStatus: {
        type: String,
        enum: ['verified', 'pending', 'disputed', 'unverified'],
        default: 'unverified'
    },
    addedBy: { type: ParticipantSchema, required: true },
    addedAt: { type: Date, default: Date.now },
    accurateVotes: { type: Number, default: 0 },
    inaccurateVotes: { type: Number, default: 0 },
    totalVotes: { type: Number, default: 0 },
    verificationScore: { type: Number, default: 0 },
    votedBy: [{
            userId: { type: String, required: true },
            vote: { type: String, enum: ['accurate', 'inaccurate'], required: true }
        }]
});
const CommentSchema = new mongoose_1.Schema({
    id: { type: String, required: true },
    author: { type: ParticipantSchema, required: true },
    text: { type: String, required: true },
    parentCommentId: { type: String },
    upvotes: { type: Number, default: 0 },
    downvotes: { type: Number, default: 0 },
    votes: { type: Number, default: 0 },
    userVotes: [{
            userId: { type: String, required: true },
            vote: { type: String, enum: ['up', 'down'], required: true }
        }],
    reportedBy: [{ type: String }],
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
});
CommentSchema.add({ replies: [CommentSchema] });
const ArgumentSchema = new mongoose_1.Schema({
    id: { type: String, required: true },
    author: { type: ParticipantSchema, required: true },
    text: { type: String, required: true },
    references: [ReferenceSchema],
    evidence: [EvidenceSchema],
    side: { type: String, enum: ['FOR', 'AGAINST'], required: true },
    votes: { type: Number, default: 0 },
    upvotes: { type: Number, default: 0 },
    downvotes: { type: Number, default: 0 },
    userVotes: [{
            userId: { type: String, required: true },
            vote: { type: String, enum: ['up', 'down'], required: true }
        }],
    parentArgumentId: { type: String },
    subArguments: [{ type: mongoose_1.Schema.Types.Mixed }],
    comments: [CommentSchema],
    depth: { type: Number, default: 0 },
    strengthScore: { type: Number, default: 0 },
    isChallenge: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
});
ArgumentSchema.add({ subArguments: [ArgumentSchema] });
const ClaimSchema = new mongoose_1.Schema({
    author: { type: ParticipantSchema, required: true },
    text: { type: String, required: true },
    references: [ReferenceSchema]
});
const DebateSchema = new mongoose_1.Schema({
    title: { type: String, required: true },
    description: { type: String, required: true },
    claim: { type: ClaimSchema, required: true },
    participants: [ParticipantSchema],
    proponent: { type: ParticipantSchema, required: true },
    opponent: { type: ParticipantSchema, required: false },
    type: {
        type: String,
        enum: ['one-on-one', 'open'],
        required: true,
        default: 'open'
    },
    arguments: [ArgumentSchema],
    votesFor: { type: Number, default: 0 },
    votesAgainst: { type: Number, default: 0 },
    isLive: { type: Boolean, default: false },
    status: {
        type: String,
        enum: ['draft', 'active', 'completed', 'cancelled'],
        default: 'draft'
    },
    startTime: { type: Date },
    endTime: { type: Date }
}, {
    timestamps: true,
    toJSON: {
        transform: function (doc, ret) {
            ret.id = ret._id;
            delete ret._id;
            delete ret.__v;
            return ret;
        }
    }
});
exports.default = mongoose_1.default.model('Debate', DebateSchema);
//# sourceMappingURL=Debate.js.map