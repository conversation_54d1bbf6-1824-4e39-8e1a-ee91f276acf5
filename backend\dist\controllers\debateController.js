"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.reportComment = exports.voteOnComment = exports.addComment = exports.voteOnEvidence = exports.addEvidence = exports.addSubArgument = exports.voteOnArgument = exports.addArgument = exports.voteOnDebate = exports.joinDebate = exports.deleteDebate = exports.updateDebate = exports.createDebate = exports.getDebateById = exports.getDebates = exports.recalculateAllDebateVoteTotals = void 0;
const Debate_1 = __importDefault(require("../models/Debate"));
const User_1 = __importDefault(require("../models/User"));
const uuid_1 = require("uuid");
const userDisplay_1 = require("../utils/userDisplay");
const server_1 = require("../server");
const calculateDebateVoteTotals = async (debate) => {
    let votesFor = 0;
    let votesAgainst = 0;
    const sumArgumentVotes = (args) => {
        for (const argument of args) {
            const netVotes = (argument.upvotes || 0) - (argument.downvotes || 0);
            if (argument.side === 'FOR') {
                votesFor += netVotes;
            }
            else if (argument.side === 'AGAINST') {
                votesAgainst += netVotes;
            }
            if (argument.subArguments && argument.subArguments.length > 0) {
                sumArgumentVotes(argument.subArguments);
            }
        }
    };
    sumArgumentVotes(debate.arguments);
    debate.votesFor = votesFor;
    debate.votesAgainst = votesAgainst;
};
const recalculateAllDebateVoteTotals = async (req, res, next) => {
    try {
        const debates = await Debate_1.default.find({});
        let updatedCount = 0;
        for (const debate of debates) {
            const oldVotesFor = debate.votesFor;
            const oldVotesAgainst = debate.votesAgainst;
            await calculateDebateVoteTotals(debate);
            if (debate.votesFor !== oldVotesFor || debate.votesAgainst !== oldVotesAgainst) {
                await debate.save();
                updatedCount++;
                console.log(`Updated debate ${debate._id}: FOR ${oldVotesFor} -> ${debate.votesFor}, AGAINST ${oldVotesAgainst} -> ${debate.votesAgainst}`);
            }
        }
        res.json({
            success: true,
            message: `Recalculated vote totals for ${updatedCount} debates`,
            data: { updatedCount, totalDebates: debates.length }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.recalculateAllDebateVoteTotals = recalculateAllDebateVoteTotals;
const calculateArgumentStrength = async (debate) => {
    const calculateStrength = (argument) => {
        const netVotes = Math.max(0, argument.upvotes - argument.downvotes);
        const totalVotes = argument.upvotes + argument.downvotes;
        const voteRatio = totalVotes > 0 ? argument.upvotes / totalVotes : 0.5;
        const voteScore = Math.log(netVotes + 1) * 2 + (voteRatio - 0.5) * 10;
        let evidenceScore = 0;
        if (argument.evidence && argument.evidence.length > 0) {
            const weightedEvidenceSum = argument.evidence.reduce((sum, ev) => {
                let verificationMultiplier = 1;
                switch (ev.verificationStatus) {
                    case 'verified':
                        verificationMultiplier = 1.2;
                        break;
                    case 'pending':
                        verificationMultiplier = 1.0;
                        break;
                    case 'disputed':
                        verificationMultiplier = 0.5;
                        break;
                    case 'unverified':
                        verificationMultiplier = 0.8;
                        break;
                }
                if (ev.totalVotes && ev.totalVotes > 0) {
                    const communityMultiplier = 0.8 + (ev.verificationScore / 100) * 0.4;
                    verificationMultiplier *= communityMultiplier;
                }
                return sum + (5 * verificationMultiplier);
            }, 0);
            evidenceScore = weightedEvidenceSum / argument.evidence.length;
        }
        let subArgumentScore = 0;
        if (argument.subArguments && argument.subArguments.length > 0) {
            let supportingScore = 0;
            let challengingScore = 0;
            let supportingCount = 0;
            let challengingCount = 0;
            for (const subArg of argument.subArguments) {
                const subScore = calculateStrength(subArg);
                if (subArg.isChallenge) {
                    challengingScore += subScore;
                    challengingCount++;
                }
                else {
                    supportingScore += subScore;
                    supportingCount++;
                }
            }
            const supportingAvg = supportingCount > 0 ? supportingScore / supportingCount : 0;
            const challengingAvg = challengingCount > 0 ? challengingScore / challengingCount : 0;
            const supportingBoost = supportingAvg * Math.min(1, supportingCount * 0.3) * 0.6;
            const challengingReduction = challengingAvg * Math.min(1, challengingCount * 0.25) * 0.4;
            subArgumentScore = supportingBoost - challengingReduction;
        }
        const referenceBonus = argument.references && argument.references.length > 0
            ? Math.min(5, argument.references.length * 1.5)
            : 0;
        const baseStrength = 30;
        const totalStrength = Math.max(0, Math.min(100, baseStrength +
            voteScore * 0.3 +
            evidenceScore * 4 +
            subArgumentScore +
            referenceBonus));
        argument.strengthScore = Math.round(totalStrength);
        return totalStrength;
    };
    const processArgument = (argument) => {
        if (argument.subArguments && argument.subArguments.length > 0) {
            for (const subArg of argument.subArguments) {
                processArgument(subArg);
            }
        }
        calculateStrength(argument);
    };
    for (const argument of debate.arguments) {
        processArgument(argument);
    }
};
const getDebates = async (req, res, next) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        const filter = {};
        if (req.query.status)
            filter.status = req.query.status;
        if (req.query.isLive !== undefined)
            filter.isLive = req.query.isLive === 'true';
        const debates = await Debate_1.default.find(filter)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit);
        const total = await Debate_1.default.countDocuments(filter);
        res.json({
            success: true,
            data: debates,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getDebates = getDebates;
const getDebateById = async (req, res, next) => {
    try {
        const debate = await Debate_1.default.findById(req.params.id);
        if (!debate) {
            res.status(404).json({
                success: false,
                message: 'Debate not found'
            });
            return;
        }
        res.json({
            success: true,
            data: debate
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getDebateById = getDebateById;
const createDebate = async (req, res, next) => {
    try {
        const { title, description, claim, opponent, type } = req.body;
        const user = req.user;
        const proponent = {
            id: user._id.toString(),
            name: (0, userDisplay_1.getDisplayName)(user),
            avatarUrl: user.avatarUrl || `https://picsum.photos/seed/${user.username}/40`
        };
        let opponentParticipant = null;
        let participants = [proponent];
        if (type === 'one-on-one') {
            if (!opponent || !opponent.id) {
                res.status(400).json({
                    success: false,
                    message: 'Opponent is required for one-on-one debates'
                });
                return;
            }
            const opponentUser = await User_1.default.findById(opponent.id);
            if (!opponentUser) {
                res.status(404).json({
                    success: false,
                    message: 'Opponent user not found'
                });
                return;
            }
            opponentParticipant = {
                id: opponentUser._id.toString(),
                name: (0, userDisplay_1.getDisplayName)(opponentUser),
                avatarUrl: opponentUser.avatarUrl || `https://picsum.photos/seed/${opponentUser.username}/40`
            };
            participants.push(opponentParticipant);
        }
        const debate = new Debate_1.default({
            title,
            description,
            type: type || 'open',
            claim: {
                author: proponent,
                text: claim.text,
                references: claim.references || []
            },
            participants,
            proponent,
            opponent: opponentParticipant,
            arguments: [],
            votesFor: 0,
            votesAgainst: 0,
            isLive: false,
            status: 'active'
        });
        await debate.save();
        await User_1.default.findByIdAndUpdate(user._id, {
            $push: { debatesCreated: debate._id },
            $inc: { totalVotes: 0 }
        });
        res.status(201).json({
            success: true,
            data: debate,
            message: 'Debate created successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.createDebate = createDebate;
const updateDebate = async (req, res, next) => {
    try {
        const { title, description, status } = req.body;
        const user = req.user;
        const debateId = req.params.id;
        const debate = await Debate_1.default.findById(debateId);
        if (!debate) {
            res.status(404).json({
                success: false,
                message: 'Debate not found'
            });
            return;
        }
        if (debate.proponent.id !== user._id.toString()) {
            res.status(403).json({
                success: false,
                message: 'Only the debate creator can update the debate'
            });
            return;
        }
        if (title)
            debate.title = title;
        if (description)
            debate.description = description;
        if (status)
            debate.status = status;
        await debate.save();
        res.json({
            success: true,
            data: debate,
            message: 'Debate updated successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.updateDebate = updateDebate;
const deleteDebate = async (req, res, next) => {
    try {
        const user = req.user;
        const debateId = req.params.id;
        const debate = await Debate_1.default.findById(debateId);
        if (!debate) {
            res.status(404).json({
                success: false,
                message: 'Debate not found'
            });
            return;
        }
        if (debate.proponent.id !== user._id.toString() && user.role !== 'admin') {
            res.status(403).json({
                success: false,
                message: 'Only the debate creator or admin can delete the debate'
            });
            return;
        }
        await Debate_1.default.findByIdAndDelete(debateId);
        res.json({
            success: true,
            message: 'Debate deleted successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.deleteDebate = deleteDebate;
const joinDebate = async (req, res, next) => {
    try {
        const user = req.user;
        const debateId = req.params.id;
        const debate = await Debate_1.default.findById(debateId);
        if (!debate) {
            res.status(404).json({
                success: false,
                message: 'Debate not found'
            });
            return;
        }
        const isAlreadyParticipant = debate.participants.some(p => p.id === user._id.toString());
        if (isAlreadyParticipant) {
            res.status(400).json({
                success: false,
                message: 'User is already a participant in this debate'
            });
            return;
        }
        const participant = {
            id: user._id.toString(),
            name: (0, userDisplay_1.getDisplayName)(user),
            avatarUrl: user.avatarUrl || `https://picsum.photos/seed/${user.username}/40`
        };
        debate.participants.push(participant);
        await debate.save();
        await User_1.default.findByIdAndUpdate(user._id, {
            $push: { debatesParticipated: debate._id }
        });
        res.json({
            success: true,
            data: participant,
            message: 'Successfully joined the debate'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.joinDebate = joinDebate;
const voteOnDebate = async (req, res, next) => {
    try {
        const { side } = req.body;
        const user = req.user;
        const debateId = req.params.id;
        const debate = await Debate_1.default.findById(debateId);
        if (!debate) {
            res.status(404).json({
                success: false,
                message: 'Debate not found'
            });
            return;
        }
        if (debate.status !== 'active') {
            res.status(400).json({
                success: false,
                message: 'Can only vote on active debates'
            });
            return;
        }
        if (side === 'FOR') {
            debate.votesFor += 1;
        }
        else {
            debate.votesAgainst += 1;
        }
        await debate.save();
        await User_1.default.findByIdAndUpdate(user._id, {
            $inc: { totalVotes: 1 }
        });
        res.json({
            success: true,
            data: {
                votesFor: debate.votesFor,
                votesAgainst: debate.votesAgainst
            },
            message: 'Vote recorded successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.voteOnDebate = voteOnDebate;
const addArgument = async (req, res, next) => {
    try {
        const { text, side, references } = req.body;
        const user = req.user;
        const debateId = req.params.id;
        const debate = await Debate_1.default.findById(debateId);
        if (!debate) {
            res.status(404).json({
                success: false,
                message: 'Debate not found'
            });
            return;
        }
        const userId = user._id.toString();
        const isParticipant = debate.participants.some(p => p.id === userId);
        if (debate.type === 'one-on-one') {
            if (!isParticipant) {
                res.status(403).json({
                    success: false,
                    message: 'Only debate participants can add arguments to one-on-one debates'
                });
                return;
            }
        }
        else if (debate.type === 'open') {
            if (!isParticipant) {
                const newParticipant = {
                    id: userId,
                    name: (0, userDisplay_1.getDisplayName)(user),
                    avatarUrl: user.avatarUrl || `https://picsum.photos/seed/${user.username}/40`
                };
                debate.participants.push(newParticipant);
                await User_1.default.findByIdAndUpdate(user._id, {
                    $push: { debatesParticipated: debate._id }
                });
            }
        }
        const argument = {
            id: (0, uuid_1.v4)(),
            author: {
                id: user._id.toString(),
                name: (0, userDisplay_1.getDisplayName)(user),
                avatarUrl: user.avatarUrl || `https://picsum.photos/seed/${user.username}/40`
            },
            text,
            references: references || [],
            evidence: [],
            side,
            votes: 0,
            upvotes: 0,
            downvotes: 0,
            userVotes: [],
            parentArgumentId: undefined,
            subArguments: [],
            comments: [],
            depth: 0,
            strengthScore: 0,
            isChallenge: false,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        debate.arguments.push(argument);
        await calculateDebateVoteTotals(debate);
        await debate.save();
        res.status(201).json({
            success: true,
            data: argument,
            message: 'Argument added successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.addArgument = addArgument;
const voteOnArgument = async (req, res, next) => {
    try {
        const { vote } = req.body;
        const user = req.user;
        const { debateId, argumentId } = req.params;
        console.log('=== ARGUMENT VOTE DEBUG ===');
        console.log('Vote type:', vote);
        console.log('User:', user.username);
        console.log('Debate ID:', debateId);
        console.log('Argument ID:', argumentId);
        const debate = await Debate_1.default.findById(debateId);
        if (!debate) {
            res.status(404).json({
                success: false,
                message: 'Debate not found'
            });
            return;
        }
        const findArgument = (args, id) => {
            for (const arg of args) {
                if (arg.id === id)
                    return arg;
                const found = findArgument(arg.subArguments || [], id);
                if (found)
                    return found;
            }
            return null;
        };
        const argument = findArgument(debate.arguments, argumentId);
        if (!argument) {
            res.status(404).json({
                success: false,
                message: 'Argument not found'
            });
            return;
        }
        if (!argument.userVotes) {
            argument.userVotes = [];
        }
        const existingVoteIndex = argument.userVotes.findIndex((v) => v.userId === user.id);
        if (existingVoteIndex !== -1) {
            const existingVote = argument.userVotes[existingVoteIndex];
            if (existingVote.vote === vote) {
                argument.userVotes.splice(existingVoteIndex, 1);
                if (vote === 'up') {
                    argument.upvotes = Math.max(0, (argument.upvotes || 0) - 1);
                }
                else {
                    argument.downvotes = Math.max(0, (argument.downvotes || 0) - 1);
                }
            }
            else {
                existingVote.vote = vote;
                if (vote === 'up') {
                    argument.upvotes = (argument.upvotes || 0) + 1;
                    argument.downvotes = Math.max(0, (argument.downvotes || 0) - 1);
                }
                else {
                    argument.downvotes = (argument.downvotes || 0) + 1;
                    argument.upvotes = Math.max(0, (argument.upvotes || 0) - 1);
                }
            }
        }
        else {
            argument.userVotes.push({ userId: user.id, vote });
            if (vote === 'up') {
                argument.upvotes = (argument.upvotes || 0) + 1;
            }
            else {
                argument.downvotes = (argument.downvotes || 0) + 1;
            }
        }
        argument.votes = (argument.upvotes || 0) - (argument.downvotes || 0);
        argument.updatedAt = new Date();
        await calculateArgumentStrength(debate);
        await calculateDebateVoteTotals(debate);
        await debate.save();
        res.json({
            success: true,
            data: {
                argumentId,
                votes: argument.votes,
                upvotes: argument.upvotes,
                downvotes: argument.downvotes,
                strengthScore: argument.strengthScore,
                debateVotesFor: debate.votesFor,
                debateVotesAgainst: debate.votesAgainst
            },
            message: 'Vote on argument recorded successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.voteOnArgument = voteOnArgument;
const addSubArgument = async (req, res, next) => {
    try {
        const { text, isChallenge, references, evidence } = req.body;
        const user = req.user;
        const { debateId, argumentId } = req.params;
        const debate = await Debate_1.default.findById(debateId);
        if (!debate) {
            res.status(404).json({
                success: false,
                message: 'Debate not found'
            });
            return;
        }
        const findArgument = (args, id) => {
            for (const arg of args) {
                if (arg.id === id)
                    return arg;
                const found = findArgument(arg.subArguments || [], id);
                if (found)
                    return found;
            }
            return null;
        };
        const parentArgument = findArgument(debate.arguments, argumentId);
        if (!parentArgument) {
            res.status(404).json({
                success: false,
                message: 'Parent argument not found'
            });
            return;
        }
        const isParticipant = debate.participants.some(p => p.id === user._id.toString());
        if (!isParticipant) {
            res.status(403).json({
                success: false,
                message: 'Only debate participants can add sub-arguments'
            });
            return;
        }
        const subArgumentSide = isChallenge
            ? (parentArgument.side === 'FOR' ? 'AGAINST' : 'FOR')
            : parentArgument.side;
        const subArgument = {
            id: (0, uuid_1.v4)(),
            author: {
                id: user._id.toString(),
                name: (0, userDisplay_1.getDisplayName)(user),
                avatarUrl: user.avatarUrl || `https://picsum.photos/seed/${user.username}/40`
            },
            text,
            references: references || [],
            evidence: evidence || [],
            side: subArgumentSide,
            votes: 0,
            upvotes: 0,
            downvotes: 0,
            userVotes: [],
            parentArgumentId: argumentId,
            subArguments: [],
            comments: [],
            depth: parentArgument.depth + 1,
            strengthScore: 0,
            isChallenge: isChallenge || false,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        if (!parentArgument.subArguments) {
            parentArgument.subArguments = [];
        }
        parentArgument.subArguments.push(subArgument);
        await calculateArgumentStrength(debate);
        await debate.save();
        res.status(201).json({
            success: true,
            data: subArgument,
            message: 'Sub-argument added successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.addSubArgument = addSubArgument;
const addEvidence = async (req, res, next) => {
    try {
        console.log('=== ADD EVIDENCE DEBUG ===');
        console.log('Request body:', JSON.stringify(req.body, null, 2));
        console.log('Request params:', req.params);
        console.log('User:', req.user?.username);
        const { type, content, source } = req.body;
        const user = req.user;
        const { debateId, argumentId } = req.params;
        console.log('Looking for debate with ID:', debateId);
        const debate = await Debate_1.default.findById(debateId);
        if (!debate) {
            console.log('Debate not found');
            res.status(404).json({
                success: false,
                message: 'Debate not found'
            });
            return;
        }
        console.log('Debate found:', debate.title);
        const findArgument = (args, id) => {
            for (const arg of args) {
                if (arg.id === id)
                    return arg;
                const found = findArgument(arg.subArguments || [], id);
                if (found)
                    return found;
            }
            return null;
        };
        console.log('Looking for argument with ID:', argumentId);
        const argument = findArgument(debate.arguments, argumentId);
        console.log('Argument found:', argument ? 'YES' : 'NO');
        if (!argument) {
            res.status(404).json({
                success: false,
                message: 'Argument not found'
            });
            return;
        }
        console.log('Checking participation...');
        console.log('User ID:', user._id.toString());
        console.log('Debate participants:', debate.participants.map(p => ({ id: p.id })));
        const isParticipant = debate.participants.some(p => p.id === user._id.toString());
        console.log('Is participant:', isParticipant);
        if (!isParticipant) {
            console.log('User is not a participant - returning 403');
            res.status(403).json({
                success: false,
                message: 'Only debate participants can add evidence'
            });
            return;
        }
        console.log('Creating evidence object...');
        const evidence = {
            id: (0, uuid_1.v4)(),
            type,
            content,
            source: {
                id: (0, uuid_1.v4)(),
                ...source
            },
            verificationStatus: 'unverified',
            addedBy: {
                id: user._id.toString(),
                name: (0, userDisplay_1.getDisplayName)(user),
                avatarUrl: user.avatarUrl || `https://picsum.photos/seed/${user.username}/40`
            },
            addedAt: new Date(),
            accurateVotes: 0,
            inaccurateVotes: 0,
            totalVotes: 0,
            verificationScore: 0,
            votedBy: []
        };
        console.log('Evidence object created:', JSON.stringify(evidence, null, 2));
        console.log('Adding evidence to argument...');
        if (!argument.evidence) {
            argument.evidence = [];
        }
        argument.evidence.push(evidence);
        console.log('Evidence added to argument. Total evidence count:', argument.evidence.length);
        console.log('Recalculating strength scores...');
        await calculateArgumentStrength(debate);
        console.log('Saving debate...');
        await debate.save();
        console.log('Debate saved successfully!');
        res.status(201).json({
            success: true,
            data: evidence,
            message: 'Evidence added successfully'
        });
        console.log('Response sent successfully!');
    }
    catch (error) {
        next(error);
    }
};
exports.addEvidence = addEvidence;
const voteOnEvidence = async (req, res, next) => {
    try {
        const { vote } = req.body;
        const user = req.user;
        const { debateId, argumentId, evidenceId } = req.params;
        console.log('=== VOTE ON EVIDENCE DEBUG ===');
        console.log('Vote type:', vote);
        console.log('User:', user.username);
        console.log('Debate ID:', debateId);
        console.log('Argument ID:', argumentId);
        console.log('Evidence ID:', evidenceId);
        const debate = await Debate_1.default.findById(debateId);
        if (!debate) {
            res.status(404).json({
                success: false,
                message: 'Debate not found'
            });
            return;
        }
        const findArgumentRecursively = (args, targetId) => {
            for (const arg of args) {
                if (arg.id === targetId)
                    return arg;
                if (arg.subArguments && arg.subArguments.length > 0) {
                    const found = findArgumentRecursively(arg.subArguments, targetId);
                    if (found)
                        return found;
                }
            }
            return null;
        };
        const argument = findArgumentRecursively(debate.arguments, argumentId);
        if (!argument) {
            res.status(404).json({
                success: false,
                message: 'Argument not found'
            });
            return;
        }
        const evidence = argument.evidence?.find((ev) => ev.id === evidenceId);
        if (!evidence) {
            res.status(404).json({
                success: false,
                message: 'Evidence not found'
            });
            return;
        }
        if (evidence.addedBy.id === user._id.toString()) {
            res.status(400).json({
                success: false,
                message: 'You cannot vote on your own evidence'
            });
            return;
        }
        if (!evidence.votedBy) {
            evidence.votedBy = [];
        }
        const userId = user._id.toString();
        const existingVoteIndex = evidence.votedBy.findIndex((v) => v.userId === userId);
        const isVoteChange = existingVoteIndex !== -1;
        if (isVoteChange) {
            const oldVote = evidence.votedBy[existingVoteIndex].vote;
            if (oldVote === 'accurate') {
                evidence.accurateVotes = Math.max(0, (evidence.accurateVotes || 0) - 1);
            }
            else if (oldVote === 'inaccurate') {
                evidence.inaccurateVotes = Math.max(0, (evidence.inaccurateVotes || 0) - 1);
            }
            evidence.votedBy[existingVoteIndex].vote = vote;
        }
        else {
            evidence.votedBy.push({
                userId,
                vote
            });
        }
        if (vote === 'accurate') {
            evidence.accurateVotes = (evidence.accurateVotes || 0) + 1;
        }
        else if (vote === 'inaccurate') {
            evidence.inaccurateVotes = (evidence.inaccurateVotes || 0) + 1;
        }
        else {
            res.status(400).json({
                success: false,
                message: 'Invalid vote type. Must be "accurate" or "inaccurate"'
            });
            return;
        }
        evidence.totalVotes = (evidence.accurateVotes || 0) + (evidence.inaccurateVotes || 0);
        evidence.verificationScore = evidence.totalVotes > 0
            ? (evidence.accurateVotes / evidence.totalVotes) * 100
            : 0;
        await updateEvidenceVerificationStatus(evidence);
        console.log('Evidence after vote:', {
            id: evidence.id,
            accurateVotes: evidence.accurateVotes,
            inaccurateVotes: evidence.inaccurateVotes,
            totalVotes: evidence.totalVotes,
            verificationScore: evidence.verificationScore,
            verificationStatus: evidence.verificationStatus
        });
        await calculateArgumentStrength(debate);
        await debate.save();
        res.json({
            success: true,
            data: {
                evidenceId,
                accurateVotes: evidence.accurateVotes,
                inaccurateVotes: evidence.inaccurateVotes,
                totalVotes: evidence.totalVotes,
                verificationScore: evidence.verificationScore,
                verificationStatus: evidence.verificationStatus
            },
            message: isVoteChange ? 'Vote on evidence updated successfully' : 'Vote on evidence recorded successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.voteOnEvidence = voteOnEvidence;
const updateEvidenceVerificationStatus = async (evidence) => {
    const { totalVotes, verificationScore } = evidence;
    const MIN_VOTES_FOR_VERIFICATION = 3;
    const MIN_VOTES_FOR_DISPUTE = 2;
    if (totalVotes >= MIN_VOTES_FOR_VERIFICATION) {
        if (verificationScore >= 80) {
            evidence.verificationStatus = 'verified';
        }
        else if (verificationScore <= 30) {
            evidence.verificationStatus = 'disputed';
        }
        else {
            evidence.verificationStatus = 'pending';
        }
    }
    else if (totalVotes >= MIN_VOTES_FOR_DISPUTE && verificationScore <= 20) {
        evidence.verificationStatus = 'disputed';
    }
    else {
        evidence.verificationStatus = totalVotes > 0 ? 'pending' : 'unverified';
    }
};
const addComment = async (req, res, next) => {
    try {
        const { text, parentCommentId } = req.body;
        const user = req.user;
        const { debateId, argumentId } = req.params;
        const { MessageCleanupService } = await Promise.resolve().then(() => __importStar(require('../services/messageCleanupService')));
        await MessageCleanupService.cleanupIfNeeded(debateId, argumentId);
        const debate = await Debate_1.default.findById(debateId);
        if (!debate) {
            res.status(404).json({
                success: false,
                message: 'Debate not found'
            });
            return;
        }
        const findArgument = (args, id) => {
            for (const arg of args) {
                if (arg.id === id)
                    return arg;
                const found = findArgument(arg.subArguments || [], id);
                if (found)
                    return found;
            }
            return null;
        };
        const argument = findArgument(debate.arguments, argumentId);
        if (!argument) {
            res.status(404).json({
                success: false,
                message: 'Argument not found'
            });
            return;
        }
        const comment = {
            id: (0, uuid_1.v4)(),
            author: {
                id: user._id.toString(),
                name: (0, userDisplay_1.getDisplayName)(user),
                avatarUrl: user.avatarUrl || `https://picsum.photos/seed/${user.username}/40`
            },
            text,
            parentCommentId,
            replies: [],
            upvotes: 0,
            downvotes: 0,
            votes: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        if (!argument.comments) {
            argument.comments = [];
        }
        if (parentCommentId) {
            const findComment = (comments, id) => {
                for (const comment of comments) {
                    if (comment.id === id)
                        return comment;
                    const found = findComment(comment.replies || [], id);
                    if (found)
                        return found;
                }
                return null;
            };
            const parentComment = findComment(argument.comments, parentCommentId);
            if (!parentComment) {
                res.status(404).json({
                    success: false,
                    message: 'Parent comment not found'
                });
                return;
            }
            if (!parentComment.replies) {
                parentComment.replies = [];
            }
            parentComment.replies.push(comment);
        }
        else {
            argument.comments.push(comment);
        }
        await debate.save();
        server_1.io.to(`argument-${argumentId}`).emit('comment-added', {
            argumentId,
            comment,
            parentCommentId
        });
        res.status(201).json({
            success: true,
            data: comment,
            message: 'Comment added successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.addComment = addComment;
const voteOnComment = async (req, res, next) => {
    try {
        const { vote } = req.body;
        const user = req.user;
        const { debateId, argumentId, commentId } = req.params;
        const debate = await Debate_1.default.findById(debateId);
        if (!debate) {
            res.status(404).json({
                success: false,
                message: 'Debate not found'
            });
            return;
        }
        const findArgument = (args, id) => {
            for (const arg of args) {
                if (arg.id === id)
                    return arg;
                const found = findArgument(arg.subArguments || [], id);
                if (found)
                    return found;
            }
            return null;
        };
        const argument = findArgument(debate.arguments, argumentId);
        if (!argument) {
            res.status(404).json({
                success: false,
                message: 'Argument not found'
            });
            return;
        }
        const findComment = (comments, id) => {
            for (const comment of comments) {
                if (comment.id === id)
                    return comment;
                const found = findComment(comment.replies || [], id);
                if (found)
                    return found;
            }
            return null;
        };
        const comment = findComment(argument.comments || [], commentId);
        if (!comment) {
            res.status(404).json({
                success: false,
                message: 'Comment not found'
            });
            return;
        }
        if (!comment.userVotes) {
            comment.userVotes = [];
        }
        const existingVoteIndex = comment.userVotes.findIndex((v) => v.userId === user.id);
        if (existingVoteIndex !== -1) {
            const existingVote = comment.userVotes[existingVoteIndex];
            if (existingVote.vote === vote) {
                comment.userVotes.splice(existingVoteIndex, 1);
                if (vote === 'up') {
                    comment.upvotes = Math.max(0, (comment.upvotes || 0) - 1);
                }
                else {
                    comment.downvotes = Math.max(0, (comment.downvotes || 0) - 1);
                }
            }
            else {
                existingVote.vote = vote;
                if (vote === 'up') {
                    comment.upvotes = (comment.upvotes || 0) + 1;
                    comment.downvotes = Math.max(0, (comment.downvotes || 0) - 1);
                }
                else {
                    comment.downvotes = (comment.downvotes || 0) + 1;
                    comment.upvotes = Math.max(0, (comment.upvotes || 0) - 1);
                }
            }
        }
        else {
            comment.userVotes.push({ userId: user.id, vote });
            if (vote === 'up') {
                comment.upvotes = (comment.upvotes || 0) + 1;
            }
            else {
                comment.downvotes = (comment.downvotes || 0) + 1;
            }
        }
        comment.votes = (comment.upvotes || 0) - (comment.downvotes || 0);
        await debate.save();
        server_1.io.to(`argument-${argumentId}`).emit('comment-vote-updated', {
            argumentId,
            commentId,
            upvotes: comment.upvotes,
            downvotes: comment.downvotes,
            votes: comment.votes,
            voteType: vote,
            voterId: user.id
        });
        res.json({
            success: true,
            data: {
                upvotes: comment.upvotes,
                downvotes: comment.downvotes,
                votes: comment.votes
            },
            message: 'Vote recorded successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.voteOnComment = voteOnComment;
const reportComment = async (req, res, next) => {
    try {
        const user = req.user;
        const { debateId, argumentId, commentId } = req.params;
        const debate = await Debate_1.default.findById(debateId);
        if (!debate) {
            res.status(404).json({
                success: false,
                message: 'Debate not found'
            });
            return;
        }
        const findArgument = (args, id) => {
            for (const arg of args) {
                if (arg.id === id)
                    return arg;
                const found = findArgument(arg.subArguments || [], id);
                if (found)
                    return found;
            }
            return null;
        };
        const argument = findArgument(debate.arguments, argumentId);
        if (!argument) {
            res.status(404).json({
                success: false,
                message: 'Argument not found'
            });
            return;
        }
        const findCommentWithContext = (comments, id, path = []) => {
            for (const comment of comments) {
                const currentPath = [...path, comment];
                if (comment.id === id) {
                    return { comment, context: currentPath };
                }
                if (comment.replies && comment.replies.length > 0) {
                    const found = findCommentWithContext(comment.replies, id, currentPath);
                    if (found)
                        return found;
                }
            }
            return null;
        };
        const result = findCommentWithContext(argument.comments || [], commentId);
        if (!result) {
            res.status(404).json({
                success: false,
                message: 'Comment not found'
            });
            return;
        }
        const { comment, context } = result;
        if (!comment.reportedBy) {
            comment.reportedBy = [];
        }
        if (comment.reportedBy.includes(user.id)) {
            res.status(400).json({
                success: false,
                message: 'You have already reported this comment'
            });
            return;
        }
        comment.reportedBy.push(user.id);
        const { ModerationItem } = await Promise.resolve().then(() => __importStar(require('../models/ModerationQueue')));
        const moderationItem = new ModerationItem({
            reportedBy: user.id,
            reportedAt: new Date(),
            debateId: debate.id,
            debateTitle: debate.title,
            argumentId: argument.id,
            argumentText: argument.text,
            commentId: comment.id,
            commentText: comment.text,
            commentAuthor: comment.author,
            commentCreatedAt: comment.createdAt,
            contextTree: context.map(c => ({
                id: c.id,
                text: c.text,
                author: c.author,
                createdAt: c.createdAt,
                upvotes: c.upvotes || 0,
                downvotes: c.downvotes || 0
            })),
            status: 'pending'
        });
        await moderationItem.save();
        await debate.save();
        res.json({
            success: true,
            message: 'Comment reported successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.reportComment = reportComment;
//# sourceMappingURL=debateController.js.map