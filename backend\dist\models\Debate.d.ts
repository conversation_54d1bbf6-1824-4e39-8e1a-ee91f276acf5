import mongoose, { Document } from 'mongoose';
export interface IReference {
    id: string;
    url: string;
    title: string;
    description?: string;
    type: 'academic' | 'news' | 'government' | 'organization' | 'other';
    credibilityScore?: number;
}
export interface IEvidence {
    id: string;
    type: 'statistic' | 'study' | 'expert_opinion' | 'case_study' | 'historical_fact' | 'other';
    content: string;
    source: IReference;
    verificationStatus: 'verified' | 'pending' | 'disputed' | 'unverified';
    addedBy: IParticipant;
    addedAt: Date;
    accurateVotes: number;
    inaccurateVotes: number;
    totalVotes: number;
    verificationScore: number;
    votedBy: Array<{
        userId: string;
        vote: 'accurate' | 'inaccurate';
    }>;
}
export interface IParticipant {
    id: string;
    name: string;
    avatarUrl: string;
}
export interface IComment {
    id: string;
    author: IParticipant;
    text: string;
    parentCommentId?: string;
    replies: IComment[];
    upvotes: number;
    downvotes: number;
    votes: number;
    userVotes: Array<{
        userId: string;
        vote: 'up' | 'down';
    }>;
    reportedBy: string[];
    createdAt: Date;
    updatedAt: Date;
}
export interface IArgument {
    id: string;
    author: IParticipant;
    text: string;
    references: IReference[];
    evidence: IEvidence[];
    side: 'FOR' | 'AGAINST';
    votes: number;
    upvotes: number;
    downvotes: number;
    userVotes: Array<{
        userId: string;
        vote: 'up' | 'down';
    }>;
    parentArgumentId?: string;
    subArguments: IArgument[];
    comments: IComment[];
    depth: number;
    strengthScore: number;
    isChallenge: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export interface IClaim {
    author: IParticipant;
    text: string;
    references: IReference[];
}
export interface IVoiceSpeaker {
    participant: IParticipant;
    side: 'FOR' | 'AGAINST';
    timeAllocated: number;
    timeUsed: number;
    timeRemaining: number;
    isSpeaking: boolean;
    isMuted: boolean;
    joinedAt: Date;
}
export interface IVoiceDebateSettings {
    isVoiceEnabled: boolean;
    defaultSpeakingTime: number;
    maxSpeakersPerSide: number;
    allowSpectatorVoice: boolean;
    autoRotateSpeakers: boolean;
    mutualExclusionEnabled: boolean;
}
export interface IVoiceDebateState {
    settings: IVoiceDebateSettings;
    speakerQueue: {
        FOR: IVoiceSpeaker[];
        AGAINST: IVoiceSpeaker[];
    };
    currentSpeaker?: IVoiceSpeaker;
    isVoiceActive: boolean;
    voiceStartedAt?: Date;
}
export interface IDebate extends Document {
    title: string;
    description: string;
    category: string;
    claim: IClaim;
    participants: IParticipant[];
    proponent: IParticipant;
    opponent?: IParticipant;
    type: 'one-on-one' | 'open';
    arguments: IArgument[];
    supportVotes: number;
    opposeVotes: number;
    userVotes: Array<{
        userId: string;
        position: 'support' | 'oppose';
    }>;
    isLive: boolean;
    status: 'draft' | 'active' | 'completed' | 'cancelled';
    startTime?: Date;
    endTime?: Date;
    voiceDebate?: IVoiceDebateState;
    createdAt: Date;
    updatedAt: Date;
}
declare const _default: mongoose.Model<IDebate, {}, {}, {}, mongoose.Document<unknown, {}, IDebate, {}, {}> & IDebate & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=Debate.d.ts.map