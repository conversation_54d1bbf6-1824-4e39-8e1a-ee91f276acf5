import React, { useState } from 'react';

interface AdminModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

export const AdminModal: React.FC<AdminModalProps> = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-brand-surface rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-white">{title}</h3>
          <button
            onClick={onClose}
            className="text-brand-text-secondary hover:text-white transition-colors"
          >
            ✕
          </button>
        </div>
        {children}
      </div>
    </div>
  );
};

interface SuspendUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (reason: string) => void;
  userName: string;
}

export const SuspendUserModal: React.FC<SuspendUserModalProps> = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  userName 
}) => {
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!reason.trim()) return;
    
    setLoading(true);
    try {
      await onConfirm(reason.trim());
      setReason('');
      onClose();
    } catch (error) {
      // Error handling is done in the parent component
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setReason('');
    onClose();
  };

  return (
    <AdminModal isOpen={isOpen} onClose={handleClose} title="Suspend User">
      <form onSubmit={handleSubmit}>
        <p className="text-brand-text-secondary mb-4">
          Are you sure you want to suspend <span className="text-white font-medium">{userName}</span>?
        </p>
        
        <div className="mb-4">
          <label className="block text-brand-text-secondary text-sm font-medium mb-2">
            Suspension Reason *
          </label>
          <textarea
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder="Enter the reason for suspension..."
            rows={3}
            required
            className="w-full bg-brand-surface-light border border-brand-border rounded-lg px-3 py-2 text-white placeholder-brand-text-secondary focus:outline-none focus:ring-2 focus:ring-brand-primary"
          />
        </div>

        <div className="flex space-x-3">
          <button
            type="submit"
            disabled={!reason.trim() || loading}
            className="flex-1 bg-red-500 hover:bg-red-600 disabled:bg-red-500/50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors"
          >
            {loading ? 'Suspending...' : 'Suspend User'}
          </button>
          <button
            type="button"
            onClick={handleClose}
            disabled={loading}
            className="flex-1 bg-gray-500 hover:bg-gray-600 disabled:bg-gray-500/50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors"
          >
            Cancel
          </button>
        </div>
      </form>
    </AdminModal>
  );
};

interface UnsuspendUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  userName: string;
}

export const UnsuspendUserModal: React.FC<UnsuspendUserModalProps> = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  userName 
}) => {
  const [loading, setLoading] = useState(false);

  const handleConfirm = async () => {
    setLoading(true);
    try {
      await onConfirm();
      onClose();
    } catch (error) {
      // Error handling is done in the parent component
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminModal isOpen={isOpen} onClose={onClose} title="Unsuspend User">
      <div>
        <p className="text-brand-text-secondary mb-6">
          Are you sure you want to unsuspend <span className="text-white font-medium">{userName}</span>?
        </p>

        <div className="flex space-x-3">
          <button
            onClick={handleConfirm}
            disabled={loading}
            className="flex-1 bg-green-500 hover:bg-green-600 disabled:bg-green-500/50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors"
          >
            {loading ? 'Unsuspending...' : 'Unsuspend User'}
          </button>
          <button
            onClick={onClose}
            disabled={loading}
            className="flex-1 bg-gray-500 hover:bg-gray-600 disabled:bg-gray-500/50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors"
          >
            Cancel
          </button>
        </div>
      </div>
    </AdminModal>
  );
};

interface ErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string;
}

export const ErrorModal: React.FC<ErrorModalProps> = ({ isOpen, onClose, title, message }) => {
  return (
    <AdminModal isOpen={isOpen} onClose={onClose} title={title}>
      <div>
        <p className="text-red-400 mb-6">{message}</p>
        <button
          onClick={onClose}
          className="w-full bg-brand-primary hover:bg-brand-primary-dark text-white px-4 py-2 rounded-lg transition-colors"
        >
          OK
        </button>
      </div>
    </AdminModal>
  );
};
