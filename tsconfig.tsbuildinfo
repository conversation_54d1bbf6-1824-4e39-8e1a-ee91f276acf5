{"root": ["./test-argument-voting-fixes.cjs", "./test-argument-voting-fixes.js", "./test-argument-voting.js", "./test-evidence-voting.js", "./vite.config.ts", "./backend/update-debate-status.js", "./backend/dist/server.d.ts", "./backend/dist/server.js", "./backend/dist/config/database.d.ts", "./backend/dist/config/database.js", "./backend/dist/controllers/authcontroller.d.ts", "./backend/dist/controllers/authcontroller.js", "./backend/dist/controllers/debatecontroller.d.ts", "./backend/dist/controllers/debatecontroller.js", "./backend/dist/controllers/moderationcontroller.d.ts", "./backend/dist/controllers/moderationcontroller.js", "./backend/dist/controllers/usercontroller.d.ts", "./backend/dist/controllers/usercontroller.js", "./backend/dist/middleware/auth.d.ts", "./backend/dist/middleware/auth.js", "./backend/dist/middleware/errorhandler.d.ts", "./backend/dist/middleware/errorhandler.js", "./backend/dist/middleware/moderation.d.ts", "./backend/dist/middleware/moderation.js", "./backend/dist/middleware/notfound.d.ts", "./backend/dist/middleware/notfound.js", "./backend/dist/middleware/validate.d.ts", "./backend/dist/middleware/validate.js", "./backend/dist/models/debate.d.ts", "./backend/dist/models/debate.js", "./backend/dist/models/moderationqueue.d.ts", "./backend/dist/models/moderationqueue.js", "./backend/dist/models/user.d.ts", "./backend/dist/models/user.js", "./backend/dist/routes/auth.d.ts", "./backend/dist/routes/auth.js", "./backend/dist/routes/debates.d.ts", "./backend/dist/routes/debates.js", "./backend/dist/routes/moderation.d.ts", "./backend/dist/routes/moderation.js", "./backend/dist/routes/users.d.ts", "./backend/dist/routes/users.js", "./backend/dist/services/messagecleanupservice.d.ts", "./backend/dist/services/messagecleanupservice.js", "./backend/dist/services/scheduler.d.ts", "./backend/dist/services/scheduler.js", "./backend/dist/utils/userdisplay.d.ts", "./backend/dist/utils/userdisplay.js", "./backend/src/server.ts", "./backend/src/config/database.ts", "./backend/src/controllers/authcontroller.ts", "./backend/src/controllers/debatecontroller.ts", "./backend/src/controllers/moderationcontroller.ts", "./backend/src/controllers/usercontroller.ts", "./backend/src/middleware/auth.ts", "./backend/src/middleware/errorhandler.ts", "./backend/src/middleware/moderation.ts", "./backend/src/middleware/notfound.ts", "./backend/src/middleware/validate.ts", "./backend/src/models/debate.ts", "./backend/src/models/moderationqueue.ts", "./backend/src/models/user.ts", "./backend/src/routes/auth.ts", "./backend/src/routes/debates.ts", "./backend/src/routes/moderation.ts", "./backend/src/routes/users.ts", "./backend/src/services/messagecleanupservice.ts", "./backend/src/services/scheduler.ts", "./backend/src/utils/userdisplay.ts", "./src/app.tsx", "./src/main.tsx", "./src/components/auth/authmodal.tsx", "./src/components/auth/loginform.tsx", "./src/components/auth/registerform.tsx", "./src/components/auth/usermenu.tsx", "./src/components/auth/index.ts", "./src/components/debate/addargumentform.tsx", "./src/components/debate/argumentbreadcrumbs.tsx", "./src/components/debate/argumentcard.tsx", "./src/components/debate/argumenttree.tsx", "./src/components/debate/chatview.tsx", "./src/components/debate/commentthread.tsx", "./src/components/debate/createdebateform.tsx", "./src/components/debate/debatecard.tsx", "./src/components/debate/debatecategories.tsx", "./src/components/debate/debateview.tsx", "./src/components/debate/featureddebatescarousel.tsx", "./src/components/debate/focusedargumentview.tsx", "./src/components/debate/livedebatestage.tsx", "./src/components/navigation/navigation.tsx", "./src/components/navigation/index.ts", "./src/components/routing/protectedroute.tsx", "./src/components/routing/index.ts", "./src/components/ui/participanticon.tsx", "./src/components/ui/reportbutton.tsx", "./src/components/ui/validationfeedback.tsx", "./src/components/ui/icons.tsx", "./src/components/user/userdashboard.tsx", "./src/components/user/userprofile.tsx", "./src/components/user/index.ts", "./src/contexts/authcontext.tsx", "./src/contexts/socketcontext.tsx", "./src/hooks/usedebatestate.ts", "./src/types/index.ts", "./src/utils/api.ts", "./src/utils/userdisplay.ts"], "errors": true, "version": "5.8.3"}