import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Debate } from '../../types';
import { usersAPI, debatesAPI } from '../../utils/api';
import { DebateCard } from '../debate/DebateCard';
import { getDisplayName } from '../../utils/userDisplay';

interface DashboardStats {
  debatesCreated: number;
  debatesParticipated: number;
  totalArguments: number;
  totalVotes: number;
  reputation: number;
}

interface UserDashboardProps {
  onDebateSelect?: (debateId: string) => void;
  onNavigate?: (page: 'debates' | 'profile' | 'dashboard' | 'create-debate') => void;
}

export const UserDashboard: React.FC<UserDashboardProps> = ({ onDebateSelect, onNavigate }) => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    debatesCreated: 0,
    debatesParticipated: 0,
    totalArguments: 0,
    totalVotes: 0,
    reputation: 0
  });
  const [userDebates, setUserDebates] = useState<Debate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'created' | 'participated'>('created');

  useEffect(() => {
    const loadDashboardData = async () => {
      if (!user) return;

      try {
        setLoading(true);
        setError(null);

        // Load user's debates
        const debatesResponse = await usersAPI.getUserDebates(user.id);
        if (debatesResponse.success) {
          setUserDebates(debatesResponse.data);
        }

        // Update stats from user data
        setStats({
          debatesCreated: 0, // Will be calculated from debates
          debatesParticipated: 0, // Will be calculated from debates
          totalArguments: 0, // Will be calculated from debates
          totalVotes: user.totalVotes || 0,
          reputation: user.reputation || 0
        });

      } catch (err) {
        setError('Failed to load dashboard data');
        console.error('Error loading dashboard:', err);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [user]);

  const createdDebates = userDebates.filter(debate => 
    debate.proponent.id === user?.id
  );

  const participatedDebates = userDebates.filter(debate => 
    debate.participants.some(p => p.id === user?.id) && debate.proponent.id !== user?.id
  );

  if (!user) {
    return (
      <div className="w-full p-4 md:p-8">
        <div className="text-center py-12">
          <p className="text-brand-text-secondary">Please log in to view your dashboard.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="w-full p-4 md:p-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-primary mx-auto mb-4"></div>
            <p className="text-brand-text-secondary">Loading dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full p-4 md:p-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Dashboard</h1>
        <p className="text-brand-text-secondary">Welcome back, {getDisplayName(user)}!</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
        <div className="bg-brand-surface rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-brand-primary">{stats.reputation}</div>
          <div className="text-sm text-brand-text-secondary">Reputation</div>
        </div>
        <div className="bg-brand-surface rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-brand-primary">{createdDebates.length}</div>
          <div className="text-sm text-brand-text-secondary">Debates Created</div>
        </div>
        <div className="bg-brand-surface rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-brand-primary">{participatedDebates.length}</div>
          <div className="text-sm text-brand-text-secondary">Debates Joined</div>
        </div>
        <div className="bg-brand-surface rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-brand-primary">{stats.totalArguments}</div>
          <div className="text-sm text-brand-text-secondary">Arguments Posted</div>
        </div>
        <div className="bg-brand-surface rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-brand-primary">{stats.totalVotes}</div>
          <div className="text-sm text-brand-text-secondary">Votes Cast</div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-brand-surface rounded-lg p-6 mb-8">
        <h2 className="text-xl font-bold text-white mb-4">Quick Actions</h2>
        <div className="flex flex-wrap gap-4">
          <button
            onClick={() => onNavigate?.('create-debate')}
            className="bg-brand-primary hover:bg-brand-primary-dark text-white px-6 py-3 rounded-lg transition-colors"
          >
            Create New Debate
          </button>
          <button
            onClick={() => onNavigate?.('debates')}
            className="bg-brand-surface-light hover:bg-brand-surface text-white px-6 py-3 rounded-lg transition-colors"
          >
            Browse Debates
          </button>
          <button
            onClick={() => onNavigate?.('profile')}
            className="bg-brand-surface-light hover:bg-brand-surface text-white px-6 py-3 rounded-lg transition-colors"
          >
            View Profile
          </button>
        </div>
      </div>

      {/* User's Debates */}
      <div className="bg-brand-surface rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-white">Your Debates</h2>
          
          <div className="flex bg-brand-surface-light rounded-lg p-1">
            <button
              onClick={() => setActiveTab('created')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'created'
                  ? 'bg-brand-primary text-white'
                  : 'text-brand-text-secondary hover:text-white'
              }`}
            >
              Created ({createdDebates.length})
            </button>
            <button
              onClick={() => setActiveTab('participated')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'participated'
                  ? 'bg-brand-primary text-white'
                  : 'text-brand-text-secondary hover:text-white'
              }`}
            >
              Participated ({participatedDebates.length})
            </button>
          </div>
        </div>

        {error && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6">
            <p className="text-red-400">{error}</p>
          </div>
        )}

        <div className="space-y-4">
          {activeTab === 'created' ? (
            createdDebates.length > 0 ? (
              createdDebates.map((debate) => (
                <DebateCard key={debate.id} debate={debate} onSelect={() => onDebateSelect?.(debate.id)} />
              ))
            ) : (
              <div className="text-center py-8">
                <p className="text-brand-text-secondary mb-4">You haven't created any debates yet.</p>
                <button
                  onClick={() => onNavigate?.('create-debate')}
                  className="bg-brand-primary hover:bg-brand-primary-dark text-white px-6 py-2 rounded-lg transition-colors"
                >
                  Create Your First Debate
                </button>
              </div>
            )
          ) : (
            participatedDebates.length > 0 ? (
              participatedDebates.map((debate) => (
                <DebateCard key={debate.id} debate={debate} onSelect={() => onDebateSelect?.(debate.id)} />
              ))
            ) : (
              <div className="text-center py-8">
                <p className="text-brand-text-secondary mb-4">You haven't joined any debates yet.</p>
                <button
                  onClick={() => onNavigate?.('debates')}
                  className="bg-brand-primary hover:bg-brand-primary-dark text-white px-6 py-2 rounded-lg transition-colors"
                >
                  Browse Available Debates
                </button>
              </div>
            )
          )}
        </div>
      </div>
    </div>
  );
};
