import mongoose, { Document, Schema } from 'mongoose';

export interface IReference {
  id: string;
  url: string;
  title: string;
  description?: string;
  type: 'academic' | 'news' | 'government' | 'organization' | 'other';
  credibilityScore?: number;
}

export interface IEvidence {
  id: string;
  type: 'statistic' | 'study' | 'expert_opinion' | 'case_study' | 'historical_fact' | 'other';
  content: string;
  source: IReference;
  verificationStatus: 'verified' | 'pending' | 'disputed' | 'unverified';
  addedBy: IParticipant;
  addedAt: Date;
  // Community verification voting
  accurateVotes: number;
  inaccurateVotes: number;
  totalVotes: number;
  verificationScore: number; // Calculated score based on votes
  votedBy: Array<{ userId: string; vote: 'accurate' | 'inaccurate' }>; // Track who voted
}

export interface IParticipant {
  id: string;
  name: string;
  avatarUrl: string;
}

export interface IComment {
  id: string;
  author: IParticipant;
  text: string;
  parentCommentId?: string; // For nested comments/replies
  replies: IComment[]; // Child comments
  upvotes: number;
  downvotes: number;
  votes: number;
  userVotes: Array<{ userId: string; vote: 'up' | 'down' }>; // Track individual user votes
  reportedBy: string[]; // Track users who reported this comment
  createdAt: Date;
  updatedAt: Date;
}

export interface IArgument {
  id: string;
  author: IParticipant;
  text: string;
  references: IReference[];
  evidence: IEvidence[];
  side: 'FOR' | 'AGAINST';
  votes: number;
  upvotes: number;
  downvotes: number;
  userVotes: Array<{ userId: string; vote: 'up' | 'down' }>; // Track user votes to prevent duplicates
  parentArgumentId?: string; // For nested arguments
  subArguments: IArgument[]; // Child arguments that support/challenge this argument
  comments: IComment[]; // Discussion comments for this argument
  depth: number; // How deep in the argument tree (0 = top level)
  strengthScore: number; // Calculated based on evidence, sub-arguments, and votes
  isChallenge: boolean; // True if this argument challenges its parent, false if it supports
  createdAt: Date;
  updatedAt: Date;
}



export interface IClaim {
  author: IParticipant;
  text: string;
  references: IReference[];
}

export interface IVoiceSpeaker {
  participant: IParticipant;
  side: 'FOR' | 'AGAINST';
  timeAllocated: number; // in seconds
  timeUsed: number; // in seconds
  timeRemaining: number; // in seconds
  isSpeaking: boolean;
  isMuted: boolean;
  joinedAt: Date;
}

export interface IVoiceDebateSettings {
  isVoiceEnabled: boolean;
  defaultSpeakingTime: number; // in seconds, default time allocation per speaker
  maxSpeakersPerSide: number; // maximum speakers allowed per side in queue
  allowSpectatorVoice: boolean; // whether non-participants can join voice
  autoRotateSpeakers: boolean; // whether to automatically rotate speakers when time runs out
  mutualExclusionEnabled: boolean; // whether to mute all if multiple speak
}

export interface IVoiceDebateState {
  settings: IVoiceDebateSettings;
  speakerQueue: {
    FOR: IVoiceSpeaker[];
    AGAINST: IVoiceSpeaker[];
  };
  currentSpeaker?: IVoiceSpeaker;
  isVoiceActive: boolean;
  voiceStartedAt?: Date;
}

export interface IDebate extends Document {
  title: string;
  description: string;
  category: string; // Category for filtering and organization
  claim: IClaim;
  participants: IParticipant[];
  proponent: IParticipant;
  opponent?: IParticipant; // Optional for open debates
  type: 'one-on-one' | 'open'; // New field to distinguish debate types
  arguments: IArgument[];
  supportVotes: number;
  opposeVotes: number;
  userVotes: Array<{ userId: string; position: 'support' | 'oppose' }>;
  isLive: boolean;
  status: 'draft' | 'active' | 'completed' | 'cancelled';
  startTime?: Date;
  endTime?: Date;

  // Voice debate features
  voiceDebate?: IVoiceDebateState;

  createdAt: Date;
  updatedAt: Date;
}

const ReferenceSchema = new Schema({
  id: { type: String, required: true },
  url: { type: String, required: true },
  title: { type: String, required: true },
  description: { type: String },
  type: {
    type: String,
    enum: ['academic', 'news', 'government', 'organization', 'other'],
    default: 'other'
  },
  credibilityScore: { type: Number, min: 1, max: 10 }
});

const ParticipantSchema = new Schema({
  id: { type: String, required: true },
  name: { type: String, required: true },
  avatarUrl: { type: String, required: true }
});

const EvidenceSchema = new Schema({
  id: { type: String, required: true },
  type: {
    type: String,
    enum: ['statistic', 'study', 'expert_opinion', 'case_study', 'historical_fact', 'other'],
    required: true
  },
  content: { type: String, required: true },
  source: { type: ReferenceSchema, required: true },
  verificationStatus: {
    type: String,
    enum: ['verified', 'pending', 'disputed', 'unverified'],
    default: 'unverified'
  },
  addedBy: { type: ParticipantSchema, required: true },
  addedAt: { type: Date, default: Date.now },
  // Community verification voting
  accurateVotes: { type: Number, default: 0 },
  inaccurateVotes: { type: Number, default: 0 },
  totalVotes: { type: Number, default: 0 },
  verificationScore: { type: Number, default: 0 },
  votedBy: [{
    userId: { type: String, required: true },
    vote: { type: String, enum: ['accurate', 'inaccurate'], required: true }
  }]
});

// CommentSchema needs to be defined recursively for replies
const CommentSchema: Schema = new Schema({
  id: { type: String, required: true },
  author: { type: ParticipantSchema, required: true },
  text: { type: String, required: true },
  parentCommentId: { type: String },
  upvotes: { type: Number, default: 0 },
  downvotes: { type: Number, default: 0 },
  votes: { type: Number, default: 0 },
  userVotes: [{
    userId: { type: String, required: true },
    vote: { type: String, enum: ['up', 'down'], required: true }
  }],
  reportedBy: [{ type: String }], // Array of user IDs who reported this comment
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Enable recursive replies
CommentSchema.add({ replies: [CommentSchema] });

// ArgumentSchema needs to be defined recursively for sub-arguments
const ArgumentSchema: Schema = new Schema({
  id: { type: String, required: true },
  author: { type: ParticipantSchema, required: true },
  text: { type: String, required: true },
  references: [ReferenceSchema],
  evidence: [EvidenceSchema],
  side: { type: String, enum: ['FOR', 'AGAINST'], required: true },
  votes: { type: Number, default: 0 },
  upvotes: { type: Number, default: 0 },
  downvotes: { type: Number, default: 0 },
  userVotes: [{
    userId: { type: String, required: true },
    vote: { type: String, enum: ['up', 'down'], required: true }
  }],
  parentArgumentId: { type: String },
  subArguments: [{ type: Schema.Types.Mixed }], // Will be populated with ArgumentSchema
  comments: [CommentSchema], // Discussion comments for this argument
  depth: { type: Number, default: 0 },
  strengthScore: { type: Number, default: 0 },
  isChallenge: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Enable recursive sub-arguments
ArgumentSchema.add({ subArguments: [ArgumentSchema] });

const ClaimSchema = new Schema({
  author: { type: ParticipantSchema, required: true },
  text: { type: String, required: true },
  references: [ReferenceSchema]
});

const VoiceSpeakerSchema = new Schema({
  participant: { type: ParticipantSchema, required: true },
  side: { type: String, enum: ['FOR', 'AGAINST'], required: true },
  timeAllocated: { type: Number, required: true, default: 120 }, // 2 minutes default
  timeUsed: { type: Number, default: 0 },
  timeRemaining: { type: Number, required: true },
  isSpeaking: { type: Boolean, default: false },
  isMuted: { type: Boolean, default: false },
  joinedAt: { type: Date, default: Date.now }
});

const VoiceDebateSettingsSchema = new Schema({
  isVoiceEnabled: { type: Boolean, default: false },
  defaultSpeakingTime: { type: Number, default: 120 }, // 2 minutes default
  maxSpeakersPerSide: { type: Number, default: 5 },
  allowSpectatorVoice: { type: Boolean, default: true },
  autoRotateSpeakers: { type: Boolean, default: true },
  mutualExclusionEnabled: { type: Boolean, default: true }
});

const VoiceDebateStateSchema = new Schema({
  settings: { type: VoiceDebateSettingsSchema, required: true },
  speakerQueue: {
    FOR: [VoiceSpeakerSchema],
    AGAINST: [VoiceSpeakerSchema]
  },
  currentSpeaker: { type: VoiceSpeakerSchema },
  isVoiceActive: { type: Boolean, default: false },
  voiceStartedAt: { type: Date }
});

const DebateSchema = new Schema({
  title: { type: String, required: true },
  description: { type: String, required: true },
  category: {
    type: String,
    enum: ['politics', 'technology', 'science', 'philosophy', 'economics', 'environment', 'health', 'education', 'sports', 'entertainment'],
    required: true
  },
  claim: { type: ClaimSchema, required: true },
  participants: [ParticipantSchema],
  proponent: { type: ParticipantSchema, required: true },
  opponent: { type: ParticipantSchema, required: false }, // Optional for open debates
  type: {
    type: String,
    enum: ['one-on-one', 'open'],
    required: true,
    default: 'open'
  },
  arguments: [ArgumentSchema],
  supportVotes: { type: Number, default: 0 },
  opposeVotes: { type: Number, default: 0 },
  userVotes: [{
    userId: { type: String, required: true },
    position: { type: String, enum: ['support', 'oppose'], required: true }
  }],
  isLive: { type: Boolean, default: false },
  status: {
    type: String,
    enum: ['draft', 'active', 'completed', 'cancelled'],
    default: 'draft'
  },
  startTime: { type: Date },
  endTime: { type: Date },
  voiceDebate: { type: VoiceDebateStateSchema }
}, {
  timestamps: true,
  toJSON: {
    transform: function(_doc: any, ret: any) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

export default mongoose.model<IDebate>('Debate', DebateSchema);
