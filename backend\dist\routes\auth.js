"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const authController_1 = require("../controllers/authController");
const auth_1 = require("../middleware/auth");
const validate_1 = require("../middleware/validate");
const router = express_1.default.Router();
router.post('/register', [
    (0, express_validator_1.body)('username')
        .trim()
        .isLength({ min: 3, max: 30 })
        .matches(/^[a-zA-Z0-9_]+$/)
        .withMessage('Username can only contain letters, numbers, and underscores'),
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail(),
    (0, express_validator_1.body)('password')
        .isLength({ min: 6 })
        .withMessage('Password must be at least 6 characters long'),
    (0, express_validator_1.body)('firstName')
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('First name is required'),
    (0, express_validator_1.body)('lastName')
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Last name is required'),
    validate_1.validate
], authController_1.register);
router.post('/login', [
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail(),
    (0, express_validator_1.body)('password')
        .notEmpty()
        .withMessage('Password is required'),
    validate_1.validate
], authController_1.login);
router.get('/profile', auth_1.auth, authController_1.getProfile);
router.put('/profile', [
    auth_1.auth,
    (0, express_validator_1.body)('firstName')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 }),
    (0, express_validator_1.body)('lastName')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 }),
    (0, express_validator_1.body)('bio')
        .optional()
        .trim()
        .isLength({ max: 500 }),
    (0, express_validator_1.body)('avatarUrl')
        .optional()
        .isURL(),
    (0, express_validator_1.body)('privacySettings.displayName')
        .optional()
        .isIn(['username', 'firstName', 'fullName']),
    validate_1.validate
], authController_1.updateProfile);
exports.default = router;
//# sourceMappingURL=auth.js.map