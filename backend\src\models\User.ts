import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcryptjs';

export interface ITimeBlockBalance {
  debateId: string;
  timeBlocksRemaining: number;
  timeBlocksUsed: number;
  lastUpdated: Date;
}

export interface IUser extends Document {
  username: string;
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
  bio?: string;
  isVerified: boolean;
  role: 'user' | 'moderator' | 'admin';
  privacySettings: {
    displayName: 'username' | 'firstName' | 'fullName';
  };
  debatesParticipated: string[];
  debatesCreated: string[];
  totalVotes: number;
  reputation: number;
  suspended?: boolean;
  suspensionReason?: string;
  suspendedAt?: Date;
  suspendedBy?: string;

  // Time block system for voice debates
  timeBlockBalances: ITimeBlockBalance[];
  totalTimeBlocksPurchased: number;

  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

const TimeBlockBalanceSchema = new Schema({
  debateId: {
    type: Schema.Types.ObjectId,
    ref: 'Debate',
    required: true
  },
  timeBlocksRemaining: {
    type: Number,
    default: 5, // Default 5 time blocks per debate
    min: 0
  },
  timeBlocksUsed: {
    type: Number,
    default: 0,
    min: 0
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  }
});

const UserSchema = new Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 30
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  firstName: {
    type: String,
    trim: true,
    maxlength: 50
  },
  lastName: {
    type: String,
    trim: true,
    maxlength: 50
  },
  privacySettings: {
    displayName: {
      type: String,
      enum: ['username', 'firstName', 'fullName'],
      default: 'username'
    }
  },
  avatarUrl: {
    type: String,
    default: function(this: IUser) {
      return `https://picsum.photos/seed/${this.username}/100`;
    }
  },
  bio: {
    type: String,
    maxlength: 500
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  role: {
    type: String,
    enum: ['user', 'moderator', 'admin'],
    default: 'user'
  },
  debatesParticipated: [{
    type: Schema.Types.ObjectId,
    ref: 'Debate'
  }],
  debatesCreated: [{
    type: Schema.Types.ObjectId,
    ref: 'Debate'
  }],
  totalVotes: {
    type: Number,
    default: 0
  },
  reputation: {
    type: Number,
    default: 0
  },
  suspended: {
    type: Boolean,
    default: false
  },
  suspensionReason: {
    type: String,
    maxlength: 500
  },
  suspendedAt: {
    type: Date
  },
  suspendedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },

  // Time block system for voice debates
  timeBlockBalances: [TimeBlockBalanceSchema],
  totalTimeBlocksPurchased: {
    type: Number,
    default: 0,
    min: 0
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc: any, ret: any) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// Hash password before saving
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Compare password method
UserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

export default mongoose.model<IUser>('User', UserSchema);
