"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getContentReports = exports.reportContent = exports.validateArgumentDepth = exports.rateLimitContent = exports.validateEvidenceQuality = exports.validateArgumentQuality = void 0;
const contentReports = new Map();
const INAPPROPRIATE_WORDS = [
    'spam', 'scam', 'fake', 'lie', 'stupid', 'idiot', 'hate', 'kill', 'die'
];
const validateArgumentQuality = (req, res, next) => {
    const { text } = req.body;
    if (!text || typeof text !== 'string') {
        return res.status(400).json({
            success: false,
            message: 'Argument text is required'
        });
    }
    const cleanText = text.trim().toLowerCase();
    const hasInappropriateContent = INAPPROPRIATE_WORDS.some(word => cleanText.includes(word.toLowerCase()));
    if (hasInappropriateContent) {
        return res.status(400).json({
            success: false,
            message: 'Argument contains inappropriate content. Please revise your argument to maintain respectful discourse.'
        });
    }
    const wordCount = text.trim().split(/\s+/).length;
    if (wordCount < 5) {
        return res.status(400).json({
            success: false,
            message: 'Arguments must be at least 5 words long to ensure meaningful contribution.'
        });
    }
    const uppercaseRatio = (text.match(/[A-Z]/g) || []).length / text.length;
    if (uppercaseRatio > 0.5 && text.length > 20) {
        return res.status(400).json({
            success: false,
            message: 'Please avoid excessive capitalization. Use normal case for better readability.'
        });
    }
    const words = cleanText.split(/\s+/);
    const uniqueWords = new Set(words);
    const repetitionRatio = uniqueWords.size / words.length;
    if (repetitionRatio < 0.3 && words.length > 10) {
        return res.status(400).json({
            success: false,
            message: 'Argument appears to be repetitive. Please provide more varied content.'
        });
    }
    next();
    return;
};
exports.validateArgumentQuality = validateArgumentQuality;
const validateEvidenceQuality = (req, res, next) => {
    const { content, source, type } = req.body;
    if (!content || typeof content !== 'string') {
        return res.status(400).json({
            success: false,
            message: 'Evidence content is required'
        });
    }
    const cleanContent = content.trim().toLowerCase();
    if (!source || !source.url || !source.title) {
        return res.status(400).json({
            success: false,
            message: 'Evidence must include a valid source with URL and title.'
        });
    }
    try {
        const suspiciousDomains = ['bit.ly', 'tinyurl.com', 'goo.gl', 't.co'];
        const urlDomain = new URL(source.url).hostname.toLowerCase();
        if (suspiciousDomains.some(domain => urlDomain.includes(domain))) {
            return res.status(400).json({
                success: false,
                message: 'Please provide direct links to sources rather than shortened URLs for transparency.'
            });
        }
    }
    catch (error) {
        return res.status(400).json({
            success: false,
            message: 'Invalid URL format in source.'
        });
    }
    const typeKeywords = {
        'statistic': ['percent', '%', 'number', 'data', 'survey', 'poll'],
        'study': ['study', 'research', 'analysis', 'investigation', 'experiment'],
        'expert_opinion': ['expert', 'professor', 'doctor', 'specialist', 'authority'],
        'case_study': ['case', 'example', 'instance', 'situation', 'scenario'],
        'historical_fact': ['history', 'historical', 'past', 'year', 'century', 'decade']
    };
    if (type !== 'other' && typeKeywords[type]) {
        const keywords = typeKeywords[type];
        const hasRelevantKeywords = keywords.some(keyword => cleanContent.includes(keyword) || source.title.toLowerCase().includes(keyword));
        if (!hasRelevantKeywords) {
            return res.status(400).json({
                success: false,
                message: `Evidence type "${type}" doesn't seem to match the content. Please verify the evidence type or provide more specific content.`
            });
        }
    }
    next();
    return;
};
exports.validateEvidenceQuality = validateEvidenceQuality;
const userActionCounts = new Map();
const rateLimitContent = (maxActions = 10, windowMinutes = 60) => {
    return (req, res, next) => {
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }
        const now = Date.now();
        const windowMs = windowMinutes * 60 * 1000;
        const userActions = userActionCounts.get(userId);
        if (!userActions || now > userActions.resetTime) {
            userActionCounts.set(userId, {
                count: 1,
                resetTime: now + windowMs
            });
            return next();
        }
        if (userActions.count >= maxActions) {
            const resetIn = Math.ceil((userActions.resetTime - now) / 60000);
            return res.status(429).json({
                success: false,
                message: `Rate limit exceeded. You can add more content in ${resetIn} minutes.`
            });
        }
        userActions.count++;
        next();
    };
};
exports.rateLimitContent = rateLimitContent;
const validateArgumentDepth = (maxDepth = 5) => {
    return async (req, res, next) => {
        const { debateId, argumentId } = req.params;
        try {
            const Debate = require('../models/Debate').default;
            const debate = await Debate.findById(debateId);
            if (!debate) {
                res.status(404).json({
                    success: false,
                    message: 'Debate not found'
                });
                return;
            }
            const findArgumentDepth = (args, targetId, currentDepth = 0) => {
                for (const arg of args) {
                    if (arg.id === targetId) {
                        return currentDepth;
                    }
                    if (arg.subArguments && arg.subArguments.length > 0) {
                        const depth = findArgumentDepth(arg.subArguments, targetId, currentDepth + 1);
                        if (depth !== -1) {
                            return depth;
                        }
                    }
                }
                return -1;
            };
            const parentDepth = findArgumentDepth(debate.arguments, argumentId);
            if (parentDepth === -1) {
                res.status(404).json({
                    success: false,
                    message: 'Parent argument not found'
                });
                return;
            }
            const newDepth = parentDepth + 1;
            if (newDepth >= maxDepth) {
                res.status(400).json({
                    success: false,
                    message: `Maximum argument depth of ${maxDepth} levels reached. Consider starting a new thread for deeper discussion.`
                });
                return;
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
exports.validateArgumentDepth = validateArgumentDepth;
const reportContent = async (req, res, next) => {
    try {
        const { contentId, contentType, reason } = req.body;
        const user = req.user;
        if (!contentId || !contentType || !reason) {
            res.status(400).json({
                success: false,
                message: 'Content ID, type, and reason are required'
            });
            return;
        }
        const validReasons = [
            'inappropriate_language',
            'harassment',
            'spam',
            'misinformation',
            'off_topic',
            'personal_attack',
            'other'
        ];
        if (!validReasons.includes(reason)) {
            res.status(400).json({
                success: false,
                message: 'Invalid report reason'
            });
            return;
        }
        const reportKey = `${contentType}:${contentId}`;
        const existingReports = contentReports.get(reportKey) || [];
        const alreadyReported = existingReports.some(report => report.reportedBy === user.id);
        if (alreadyReported) {
            res.status(400).json({
                success: false,
                message: 'You have already reported this content'
            });
            return;
        }
        existingReports.push({
            reportedBy: user.id,
            reason,
            timestamp: Date.now()
        });
        contentReports.set(reportKey, existingReports);
        if (existingReports.length >= 3) {
            console.log(`Content ${reportKey} has received ${existingReports.length} reports and should be reviewed`);
        }
        res.json({
            success: true,
            message: 'Content reported successfully. Thank you for helping maintain community standards.'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.reportContent = reportContent;
const getContentReports = async (req, res, next) => {
    try {
        const user = req.user;
        if (user.role !== 'moderator' && user.role !== 'admin') {
            res.status(403).json({
                success: false,
                message: 'Access denied. Moderator privileges required.'
            });
            return;
        }
        const reports = [];
        for (const [key, reportList] of contentReports.entries()) {
            const [contentType, contentId] = key.split(':');
            reports.push({
                contentId,
                contentType,
                reportCount: reportList.length,
                reports: reportList
            });
        }
        reports.sort((a, b) => b.reportCount - a.reportCount);
        res.json({
            success: true,
            data: reports
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getContentReports = getContentReports;
//# sourceMappingURL=moderation.js.map