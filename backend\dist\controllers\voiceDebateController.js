"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.releaseMutualExclusion = exports.reportMutualExclusion = exports.updateVoiceSettings = exports.getVoiceDebateState = exports.grantTimeBlocks = exports.allocateTime = exports.getTimeBlocks = exports.giveTime = exports.nextSpeaker = exports.leaveVoiceQueue = exports.joinVoiceQueue = exports.initializeVoiceDebate = void 0;
const voiceDebateService_1 = require("../services/voiceDebateService");
const Debate_1 = __importDefault(require("../models/Debate"));
const User_1 = __importDefault(require("../models/User"));
const userDisplay_1 = require("../utils/userDisplay");
const initializeVoiceDebate = async (req, res, next) => {
    try {
        const { id: debateId } = req.params;
        const { settings } = req.body;
        const user = req.user;
        const debate = await Debate_1.default.findById(debateId);
        if (!debate) {
            res.status(404).json({ success: false, message: 'Debate not found' });
            return;
        }
        const canInitialize = debate.proponent.id === user.id ||
            (debate.opponent && debate.opponent.id === user.id) ||
            debate.type === 'open';
        if (!canInitialize) {
            res.status(403).json({ success: false, message: 'Not authorized to initialize voice debate' });
            return;
        }
        const updatedDebate = await voiceDebateService_1.VoiceDebateService.initializeVoiceDebate(debateId, settings);
        if (!updatedDebate) {
            res.status(500).json({ success: false, message: 'Failed to initialize voice debate' });
            return;
        }
        res.json({
            success: true,
            data: updatedDebate.voiceDebate,
            message: 'Voice debate initialized successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.initializeVoiceDebate = initializeVoiceDebate;
const joinVoiceQueue = async (req, res, next) => {
    try {
        const { id: debateId } = req.params;
        const { side, timeAllocation } = req.body;
        const user = req.user;
        const userDoc = await User_1.default.findById(user.id);
        if (!userDoc) {
            res.status(404).json({ success: false, message: 'User not found' });
            return;
        }
        const participant = {
            id: userDoc.id,
            name: (0, userDisplay_1.getDisplayName)(userDoc),
            avatarUrl: userDoc.avatarUrl || '/default-avatar.png'
        };
        const result = await voiceDebateService_1.VoiceDebateService.joinVoiceQueue(debateId, participant, side, timeAllocation);
        if (result.success) {
            res.json({
                success: true,
                data: result.speaker,
                message: result.message
            });
        }
        else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    }
    catch (error) {
        next(error);
    }
};
exports.joinVoiceQueue = joinVoiceQueue;
const leaveVoiceQueue = async (req, res, next) => {
    try {
        const { id: debateId } = req.params;
        const user = req.user;
        const result = await voiceDebateService_1.VoiceDebateService.leaveVoiceQueue(debateId, user.id);
        if (result.success) {
            res.json({
                success: true,
                message: result.message
            });
        }
        else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    }
    catch (error) {
        next(error);
    }
};
exports.leaveVoiceQueue = leaveVoiceQueue;
const nextSpeaker = async (req, res, next) => {
    try {
        const { id: debateId } = req.params;
        const user = req.user;
        const debate = await Debate_1.default.findById(debateId);
        if (!debate) {
            res.status(404).json({ success: false, message: 'Debate not found' });
            return;
        }
        const hasPermission = debate.proponent.id === user.id ||
            (debate.opponent && debate.opponent.id === user.id) ||
            user.role === 'moderator' ||
            user.role === 'admin';
        if (!hasPermission) {
            res.status(403).json({ success: false, message: 'Insufficient permissions' });
            return;
        }
        const result = await voiceDebateService_1.VoiceDebateService.rotateToNextSpeaker(debateId);
        if (result.success) {
            res.json({
                success: true,
                data: result.speaker,
                message: 'Rotated to next speaker'
            });
        }
        else {
            res.status(400).json({
                success: false,
                message: 'No speakers available in queue'
            });
        }
    }
    catch (error) {
        next(error);
    }
};
exports.nextSpeaker = nextSpeaker;
const giveTime = async (req, res, next) => {
    try {
        const { id: debateId } = req.params;
        const { speakerId } = req.body;
        const user = req.user;
        if (!speakerId) {
            res.status(400).json({
                success: false,
                message: 'Speaker ID is required'
            });
            return;
        }
        const result = await voiceDebateService_1.VoiceDebateService.useTimeBlock(user.id, debateId, speakerId);
        if (result.success) {
            res.json({
                success: true,
                message: result.message
            });
        }
        else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    }
    catch (error) {
        next(error);
    }
};
exports.giveTime = giveTime;
const getTimeBlocks = async (req, res, next) => {
    try {
        const { id: debateId } = req.params;
        const user = req.user;
        const balance = await voiceDebateService_1.VoiceDebateService.getTimeBlockBalance(user.id, debateId);
        if (balance) {
            res.json({
                success: true,
                data: balance
            });
        }
        else {
            const initResult = await voiceDebateService_1.VoiceDebateService.initializeTimeBlocks(user.id, debateId);
            if (initResult.success) {
                const newBalance = await voiceDebateService_1.VoiceDebateService.getTimeBlockBalance(user.id, debateId);
                res.json({
                    success: true,
                    data: newBalance
                });
            }
            else {
                res.status(400).json({
                    success: false,
                    message: initResult.message
                });
            }
        }
    }
    catch (error) {
        next(error);
    }
};
exports.getTimeBlocks = getTimeBlocks;
const allocateTime = async (req, res, next) => {
    try {
        const { id: debateId } = req.params;
        const { speakerId, additionalTime } = req.body;
        const user = req.user;
        if (!additionalTime || additionalTime <= 0 || additionalTime > 300) {
            res.status(400).json({
                success: false,
                message: 'Additional time must be between 1 and 300 seconds'
            });
            return;
        }
        if (user.role !== 'moderator' && user.role !== 'admin') {
            res.status(403).json({ success: false, message: 'Only moderators and admins can allocate time directly' });
            return;
        }
        const result = await voiceDebateService_1.VoiceDebateService.allocateTime(debateId, speakerId, additionalTime, user.id);
        if (result.success) {
            res.json({
                success: true,
                message: result.message
            });
        }
        else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    }
    catch (error) {
        next(error);
    }
};
exports.allocateTime = allocateTime;
const grantTimeBlocks = async (req, res, next) => {
    try {
        const { id: debateId } = req.params;
        const { userId, timeBlocks } = req.body;
        const user = req.user;
        if (user.role !== 'admin') {
            res.status(403).json({ success: false, message: 'Only admins can grant time blocks' });
            return;
        }
        if (!userId || !timeBlocks || timeBlocks <= 0) {
            res.status(400).json({
                success: false,
                message: 'User ID and positive time blocks amount required'
            });
            return;
        }
        const targetUser = await User_1.default.findById(userId);
        if (!targetUser) {
            res.status(404).json({ success: false, message: 'User not found' });
            return;
        }
        const balanceIndex = targetUser.timeBlockBalances.findIndex(balance => balance.debateId === debateId);
        if (balanceIndex === -1) {
            targetUser.timeBlockBalances.push({
                debateId,
                timeBlocksRemaining: timeBlocks,
                timeBlocksUsed: 0,
                lastUpdated: new Date()
            });
        }
        else {
            targetUser.timeBlockBalances[balanceIndex].timeBlocksRemaining += timeBlocks;
            targetUser.timeBlockBalances[balanceIndex].lastUpdated = new Date();
        }
        targetUser.totalTimeBlocksPurchased += timeBlocks;
        await targetUser.save();
        res.json({
            success: true,
            message: `Granted ${timeBlocks} time blocks to ${targetUser.username}`,
            data: {
                userId: targetUser.id,
                username: targetUser.username,
                timeBlocksGranted: timeBlocks,
                newBalance: targetUser.timeBlockBalances.find(b => b.debateId === debateId)?.timeBlocksRemaining || 0
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.grantTimeBlocks = grantTimeBlocks;
const getVoiceDebateState = async (req, res, next) => {
    try {
        const { id: debateId } = req.params;
        const debate = await Debate_1.default.findById(debateId);
        if (!debate) {
            res.status(404).json({ success: false, message: 'Debate not found' });
            return;
        }
        console.log(`getVoiceDebateState for debate ${debateId}:`);
        if (debate.voiceDebate) {
            console.log('Voice debate exists with speaker queues:', {
                FOR: debate.voiceDebate.speakerQueue.FOR.map(s => ({ id: s.participant.id, name: s.participant.name })),
                AGAINST: debate.voiceDebate.speakerQueue.AGAINST.map(s => ({ id: s.participant.id, name: s.participant.name })),
                isVoiceActive: debate.voiceDebate.isVoiceActive
            });
        }
        else {
            console.log('No voice debate found');
        }
        res.json({
            success: true,
            data: debate.voiceDebate || null
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getVoiceDebateState = getVoiceDebateState;
const updateVoiceSettings = async (req, res, next) => {
    try {
        const { id: debateId } = req.params;
        const { settings } = req.body;
        const user = req.user;
        const debate = await Debate_1.default.findById(debateId);
        if (!debate) {
            res.status(404).json({ success: false, message: 'Debate not found' });
            return;
        }
        if (debate.proponent.id !== user.id) {
            res.status(403).json({ success: false, message: 'Only debate creator can update voice settings' });
            return;
        }
        if (!debate.voiceDebate) {
            res.status(400).json({ success: false, message: 'Voice debate not initialized' });
            return;
        }
        debate.voiceDebate.settings = { ...debate.voiceDebate.settings, ...settings };
        await debate.save();
        res.json({
            success: true,
            data: debate.voiceDebate.settings,
            message: 'Voice settings updated successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.updateVoiceSettings = updateVoiceSettings;
const reportMutualExclusion = async (req, res, next) => {
    try {
        const { id: debateId } = req.params;
        const result = await voiceDebateService_1.VoiceDebateService.handleMutualExclusion(debateId);
        if (result.success) {
            res.json({
                success: true,
                message: result.message
            });
        }
        else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    }
    catch (error) {
        next(error);
    }
};
exports.reportMutualExclusion = reportMutualExclusion;
const releaseMutualExclusion = async (req, res, next) => {
    try {
        const { id: debateId } = req.params;
        const result = await voiceDebateService_1.VoiceDebateService.handleMutualExclusionRelease(debateId);
        if (result.success) {
            res.json({
                success: true,
                message: result.message
            });
        }
        else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    }
    catch (error) {
        next(error);
    }
};
exports.releaseMutualExclusion = releaseMutualExclusion;
//# sourceMappingURL=voiceDebateController.js.map