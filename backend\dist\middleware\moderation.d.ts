import { Response, NextFunction } from 'express';
import { AuthRequest } from './auth';
export declare const validateArgumentQuality: (req: AuthRequest, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
export declare const validateEvidenceQuality: (req: AuthRequest, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
export declare const rateLimitContent: (maxActions?: number, windowMinutes?: number) => (req: AuthRequest, res: Response, next: NextFunction) => void | Response<any, Record<string, any>>;
export declare const validateArgumentDepth: (maxDepth?: number) => (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const reportContent: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getContentReports: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=moderation.d.ts.map