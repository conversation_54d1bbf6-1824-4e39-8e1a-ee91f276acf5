import { Request, Response, NextFunction } from 'express';
import { AuthRequest } from '../middleware/auth';
import { VoiceDebateService } from '../services/voiceDebateService';
import Debate from '../models/Debate';
import User from '../models/User';
import { getDisplayName } from '../utils/userDisplay';

/**
 * Initialize voice debate for a debate
 * POST /api/debates/:id/voice/initialize
 */
export const initializeVoiceDebate = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id: debateId } = req.params;
    const { settings } = req.body;
    const user = req.user!;

    // Check if user can initialize voice debate
    const debate = await Debate.findById(debateId);
    if (!debate) {
      res.status(404).json({ success: false, message: 'Debate not found' });
      return;
    }

    // Allow debate creator or participants to initialize voice debate
    const canInitialize = debate.proponent.id === user.id ||
                         (debate.opponent && debate.opponent.id === user.id) ||
                         debate.type === 'open'; // For open debates, anyone can initialize

    if (!canInitialize) {
      res.status(403).json({ success: false, message: 'Not authorized to initialize voice debate' });
      return;
    }

    const updatedDebate = await VoiceDebateService.initializeVoiceDebate(debateId, settings);
    if (!updatedDebate) {
      res.status(500).json({ success: false, message: 'Failed to initialize voice debate' });
      return;
    }

    res.json({
      success: true,
      data: updatedDebate.voiceDebate,
      message: 'Voice debate initialized successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Join voice debate queue
 * POST /api/debates/:id/voice/join
 */
export const joinVoiceQueue = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id: debateId } = req.params;
    const { side, timeAllocation } = req.body;
    const user = req.user!;

    // Get user details for participant object
    const userDoc = await User.findById(user.id);
    if (!userDoc) {
      res.status(404).json({ success: false, message: 'User not found' });
      return;
    }

    const participant = {
      id: userDoc.id,
      name: getDisplayName(userDoc),
      avatarUrl: userDoc.avatarUrl || '/default-avatar.png'
    };

    const result = await VoiceDebateService.joinVoiceQueue(debateId, participant, side, timeAllocation);

    if (result.success) {
      res.json({
        success: true,
        data: result.speaker,
        message: result.message
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message
      });
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Leave voice debate queue
 * POST /api/debates/:id/voice/leave
 */
export const leaveVoiceQueue = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id: debateId } = req.params;
    const user = req.user!;

    const result = await VoiceDebateService.leaveVoiceQueue(debateId, user.id);

    if (result.success) {
      res.json({
        success: true,
        message: result.message
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message
      });
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Rotate to next speaker
 * POST /api/debates/:id/voice/next-speaker
 */
export const nextSpeaker = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id: debateId } = req.params;
    const user = req.user!;

    // Check if user has permission to control speakers (debate creator or moderator)
    const debate = await Debate.findById(debateId);
    if (!debate) {
      res.status(404).json({ success: false, message: 'Debate not found' });
      return;
    }

    const hasPermission = debate.proponent.id === user.id || 
                         (debate.opponent && debate.opponent.id === user.id) ||
                         user.role === 'moderator' || 
                         user.role === 'admin';

    if (!hasPermission) {
      res.status(403).json({ success: false, message: 'Insufficient permissions' });
      return;
    }

    const result = await VoiceDebateService.rotateToNextSpeaker(debateId);

    if (result.success) {
      res.json({
        success: true,
        data: result.speaker,
        message: 'Rotated to next speaker'
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'No speakers available in queue'
      });
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Use a time block to give time to a speaker
 * POST /api/debates/:id/voice/give-time
 */
export const giveTime = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id: debateId } = req.params;
    const { speakerId } = req.body;
    const user = req.user!;

    if (!speakerId) {
      res.status(400).json({
        success: false,
        message: 'Speaker ID is required'
      });
      return;
    }

    const result = await VoiceDebateService.useTimeBlock(user.id, debateId, speakerId);

    if (result.success) {
      res.json({
        success: true,
        message: result.message
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message
      });
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Get user's time block balance for a debate
 * GET /api/debates/:id/voice/time-blocks
 */
export const getTimeBlocks = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id: debateId } = req.params;
    const user = req.user!;

    const balance = await VoiceDebateService.getTimeBlockBalance(user.id, debateId);

    if (balance) {
      res.json({
        success: true,
        data: balance
      });
    } else {
      // Initialize time blocks if not found
      const initResult = await VoiceDebateService.initializeTimeBlocks(user.id, debateId);
      if (initResult.success) {
        const newBalance = await VoiceDebateService.getTimeBlockBalance(user.id, debateId);
        res.json({
          success: true,
          data: newBalance
        });
      } else {
        res.status(400).json({
          success: false,
          message: initResult.message
        });
      }
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Legacy allocate time endpoint (for moderators/admins only)
 * POST /api/debates/:id/voice/allocate-time
 */
export const allocateTime = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id: debateId } = req.params;
    const { speakerId, additionalTime } = req.body;
    const user = req.user!;

    // Validate additional time
    if (!additionalTime || additionalTime <= 0 || additionalTime > 300) { // Max 5 minutes
      res.status(400).json({
        success: false,
        message: 'Additional time must be between 1 and 300 seconds'
      });
      return;
    }

    // Only moderators and admins can use this endpoint
    if (user.role !== 'moderator' && user.role !== 'admin') {
      res.status(403).json({ success: false, message: 'Only moderators and admins can allocate time directly' });
      return;
    }

    const result = await VoiceDebateService.allocateTime(debateId, speakerId, additionalTime, user.id);

    if (result.success) {
      res.json({
        success: true,
        message: result.message
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message
      });
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Grant time blocks to a user (admin only)
 * POST /api/debates/:id/voice/grant-time-blocks
 */
export const grantTimeBlocks = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id: debateId } = req.params;
    const { userId, timeBlocks } = req.body;
    const user = req.user!;

    // Only admins can grant time blocks
    if (user.role !== 'admin') {
      res.status(403).json({ success: false, message: 'Only admins can grant time blocks' });
      return;
    }

    if (!userId || !timeBlocks || timeBlocks <= 0) {
      res.status(400).json({
        success: false,
        message: 'User ID and positive time blocks amount required'
      });
      return;
    }

    const targetUser = await User.findById(userId);
    if (!targetUser) {
      res.status(404).json({ success: false, message: 'User not found' });
      return;
    }

    // Find or create time block balance for this debate
    const balanceIndex = targetUser.timeBlockBalances.findIndex(balance => balance.debateId === debateId);

    if (balanceIndex === -1) {
      // Create new balance
      targetUser.timeBlockBalances.push({
        debateId,
        timeBlocksRemaining: timeBlocks,
        timeBlocksUsed: 0,
        lastUpdated: new Date()
      });
    } else {
      // Update existing balance
      targetUser.timeBlockBalances[balanceIndex].timeBlocksRemaining += timeBlocks;
      targetUser.timeBlockBalances[balanceIndex].lastUpdated = new Date();
    }

    targetUser.totalTimeBlocksPurchased += timeBlocks;
    await targetUser.save();

    res.json({
      success: true,
      message: `Granted ${timeBlocks} time blocks to ${targetUser.username}`,
      data: {
        userId: targetUser.id,
        username: targetUser.username,
        timeBlocksGranted: timeBlocks,
        newBalance: targetUser.timeBlockBalances.find(b => b.debateId === debateId)?.timeBlocksRemaining || 0
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get voice debate state
 * GET /api/debates/:id/voice
 */
export const getVoiceDebateState = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id: debateId } = req.params;

    const debate = await Debate.findById(debateId);
    if (!debate) {
      res.status(404).json({ success: false, message: 'Debate not found' });
      return;
    }

    console.log(`getVoiceDebateState for debate ${debateId}:`);
    if (debate.voiceDebate) {
      console.log('Voice debate exists with speaker queues:', {
        FOR: debate.voiceDebate.speakerQueue.FOR.map(s => ({ id: s.participant.id, name: s.participant.name })),
        AGAINST: debate.voiceDebate.speakerQueue.AGAINST.map(s => ({ id: s.participant.id, name: s.participant.name })),
        isVoiceActive: debate.voiceDebate.isVoiceActive
      });
    } else {
      console.log('No voice debate found');
    }

    res.json({
      success: true,
      data: debate.voiceDebate || null
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update voice debate settings
 * PUT /api/debates/:id/voice/settings
 */
export const updateVoiceSettings = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id: debateId } = req.params;
    const { settings } = req.body;
    const user = req.user!;

    // Check if user is the debate creator
    const debate = await Debate.findById(debateId);
    if (!debate) {
      res.status(404).json({ success: false, message: 'Debate not found' });
      return;
    }

    if (debate.proponent.id !== user.id) {
      res.status(403).json({ success: false, message: 'Only debate creator can update voice settings' });
      return;
    }

    if (!debate.voiceDebate) {
      res.status(400).json({ success: false, message: 'Voice debate not initialized' });
      return;
    }

    // Update settings
    debate.voiceDebate.settings = { ...debate.voiceDebate.settings, ...settings };
    await debate.save();

    res.json({
      success: true,
      data: debate.voiceDebate.settings,
      message: 'Voice settings updated successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Report mutual exclusion (multiple speakers)
 * POST /api/debates/:id/voice/mutual-exclusion
 */
export const reportMutualExclusion = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id: debateId } = req.params;

    const result = await VoiceDebateService.handleMutualExclusion(debateId);

    if (result.success) {
      res.json({
        success: true,
        message: result.message
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message
      });
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Release mutual exclusion (when speakers stop talking simultaneously)
 * POST /api/debates/:id/voice/mutual-exclusion/release
 */
export const releaseMutualExclusion = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id: debateId } = req.params;

    const result = await VoiceDebateService.handleMutualExclusionRelease(debateId);

    if (result.success) {
      res.json({
        success: true,
        message: result.message
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message
      });
    }
  } catch (error) {
    next(error);
  }
};
