import React, { useState, useEffect, useCallback } from 'react';
import { Participant, Debate, VoiceDebateState, VoiceSpeaker } from '../../types';
import { MicrophoneIcon, ClockIcon } from '../ui/icons';
import { useSocket } from '../../contexts/SocketContext';
import { useAuth } from '../../contexts/AuthContext';
import { debatesAPI } from '../../utils/api';

interface LiveDebateStageProps {
  debate: Debate;
  onRefresh: () => void;
}

type PermissionStatus = 'idle' | 'requesting' | 'granted' | 'denied';

// Helper function to format time
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};






export const LiveDebateStage: React.FC<LiveDebateStageProps> = ({ debate, onRefresh }) => {
  const { user } = useAuth();
  const { socket } = useSocket();
  const [permission, setPermission] = useState<PermissionStatus>('idle');
  const [voiceState, setVoiceState] = useState<VoiceDebateState | null>(debate.voiceDebate || null);



  // Time tracking state
  const [currentSpeakerTime, setCurrentSpeakerTime] = useState<number | null>(null);
  const [timeWarning, setTimeWarning] = useState<'none' | 'warning' | 'critical'>('none');

  // Time block state
  const [timeBlocksRemaining, setTimeBlocksRemaining] = useState<number>(0);
  const [timeBlocksUsed, setTimeBlocksUsed] = useState<number>(0);



  // Debug logging
  console.log('LiveDebateStage Debug:', {
    debateType: debate.type,
    user: user?.id,
    userId: user?.id,
    proponentId: debate.proponent.id,
    opponentId: debate.opponent?.id,
    canJoinVoice: user && (debate.type === 'open' || debate.proponent.id === user.id || (debate.opponent && debate.opponent.id === user.id)),
    voiceState: !!voiceState,
    permission,
    isRendering: true
  });

  const requestMicPermission = useCallback(async () => {
    setPermission('requesting');
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      setPermission('granted');
    } catch (err) {
      console.error("Microphone permission denied:", err);
      setPermission('denied');
    }
  }, []);

  // Check if user can manage voice debate
  const canManageVoice = user && (
    debate.proponent.id === user.id ||
    (debate.opponent && debate.opponent.id === user.id) ||
    user.role === 'moderator' ||
    user.role === 'admin'
  );

  // Check if user can allocate time (moderators only for legacy endpoint)
  const canAllocateTime = user && (user.role === 'moderator' || user.role === 'admin');

  // Check if user can join voice
  const canJoinVoice = user && (
    // For public debates, anyone can join
    debate.type === 'open' ||
    // For one-on-one debates, only participants can join
    debate.proponent.id === user.id ||
    (debate.opponent && debate.opponent.id === user.id)
  );

  // Load user's time block balance
  const loadTimeBlocks = useCallback(async () => {
    if (!user) return;

    try {
      const response = await debatesAPI.getTimeBlocks(debate.id);
      if (response.success) {
        setTimeBlocksRemaining(response.data.timeBlocksRemaining);
        setTimeBlocksUsed(response.data.timeBlocksUsed);
      }
    } catch (error) {
      console.error('Failed to load time blocks:', error);
    }
  }, [user, debate.id]);

  // Check if user is already in voice queue
  const isInVoiceQueue = voiceState && user && (
    voiceState.speakerQueue.FOR.some(s => s.participant.id === user.id) ||
    voiceState.speakerQueue.AGAINST.some(s => s.participant.id === user.id) ||
    (voiceState.currentSpeaker && voiceState.currentSpeaker.participant.id === user.id)
  );

  // Determine which side user should join for one-on-one debates
  const getUserSideForOneOnOne = (): 'FOR' | 'AGAINST' => {
    if (!user) return 'FOR';
    return debate.proponent.id === user.id ? 'FOR' : 'AGAINST';
  };

  // Initialize voice debate if not already done
  const initializeVoice = async () => {
    if (!voiceState && canManageVoice) {
      try {
        const response = await debatesAPI.initializeVoiceDebate(debate.id);
        if (response.success) {
          setVoiceState(response.data);
          onRefresh();
        }
      } catch (error) {
        console.error('Failed to initialize voice debate:', error);
      }
    }
  };

  // Join voice queue
  const joinVoiceQueue = async (side: 'FOR' | 'AGAINST') => {
    // Request microphone permission before joining
    if (permission !== 'granted') {
      setPermission('requesting');
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        stream.getTracks().forEach(track => track.stop());
        setPermission('granted');
      } catch (error) {
        console.error('Microphone permission denied:', error);
        setPermission('denied');
        alert('Microphone permission is required to join voice debate. Please allow microphone access and try again.');
        return;
      }
    }

    // Initialize voice debate if it doesn't exist
    if (!voiceState) {
      try {
        console.log('Initializing voice debate before joining...');
        const initResponse = await debatesAPI.initializeVoiceDebate(debate.id);
        if (initResponse.success) {
          setVoiceState(initResponse.data);
        } else {
          alert('Failed to initialize voice debate. Please try again.');
          return;
        }
      } catch (error) {
        console.error('Failed to initialize voice debate:', error);
        alert('Failed to initialize voice debate. Please try again.');
        return;
      }
    }

    try {
      console.log('Joining voice queue for side:', side);
      const response = await debatesAPI.joinVoiceQueue(debate.id, side);
      if (response.success) {
        console.log('Successfully joined voice queue');
        // The socket event will update the UI automatically
        // But also refresh to ensure we have the latest state
        onRefresh();
      }
    } catch (error) {
      console.error('Failed to join voice queue:', error);
      alert('Failed to join voice debate. Please try again.');
    }
  };

  // Leave voice queue
  const leaveVoiceQueue = async () => {
    try {
      console.log('Leaving voice queue...');
      const response = await debatesAPI.leaveVoiceQueue(debate.id);
      if (response.success) {
        console.log('Successfully left voice queue');
        // The socket event will update the UI automatically
        // But also refresh to ensure we have the latest state
        onRefresh();
      } else {
        console.error('Server error leaving voice queue:', response.message);
        alert(`Failed to leave voice queue: ${response.message}`);
      }
    } catch (error: any) {
      console.error('Failed to leave voice queue:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Unknown error';
      alert(`Failed to leave voice queue: ${errorMessage}`);
    }
  };

  // Load fresh voice state when component mounts or user changes
  const loadVoiceState = useCallback(async () => {
    try {
      const response = await debatesAPI.getVoiceDebateState(debate.id);
      if (response.success && response.data) {
        setVoiceState(response.data);
      } else {
        setVoiceState(null);
      }
    } catch (error) {
      console.error('Failed to load voice state:', error);
      setVoiceState(null);
    }
  }, [debate.id]);

  // Load time blocks and voice state on component mount
  useEffect(() => {
    if (user) {
      loadTimeBlocks();
      loadVoiceState();
    }
  }, [user, loadTimeBlocks, loadVoiceState]);





  // Socket event listeners for voice debate updates
  useEffect(() => {
    if (!socket) {
      console.log('No socket available for voice debate updates');
      return;
    }

    console.log('Setting up voice debate socket listeners for debate:', debate.id);

    const handleVoiceUpdate = (data: any) => {
      console.log('Received voice-queue-updated event:', data);
      if (data.debateId === debate.id) {
        console.log('Voice queue updated for current debate:', data);
        console.log('Current voiceState:', voiceState);
        console.log('New speakerQueue:', data.speakerQueue);

        // Update voice state directly instead of full refresh
        if (data.speakerQueue) {
          if (voiceState) {
            console.log('Updating existing voice state');
            setVoiceState(prev => prev ? {
              ...prev,
              speakerQueue: data.speakerQueue,
              isVoiceActive: data.isVoiceActive !== undefined ? data.isVoiceActive : prev.isVoiceActive
            } : null);
          } else {
            console.log('No voice state exists, triggering full refresh');
            onRefresh();
          }
        } else {
          // Fallback to full refresh if needed
          console.log('No speakerQueue in data, falling back to full refresh');
          onRefresh();
        }
      } else {
        console.log('Event for different debate, ignoring');
      }
    };

    const handleTimeUpdate = (data: any) => {
      if (data.debateId === debate.id) {
        setCurrentSpeakerTime(data.timeRemaining);
        setTimeWarning('none');
      }
    };

    const handleTimeWarning = (data: any) => {
      if (data.debateId === debate.id) {
        setTimeWarning('warning');
      }
    };

    const handleTimeCritical = (data: any) => {
      if (data.debateId === debate.id) {
        setTimeWarning('critical');
      }
    };

    const handleTimeUp = (data: any) => {
      if (data.debateId === debate.id) {
        setCurrentSpeakerTime(0);
        setTimeWarning('none');
        onRefresh();
      }
    };

    const handleTimePaused = (data: any) => {
      if (data.debateId === debate.id) {
        // Visual indication that time is paused
        console.log('Time tracking paused');
      }
    };

    const handleTimeResumed = (data: any) => {
      if (data.debateId === debate.id) {
        setCurrentSpeakerTime(data.timeRemaining);
        console.log('Time tracking resumed');
      }
    };

    const handleTimeBlockUsed = (data: any) => {
      if (data.debateId === debate.id && data.userId === user?.id) {
        // Update user's time block balance
        setTimeBlocksRemaining(data.timeBlocksRemaining);
        setTimeBlocksUsed(prev => prev + 1);
        console.log('Time block used, remaining:', data.timeBlocksRemaining);
      }
    };

    socket.on('voice-queue-updated', handleVoiceUpdate);
    socket.on('speaker-changed', handleVoiceUpdate);
    socket.on('speaker-time-up', handleTimeUp);
    socket.on('speaker-rotated', handleVoiceUpdate);
    socket.on('voice-debate-stopped', handleVoiceUpdate);
    socket.on('time-allocated', handleVoiceUpdate);
    socket.on('time-block-used', handleTimeBlockUsed);
    socket.on('speaker-time-update', handleTimeUpdate);
    socket.on('speaker-time-warning', handleTimeWarning);
    socket.on('speaker-time-critical', handleTimeCritical);
    socket.on('speaker-time-paused', handleTimePaused);
    socket.on('speaker-time-resumed', handleTimeResumed);

    return () => {
      console.log('Cleaning up voice debate socket listeners');
      socket.off('voice-queue-updated', handleVoiceUpdate);
      socket.off('speaker-changed', handleVoiceUpdate);
      socket.off('speaker-time-up', handleTimeUp);
      socket.off('speaker-rotated', handleVoiceUpdate);
      socket.off('voice-debate-stopped', handleVoiceUpdate);
      socket.off('time-allocated', handleVoiceUpdate);
      socket.off('time-block-used', handleTimeBlockUsed);
      socket.off('speaker-time-update', handleTimeUpdate);
      socket.off('speaker-time-warning', handleTimeWarning);
      socket.off('speaker-time-critical', handleTimeCritical);
      socket.off('speaker-time-paused', handleTimePaused);
      socket.off('speaker-time-resumed', handleTimeResumed);
    };
  }, [socket, debate.id, onRefresh, voiceState]);

  // Show microphone permission status if needed
  const showMicrophoneStatus = () => {
    if (permission === 'denied') {
      return (
        <div className="bg-warning/10 border border-warning rounded-lg p-4 mb-6 text-center">
          <p className="text-warning mb-2">⚠️ Microphone access denied</p>
          <p className="text-sm text-brand-text-light mb-3">Voice features require microphone permission</p>
          <button
            onClick={requestMicPermission}
            className="bg-warning hover:bg-warning/80 text-white px-4 py-2 rounded-lg text-sm"
          >
            Request Permission Again
          </button>
        </div>
      );
    }
    return null;
  };

  // Helper function to get total queue count
  const getTotalQueueCount = () => {
    if (!voiceState) return 0;
    return voiceState.speakerQueue.FOR.length + voiceState.speakerQueue.AGAINST.length;
  };

  // Get current speaker
  const getCurrentSpeaker = () => {
    return voiceState?.currentSpeaker || null;
  };



  // Give time to a speaker using time blocks
  const giveTime = async (speakerId: string) => {
    if (!user || timeBlocksRemaining <= 0) return;

    try {
      const response = await debatesAPI.giveTime(debate.id, speakerId);
      if (response.success) {
        // Update time blocks locally
        setTimeBlocksRemaining(prev => prev - 1);
        setTimeBlocksUsed(prev => prev + 1);

        // Refresh debate state to show updated speaker time
        onRefresh();

        alert(response.message);
      } else {
        alert(response.message || 'Failed to give time');
      }
    } catch (error: any) {
      console.error('Failed to give time:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Unknown error';
      alert(`Failed to give time: ${errorMessage}`);
    }
  };



  // Compact voice interface for all debate types
  const renderCompactVoiceInterface = () => {
    if (!voiceState && !canJoinVoice && !canManageVoice) {
      return null; // Don't show anything if user can't interact
    }

    return (
      <div className="bg-brand-surface rounded-lg p-4 border border-brand-surface-light">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3">
            <MicrophoneIcon className="w-5 h-5 text-brand-primary" />
            <h3 className="text-lg font-semibold">Voice Debate</h3>
            {voiceState && (
              <span className="text-sm text-brand-text-light">
                ({getTotalQueueCount()} in queue)
              </span>
            )}
            {user && (
              <span className="text-sm bg-brand-primary/10 text-brand-primary px-2 py-1 rounded">
                {timeBlocksRemaining} time blocks
              </span>
            )}
          </div>

          {/* Quick action buttons */}
          <div className="flex items-center gap-2">
            {!voiceState ? (
              <>
                {canJoinVoice && (
                  <button
                    onClick={() => joinVoiceQueue(debate.type === 'one-on-one' ? getUserSideForOneOnOne() : 'FOR')}
                    className="bg-brand-primary hover:bg-brand-primary-hover text-white px-3 py-1.5 rounded text-sm font-medium"
                  >
                    Join Voice
                  </button>
                )}
                {canManageVoice && (
                  <button
                    onClick={initializeVoice}
                    className="bg-brand-surface-light hover:bg-brand-surface text-white px-3 py-1.5 rounded text-sm"
                  >
                    Enable
                  </button>
                )}
              </>
            ) : (
              <>
                {isInVoiceQueue ? (
                  <button
                    onClick={leaveVoiceQueue}
                    className="bg-error hover:bg-error/80 text-white px-3 py-1.5 rounded text-sm font-medium"
                  >
                    Leave Queue
                  </button>
                ) : canJoinVoice && (
                  <div className="flex gap-1">
                    {debate.type === 'open' ? (
                      <>
                        <button
                          onClick={() => joinVoiceQueue('FOR')}
                          className="hover:bg-green-500/20 text-green-500 border border-green-500 px-2 py-1.5 rounded text-xs bg-transparent"
                        >
                          Join For
                        </button>
                        <button
                          onClick={() => joinVoiceQueue('AGAINST')}
                          className="hover:bg-red-500/20 text-red-500 border border-red-500 px-2 py-1.5 rounded text-xs bg-transparent"
                        >
                          Join Against
                        </button>
                      </>
                    ) : (
                      <button
                        onClick={() => joinVoiceQueue(getUserSideForOneOnOne())}
                        className="bg-brand-primary hover:bg-brand-primary-hover text-white px-3 py-1.5 rounded text-sm"
                      >
                        Join Voice
                      </button>
                    )}
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Microphone permission status */}
        {showMicrophoneStatus()}

        {/* Voice state display */}
        {voiceState && renderVoiceState()}
      </div>
    );
  };

  // Compact voice state renderer for scalability
  const renderVoiceState = () => {
    if (!voiceState) return null;

    const currentSpeaker = getCurrentSpeaker();
    const forQueue = voiceState.speakerQueue.FOR;
    const againstQueue = voiceState.speakerQueue.AGAINST;

    return (
      <div className="space-y-3">
        {/* Voice Debate Status */}
        {voiceState.isVoiceActive ? (
          <div className="bg-brand-primary/10 rounded-lg p-3 border border-brand-primary/20">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-brand-primary rounded-full animate-pulse"></div>
                <span className="font-medium">Voice Debate Active</span>
                <span className="text-sm text-brand-text-light">
                  Both speakers can talk - mutual exclusion enabled
                </span>
              </div>
            </div>
          </div>
        ) : (forQueue.length > 0 || againstQueue.length > 0) ? (
          <div className="bg-yellow-500/10 rounded-lg p-3 border border-yellow-500/20">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <span className="font-medium text-yellow-600">Voice Debate Waiting</span>
              <span className="text-sm text-brand-text-light">
                Need speakers on both sides to start
              </span>
            </div>
          </div>
        ) : null}

        {/* Queue Summary */}
        <div className="grid grid-cols-2 gap-3">
          {/* For Queue */}
          <div className="bg-green-500/10 rounded-lg p-3 border border-green-500/20">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-green-500">Supporting ({forQueue.length})</h4>
              {forQueue.length > 3 && (
                <span className="text-xs text-brand-text-light">+{forQueue.length - 3} more</span>
              )}
            </div>
            <div className="space-y-1">
              {forQueue.slice(0, 3).map((speaker, index) => (
                <div key={speaker.participant.id} className={`flex items-center justify-between text-sm ${speaker.isSpeaking ? 'bg-green-500/20 rounded px-2 py-1 border border-green-500/30' : ''}`}>
                  <div className="flex items-center gap-2">
                    {speaker.isSpeaking && (
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    )}
                    <span className="truncate">{index + 1}. {speaker.participant.name}</span>
                    {speaker.isMuted && (
                      <span className="text-xs text-red-500">(muted)</span>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {speaker.isSpeaking ? (
                      <div className="flex items-center gap-1">
                        <span className="text-lg font-bold text-green-600 font-mono">
                          {Math.floor(speaker.timeRemaining / 60)}:{(speaker.timeRemaining % 60).toString().padStart(2, '0')}
                        </span>
                        <span className="text-xs text-green-500">remaining</span>
                      </div>
                    ) : (
                      <span className="text-xs text-brand-text-light">
                        {Math.floor(speaker.timeAllocated / 60)}m
                      </span>
                    )}
                    {user && timeBlocksRemaining > 0 && (
                      <button
                        onClick={() => giveTime(speaker.participant.id)}
                        className="p-1 hover:bg-green-500/20 rounded transition-colors"
                        title="Give 1 minute (uses 1 time block)"
                      >
                        <ClockIcon className="w-3 h-3 text-green-500" />
                      </button>
                    )}
                  </div>
                </div>
              ))}
              {forQueue.length === 0 && (
                <span className="text-xs text-brand-text-light">No speakers in queue</span>
              )}
            </div>
          </div>

          {/* Against Queue */}
          <div className="bg-red-500/10 rounded-lg p-3 border border-red-500/20">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-red-500">Opposing ({againstQueue.length})</h4>
              {againstQueue.length > 3 && (
                <span className="text-xs text-brand-text-light">+{againstQueue.length - 3} more</span>
              )}
            </div>
            <div className="space-y-1">
              {againstQueue.slice(0, 3).map((speaker, index) => (
                <div key={speaker.participant.id} className={`flex items-center justify-between text-sm ${speaker.isSpeaking ? 'bg-red-500/20 rounded px-2 py-1 border border-red-500/30' : ''}`}>
                  <div className="flex items-center gap-2">
                    {speaker.isSpeaking && (
                      <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                    )}
                    <span className="truncate">{index + 1}. {speaker.participant.name}</span>
                    {speaker.isMuted && (
                      <span className="text-xs text-red-500">(muted)</span>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {speaker.isSpeaking ? (
                      <div className="flex items-center gap-1">
                        <span className="text-lg font-bold text-red-600 font-mono">
                          {Math.floor(speaker.timeRemaining / 60)}:{(speaker.timeRemaining % 60).toString().padStart(2, '0')}
                        </span>
                        <span className="text-xs text-red-500">remaining</span>
                      </div>
                    ) : (
                      <span className="text-xs text-brand-text-light">
                        {Math.floor(speaker.timeAllocated / 60)}m
                      </span>
                    )}
                    {user && timeBlocksRemaining > 0 && (
                      <button
                        onClick={() => giveTime(speaker.participant.id)}
                        className="p-1 hover:bg-red-500/20 rounded transition-colors"
                        title="Give 1 minute (uses 1 time block)"
                      >
                        <ClockIcon className="w-3 h-3 text-red-500" />
                      </button>
                    )}
                  </div>
                </div>
              ))}
              {againstQueue.length === 0 && (
                <span className="text-xs text-brand-text-light">No speakers in queue</span>
              )}
            </div>
          </div>
        </div>


      </div>
    );
  };

  // Add a simple test to ensure component is rendering
  console.log('LiveDebateStage is rendering for debate:', debate.id);

  // Return the compact interface for all debate types
  return renderCompactVoiceInterface();
};

export default LiveDebateStage;
