"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserInitials = exports.getShortDisplayName = exports.getDisplayName = void 0;
const getDisplayName = (user) => {
    if (!user.privacySettings) {
        if (user.firstName && user.lastName) {
            return `${user.firstName} ${user.lastName}`;
        }
        return user.firstName || user.username;
    }
    switch (user.privacySettings.displayName) {
        case 'firstName':
            return user.firstName || user.username;
        case 'fullName':
            if (user.firstName && user.lastName) {
                return `${user.firstName} ${user.lastName}`;
            }
            return user.firstName || user.username;
        case 'username':
        default:
            return user.username;
    }
};
exports.getDisplayName = getDisplayName;
const getShortDisplayName = (user) => {
    const displayName = (0, exports.getDisplayName)(user);
    if (user.privacySettings?.displayName === 'fullName' && user.firstName) {
        return user.firstName;
    }
    return displayName;
};
exports.getShortDisplayName = getShortDisplayName;
const getUserInitials = (user) => {
    const displayName = (0, exports.getDisplayName)(user);
    if (user.privacySettings?.displayName === 'fullName' && user.firstName && user.lastName) {
        return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase();
    }
    if (displayName.length >= 2) {
        return displayName.substring(0, 2).toUpperCase();
    }
    return displayName.charAt(0).toUpperCase();
};
exports.getUserInitials = getUserInitials;
//# sourceMappingURL=userDisplay.js.map