import { Request, Response, NextFunction } from 'express';
import { AuthRequest } from '../middleware/auth';
import User from '../models/User';
import Debate from '../models/Debate';
import { ModerationItem } from '../models/ModerationQueue';

// GET /api/admin/users - Get all users with pagination and filtering
export const getUsers = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Check if user has admin role
    if (req.user?.role !== 'admin') {
      res.status(403).json({ success: false, message: 'Admin access required' });
      return;
    }

    const { page = 1, limit = 20, role, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
    
    const skip = (Number(page) - 1) * Number(limit);
    
    // Build filter query
    const filter: any = {};
    if (role && role !== 'all') {
      filter.role = role;
    }
    if (search) {
      filter.$or = [
        { username: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort query
    const sort: any = {};
    sort[sortBy as string] = sortOrder === 'asc' ? 1 : -1;

    const users = await User.find(filter)
      .select('-password')
      .sort(sort)
      .skip(skip)
      .limit(Number(limit));

    const total = await User.countDocuments(filter);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

// GET /api/admin/users/:id - Get user details
export const getUserDetails = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Check if user has admin role
    if (req.user?.role !== 'admin') {
      res.status(403).json({ success: false, message: 'Admin access required' });
      return;
    }

    const { id } = req.params;
    
    const user = await User.findById(id).select('-password');
    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    // Get user's debates
    const debates = await Debate.find({
      $or: [
        { 'proponent.id': id },
        { 'opponent.id': id }
      ]
    }).select('title status createdAt');

    // Get moderation history
    const moderationHistory = await ModerationItem.find({
      $or: [
        { reportedBy: id },
        { 'commentAuthor.id': id }
      ]
    }).sort({ reportedAt: -1 }).limit(10);

    res.json({
      success: true,
      data: {
        user,
        debates,
        moderationHistory
      }
    });
  } catch (error) {
    next(error);
  }
};

// PUT /api/admin/users/:id/role - Update user role
export const updateUserRole = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Check if user has admin role
    if (req.user?.role !== 'admin') {
      res.status(403).json({ success: false, message: 'Admin access required' });
      return;
    }

    const { id } = req.params;
    const { role } = req.body;

    if (!['user', 'moderator', 'admin'].includes(role)) {
      res.status(400).json({
        success: false,
        message: 'Invalid role. Must be user, moderator, or admin'
      });
      return;
    }

    const user = await User.findByIdAndUpdate(
      id,
      { role },
      { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    res.json({
      success: true,
      data: user,
      message: `User role updated to ${role}`
    });
  } catch (error) {
    next(error);
  }
};

// PUT /api/admin/users/:id/suspend - Suspend/unsuspend user
export const suspendUser = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Check if user has admin role
    if (req.user?.role !== 'admin') {
      res.status(403).json({ success: false, message: 'Admin access required' });
      return;
    }

    const { id } = req.params;
    const { suspended, reason } = req.body;

    // Validate that reason is provided when suspending
    if (suspended && !reason) {
      res.status(400).json({
        success: false,
        message: 'Suspension reason is required when suspending a user'
      });
      return;
    }

    const user = await User.findById(id);
    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    // Add suspension fields to user model if they don't exist
    const updateData: any = {
      suspended: suspended,
      suspensionReason: suspended ? reason : undefined,
      suspendedAt: suspended ? new Date() : undefined,
      suspendedBy: suspended ? req.user!.id : undefined
    };

    const updatedUser = await User.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password');

    res.json({
      success: true,
      data: updatedUser,
      message: suspended ? 'User suspended successfully' : 'User unsuspended successfully'
    });
  } catch (error) {
    next(error);
  }
};

// GET /api/admin/stats - Get admin dashboard statistics
export const getAdminStats = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Check if user has admin role
    if (req.user?.role !== 'admin') {
      res.status(403).json({ success: false, message: 'Admin access required' });
      return;
    }

    const [
      totalUsers,
      totalDebates,
      activeDebates,
      pendingModerationItems,
      totalModerationItems,
      usersToday,
      debatesToday
    ] = await Promise.all([
      User.countDocuments(),
      Debate.countDocuments(),
      Debate.countDocuments({ status: 'active' }),
      ModerationItem.countDocuments({ status: 'pending' }),
      ModerationItem.countDocuments(),
      User.countDocuments({
        createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      }),
      Debate.countDocuments({
        createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      })
    ]);

    // Get user role distribution
    const userRoles = await User.aggregate([
      { $group: { _id: '$role', count: { $sum: 1 } } }
    ]);

    // Get recent activity
    const recentUsers = await User.find()
      .select('username email role createdAt')
      .sort({ createdAt: -1 })
      .limit(5);

    const recentDebates = await Debate.find()
      .select('title status createdAt proponent')
      .sort({ createdAt: -1 })
      .limit(5);

    res.json({
      success: true,
      data: {
        overview: {
          totalUsers,
          totalDebates,
          activeDebates,
          pendingModerationItems,
          totalModerationItems,
          usersToday,
          debatesToday
        },
        userRoles: userRoles.reduce((acc, role) => {
          acc[role._id] = role.count;
          return acc;
        }, {}),
        recentActivity: {
          users: recentUsers,
          debates: recentDebates
        }
      }
    });
  } catch (error) {
    next(error);
  }
};
