"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserDebates = exports.getUserById = exports.getUsers = void 0;
const User_1 = __importDefault(require("../models/User"));
const Debate_1 = __importDefault(require("../models/Debate"));
const getUsers = async (req, res, next) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        const search = req.query.search;
        const filter = {};
        if (search) {
            filter.$or = [
                { username: { $regex: search, $options: 'i' } },
                { name: { $regex: search, $options: 'i' } }
            ];
        }
        const users = await User_1.default.find(filter)
            .select('-password')
            .sort({ reputation: -1, createdAt: -1 })
            .skip(skip)
            .limit(limit);
        const total = await User_1.default.countDocuments(filter);
        res.json({
            success: true,
            data: users,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getUsers = getUsers;
const getUserById = async (req, res, next) => {
    try {
        const user = await User_1.default.findById(req.params.id).select('-password');
        if (!user) {
            res.status(404).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        res.json({
            success: true,
            data: user
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getUserById = getUserById;
const getUserDebates = async (req, res, next) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        const type = req.query.type;
        const user = await User_1.default.findById(req.params.id);
        if (!user) {
            res.status(404).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        let filter = {};
        if (type === 'created') {
            filter = { _id: { $in: user.debatesCreated } };
        }
        else if (type === 'participated') {
            filter = { _id: { $in: user.debatesParticipated } };
        }
        else {
            filter = {
                _id: {
                    $in: [...user.debatesCreated, ...user.debatesParticipated]
                }
            };
        }
        const debates = await Debate_1.default.find(filter)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit);
        const total = await Debate_1.default.countDocuments(filter);
        res.json({
            success: true,
            data: debates,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getUserDebates = getUserDebates;
//# sourceMappingURL=userController.js.map