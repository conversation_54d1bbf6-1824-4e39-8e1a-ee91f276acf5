"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoiceDebateService = void 0;
const Debate_1 = __importDefault(require("../models/Debate"));
const User_1 = __importDefault(require("../models/User"));
const server_1 = require("../server");
class VoiceDebateService {
    static async initializeVoiceDebate(debateId, settings) {
        try {
            const debate = await Debate_1.default.findById(debateId);
            if (!debate)
                return null;
            const defaultSettings = {
                isVoiceEnabled: true,
                defaultSpeakingTime: 120,
                maxSpeakersPerSide: 5,
                allowSpectatorVoice: debate.type === 'open',
                autoRotateSpeakers: true,
                mutualExclusionEnabled: true
            };
            debate.voiceDebate = {
                settings: { ...defaultSettings, ...settings },
                speakerQueue: {
                    FOR: [],
                    AGAINST: []
                },
                isVoiceActive: false
            };
            await debate.save();
            return debate;
        }
        catch (error) {
            console.error('Error initializing voice debate:', error);
            return null;
        }
    }
    static async joinVoiceQueue(debateId, participant, side, timeAllocation) {
        try {
            const debate = await Debate_1.default.findById(debateId);
            if (!debate || !debate.voiceDebate) {
                return { success: false, message: 'Voice debate not available' };
            }
            if (debate.type === 'one-on-one') {
                const isAuthorized = participant.id === debate.proponent.id ||
                    (debate.opponent && participant.id === debate.opponent.id);
                if (!isAuthorized) {
                    return { success: false, message: 'Only debate participants can join voice in one-on-one debates' };
                }
            }
            const existingSpeaker = [
                ...debate.voiceDebate.speakerQueue.FOR,
                ...debate.voiceDebate.speakerQueue.AGAINST
            ].find(speaker => speaker.participant.id === participant.id);
            if (existingSpeaker) {
                return { success: false, message: 'Already in voice queue' };
            }
            const currentQueueSize = debate.voiceDebate.speakerQueue[side].length;
            if (currentQueueSize >= debate.voiceDebate.settings.maxSpeakersPerSide) {
                return { success: false, message: `Queue is full for ${side} side` };
            }
            const allocatedTime = timeAllocation || debate.voiceDebate.settings.defaultSpeakingTime;
            const newSpeaker = {
                participant,
                side,
                timeAllocated: allocatedTime,
                timeUsed: 0,
                timeRemaining: allocatedTime,
                isSpeaking: false,
                isMuted: false,
                joinedAt: new Date()
            };
            debate.voiceDebate.speakerQueue[side].push(newSpeaker);
            const forQueue = debate.voiceDebate.speakerQueue.FOR;
            const againstQueue = debate.voiceDebate.speakerQueue.AGAINST;
            if (!debate.voiceDebate.isVoiceActive && forQueue.length > 0 && againstQueue.length > 0) {
                debate.voiceDebate.isVoiceActive = true;
                debate.voiceDebate.voiceStartedAt = new Date();
                forQueue[0].isSpeaking = true;
                forQueue[0].isMuted = false;
                againstQueue[0].isSpeaking = true;
                againstQueue[0].isMuted = false;
                this.startSpeakerTimer(debateId, forQueue[0]);
                this.startSpeakerTimer(debateId, againstQueue[0]);
            }
            await debate.save();
            server_1.io.to(`debate-${debateId}`).emit('voice-queue-updated', {
                debateId,
                speakerQueue: debate.voiceDebate.speakerQueue,
                currentSpeaker: debate.voiceDebate.currentSpeaker,
                isVoiceActive: debate.voiceDebate.isVoiceActive,
                voiceStartedAt: debate.voiceDebate.voiceStartedAt,
                newSpeaker
            });
            return { success: true, message: 'Joined voice queue successfully', speaker: newSpeaker };
        }
        catch (error) {
            console.error('Error joining voice queue:', error);
            return { success: false, message: 'Failed to join voice queue' };
        }
    }
    static async leaveVoiceQueue(debateId, participantId) {
        try {
            const debate = await Debate_1.default.findById(debateId);
            if (!debate || !debate.voiceDebate) {
                return { success: false, message: 'Voice debate not available' };
            }
            let removed = false;
            ['FOR', 'AGAINST'].forEach(side => {
                const queue = debate.voiceDebate.speakerQueue[side];
                const index = queue.findIndex(speaker => speaker.participant.id === participantId);
                if (index !== -1) {
                    queue.splice(index, 1);
                    removed = true;
                }
            });
            if (debate.voiceDebate.currentSpeaker?.participant.id === participantId) {
                this.stopSpeakerTimer(debateId);
                debate.voiceDebate.currentSpeaker = undefined;
                await this.rotateToNextSpeaker(debateId);
            }
            if (removed) {
                const forQueue = debate.voiceDebate.speakerQueue.FOR;
                const againstQueue = debate.voiceDebate.speakerQueue.AGAINST;
                if (forQueue.length === 0 || againstQueue.length === 0) {
                    debate.voiceDebate.isVoiceActive = false;
                    [...forQueue, ...againstQueue].forEach(speaker => {
                        speaker.isSpeaking = false;
                        speaker.isMuted = true;
                    });
                }
                await debate.save();
                console.log(`Emitting voice-queue-updated for debate ${debateId}, removed participant ${participantId}`);
                console.log('Updated speaker queues:', {
                    FOR: debate.voiceDebate.speakerQueue.FOR.map(s => ({ id: s.participant.id, name: s.participant.name })),
                    AGAINST: debate.voiceDebate.speakerQueue.AGAINST.map(s => ({ id: s.participant.id, name: s.participant.name }))
                });
                server_1.io.to(`debate-${debateId}`).emit('voice-queue-updated', {
                    debateId,
                    speakerQueue: debate.voiceDebate.speakerQueue,
                    isVoiceActive: debate.voiceDebate.isVoiceActive,
                    removedParticipantId: participantId
                });
                return { success: true, message: 'Left voice queue successfully' };
            }
            return { success: false, message: 'Not in voice queue' };
        }
        catch (error) {
            console.error('Error leaving voice queue:', error);
            return { success: false, message: 'Failed to leave voice queue' };
        }
    }
    static async handleMutualExclusion(debateId) {
        try {
            const debate = await Debate_1.default.findById(debateId);
            if (!debate || !debate.voiceDebate) {
                return { success: false, message: 'Voice debate not available' };
            }
            if (!debate.voiceDebate.settings.mutualExclusionEnabled) {
                return { success: false, message: 'Mutual exclusion not enabled' };
            }
            const forQueue = debate.voiceDebate.speakerQueue.FOR;
            const againstQueue = debate.voiceDebate.speakerQueue.AGAINST;
            const forSpeaker = forQueue.find(s => s.isSpeaking);
            const againstSpeaker = againstQueue.find(s => s.isSpeaking);
            if (forSpeaker && againstSpeaker && !forSpeaker.isMuted && !againstSpeaker.isMuted) {
                forSpeaker.isMuted = true;
                againstSpeaker.isMuted = true;
                await debate.save();
                server_1.io.to(`debate-${debateId}`).emit('mutual-exclusion-triggered', {
                    debateId,
                    mutedSpeakers: [forSpeaker.participant.id, againstSpeaker.participant.id],
                    message: 'Both speakers muted due to simultaneous talking'
                });
                return { success: true, message: 'Both speakers muted due to simultaneous talking' };
            }
            return { success: false, message: 'Mutual exclusion not triggered' };
        }
        catch (error) {
            console.error('Error handling mutual exclusion:', error);
            return { success: false, message: 'Error handling mutual exclusion' };
        }
    }
    static async handleMutualExclusionRelease(debateId) {
        try {
            const debate = await Debate_1.default.findById(debateId);
            if (!debate || !debate.voiceDebate) {
                return { success: false, message: 'Voice debate not available' };
            }
            const forQueue = debate.voiceDebate.speakerQueue.FOR;
            const againstQueue = debate.voiceDebate.speakerQueue.AGAINST;
            const forSpeaker = forQueue.find(s => s.isSpeaking && s.isMuted);
            const againstSpeaker = againstQueue.find(s => s.isSpeaking && s.isMuted);
            if (forSpeaker && againstSpeaker) {
                forSpeaker.isMuted = false;
                againstSpeaker.isMuted = false;
                await debate.save();
                server_1.io.to(`debate-${debateId}`).emit('mutual-exclusion-released', {
                    debateId,
                    unmutedSpeakers: [forSpeaker.participant.id, againstSpeaker.participant.id],
                    message: 'Speakers unmuted - can resume talking'
                });
                return { success: true, message: 'Speakers unmuted' };
            }
            return { success: false, message: 'No muted speakers to release' };
        }
        catch (error) {
            console.error('Error releasing mutual exclusion:', error);
            return { success: false, message: 'Error releasing mutual exclusion' };
        }
    }
    static async rotateToNextSpeaker(debateId) {
        try {
            const debate = await Debate_1.default.findById(debateId);
            if (!debate || !debate.voiceDebate) {
                return { success: false };
            }
            let nextSpeaker;
            const { FOR, AGAINST } = debate.voiceDebate.speakerQueue;
            const currentSide = debate.voiceDebate.currentSpeaker?.side;
            if (currentSide === 'FOR' && AGAINST.length > 0) {
                nextSpeaker = AGAINST[0];
            }
            else if (currentSide === 'AGAINST' && FOR.length > 0) {
                nextSpeaker = FOR[0];
            }
            else if (FOR.length > 0) {
                nextSpeaker = FOR[0];
            }
            else if (AGAINST.length > 0) {
                nextSpeaker = AGAINST[0];
            }
            if (nextSpeaker) {
                nextSpeaker.isSpeaking = true;
                nextSpeaker.isMuted = false;
                debate.voiceDebate.currentSpeaker = nextSpeaker;
                this.startSpeakerTimer(debateId, nextSpeaker);
                await debate.save();
                server_1.io.to(`debate-${debateId}`).emit('speaker-changed', {
                    debateId,
                    currentSpeaker: nextSpeaker,
                    speakerQueue: debate.voiceDebate.speakerQueue
                });
                return { success: true, speaker: nextSpeaker };
            }
            return { success: false };
        }
        catch (error) {
            console.error('Error rotating to next speaker:', error);
            return { success: false };
        }
    }
    static startSpeakerTimer(debateId, speaker) {
        this.stopSpeakerTimer(debateId);
        const timerId = setTimeout(async () => {
            await this.handleSpeakerTimeUp(debateId, speaker.participant.id);
        }, speaker.timeRemaining * 1000);
        this.speakerTimers.set(debateId, timerId);
        this.startTimeTracking(debateId, speaker);
        server_1.io.to(`debate-${debateId}`).emit('speaker-timer-started', {
            debateId,
            speakerId: speaker.participant.id,
            timeRemaining: speaker.timeRemaining
        });
    }
    static startTimeTracking(debateId, speaker) {
        this.stopTimeTracking(debateId);
        const startTime = Date.now();
        const initialTimeRemaining = speaker.timeRemaining;
        const trackingInterval = setInterval(async () => {
            try {
                const debate = await Debate_1.default.findById(debateId);
                if (!debate || !debate.voiceDebate || !debate.voiceDebate.currentSpeaker) {
                    this.stopTimeTracking(debateId);
                    return;
                }
                const currentSpeaker = debate.voiceDebate.currentSpeaker;
                if (currentSpeaker.participant.id !== speaker.participant.id) {
                    this.stopTimeTracking(debateId);
                    return;
                }
                const elapsedSeconds = Math.floor((Date.now() - startTime) / 1000);
                const newTimeRemaining = Math.max(0, initialTimeRemaining - elapsedSeconds);
                currentSpeaker.timeRemaining = newTimeRemaining;
                currentSpeaker.timeUsed = currentSpeaker.timeAllocated - newTimeRemaining;
                await debate.save();
                server_1.io.to(`debate-${debateId}`).emit('speaker-time-update', {
                    debateId,
                    speakerId: currentSpeaker.participant.id,
                    timeRemaining: newTimeRemaining,
                    timeUsed: currentSpeaker.timeUsed
                });
                if (newTimeRemaining === 30) {
                    server_1.io.to(`debate-${debateId}`).emit('speaker-time-warning', {
                        debateId,
                        speakerId: currentSpeaker.participant.id,
                        timeRemaining: newTimeRemaining
                    });
                }
                if (newTimeRemaining === 10) {
                    server_1.io.to(`debate-${debateId}`).emit('speaker-time-critical', {
                        debateId,
                        speakerId: currentSpeaker.participant.id,
                        timeRemaining: newTimeRemaining
                    });
                }
            }
            catch (error) {
                console.error('Error in time tracking:', error);
                this.stopTimeTracking(debateId);
            }
        }, 1000);
        this.timeTrackingIntervals.set(debateId, trackingInterval);
    }
    static stopTimeTracking(debateId) {
        const intervalId = this.timeTrackingIntervals.get(debateId);
        if (intervalId) {
            clearInterval(intervalId);
            this.timeTrackingIntervals.delete(debateId);
        }
    }
    static stopSpeakerTimer(debateId) {
        const timerId = this.speakerTimers.get(debateId);
        if (timerId) {
            clearTimeout(timerId);
            this.speakerTimers.delete(debateId);
        }
        this.stopTimeTracking(debateId);
    }
    static async handleSpeakerTimeUp(debateId, speakerId) {
        try {
            const debate = await Debate_1.default.findById(debateId);
            if (!debate || !debate.voiceDebate)
                return;
            let speakerSide = null;
            let speakerRemoved = false;
            ['FOR', 'AGAINST'].forEach(side => {
                const queue = debate.voiceDebate.speakerQueue[side];
                const index = queue.findIndex(speaker => speaker.participant.id === speakerId);
                if (index !== -1) {
                    queue.splice(index, 1);
                    speakerSide = side;
                    speakerRemoved = true;
                }
            });
            if (!speakerRemoved)
                return;
            const forQueue = debate.voiceDebate.speakerQueue.FOR;
            const againstQueue = debate.voiceDebate.speakerQueue.AGAINST;
            if (forQueue.length === 0 || againstQueue.length === 0) {
                debate.voiceDebate.isVoiceActive = false;
                [...forQueue, ...againstQueue].forEach(speaker => {
                    speaker.isSpeaking = false;
                    speaker.isMuted = true;
                });
                await debate.save();
                server_1.io.to(`debate-${debateId}`).emit('voice-debate-stopped', {
                    debateId,
                    reason: 'One side has no speakers remaining',
                    speakerQueue: debate.voiceDebate.speakerQueue,
                    isVoiceActive: false
                });
            }
            else {
                const nextSpeaker = speakerSide === 'FOR' ? forQueue[0] : againstQueue[0];
                if (nextSpeaker) {
                    nextSpeaker.isSpeaking = true;
                    nextSpeaker.isMuted = false;
                    this.startSpeakerTimer(debateId, nextSpeaker);
                }
                await debate.save();
                server_1.io.to(`debate-${debateId}`).emit('speaker-rotated', {
                    debateId,
                    removedSpeakerId: speakerId,
                    newSpeaker: nextSpeaker,
                    speakerQueue: debate.voiceDebate.speakerQueue,
                    isVoiceActive: debate.voiceDebate.isVoiceActive
                });
            }
        }
        catch (error) {
            console.error('Error handling speaker time up:', error);
        }
    }
    static async allocateTime(debateId, speakerId, additionalTime, allocatedBy) {
        try {
            const debate = await Debate_1.default.findById(debateId);
            if (!debate || !debate.voiceDebate) {
                return { success: false, message: 'Voice debate not available' };
            }
            let targetSpeaker;
            ['FOR', 'AGAINST'].forEach(side => {
                const speaker = debate.voiceDebate.speakerQueue[side]
                    .find(s => s.participant.id === speakerId);
                if (speaker)
                    targetSpeaker = speaker;
            });
            if (debate.voiceDebate.currentSpeaker?.participant.id === speakerId) {
                targetSpeaker = debate.voiceDebate.currentSpeaker;
            }
            if (!targetSpeaker) {
                return { success: false, message: 'Speaker not found in voice debate' };
            }
            targetSpeaker.timeAllocated += additionalTime;
            targetSpeaker.timeRemaining += additionalTime;
            if (targetSpeaker.isSpeaking) {
                this.startSpeakerTimer(debateId, targetSpeaker);
            }
            await debate.save();
            server_1.io.to(`debate-${debateId}`).emit('time-allocated', {
                debateId,
                speakerId,
                additionalTime,
                newTimeRemaining: targetSpeaker.timeRemaining,
                allocatedBy
            });
            return { success: true, message: `Allocated ${additionalTime} seconds to speaker` };
        }
        catch (error) {
            console.error('Error allocating time:', error);
            return { success: false, message: 'Failed to allocate time' };
        }
    }
    static async pauseTimeTracking(debateId) {
        this.stopTimeTracking(debateId);
        server_1.io.to(`debate-${debateId}`).emit('speaker-time-paused', {
            debateId
        });
    }
    static async resumeTimeTracking(debateId) {
        try {
            const debate = await Debate_1.default.findById(debateId);
            if (!debate || !debate.voiceDebate || !debate.voiceDebate.currentSpeaker) {
                return;
            }
            const currentSpeaker = debate.voiceDebate.currentSpeaker;
            if (!currentSpeaker.isMuted && currentSpeaker.isSpeaking) {
                this.startTimeTracking(debateId, currentSpeaker);
                server_1.io.to(`debate-${debateId}`).emit('speaker-time-resumed', {
                    debateId,
                    speakerId: currentSpeaker.participant.id,
                    timeRemaining: currentSpeaker.timeRemaining
                });
            }
        }
        catch (error) {
            console.error('Error resuming time tracking:', error);
        }
    }
    static async handleMutualExclusionLegacy(debateId, speakingParticipants) {
        try {
            const debate = await Debate_1.default.findById(debateId);
            if (!debate || !debate.voiceDebate || !debate.voiceDebate.settings.mutualExclusionEnabled) {
                return;
            }
            if (speakingParticipants.length > 1) {
                this.pauseTimeTracking(debateId);
                ['FOR', 'AGAINST'].forEach(side => {
                    debate.voiceDebate.speakerQueue[side].forEach(speaker => {
                        if (speakingParticipants.includes(speaker.participant.id)) {
                            speaker.isMuted = true;
                            speaker.isSpeaking = false;
                        }
                    });
                });
                if (debate.voiceDebate.currentSpeaker &&
                    speakingParticipants.includes(debate.voiceDebate.currentSpeaker.participant.id)) {
                    debate.voiceDebate.currentSpeaker.isMuted = true;
                    debate.voiceDebate.currentSpeaker.isSpeaking = false;
                }
                await debate.save();
                server_1.io.to(`debate-${debateId}`).emit('mutual-exclusion-triggered', {
                    debateId,
                    mutedParticipants: speakingParticipants
                });
            }
        }
        catch (error) {
            console.error('Error handling mutual exclusion:', error);
        }
    }
    static async initializeTimeBlocks(userId, debateId) {
        try {
            const user = await User_1.default.findById(userId);
            if (!user) {
                return { success: false, message: 'User not found' };
            }
            const existingBalance = user.timeBlockBalances.find(balance => balance.debateId === debateId);
            if (existingBalance) {
                return { success: true, message: 'Time blocks already initialized' };
            }
            user.timeBlockBalances.push({
                debateId,
                timeBlocksRemaining: this.DEFAULT_TIME_BLOCKS,
                timeBlocksUsed: 0,
                lastUpdated: new Date()
            });
            await user.save();
            return { success: true, message: `Initialized ${this.DEFAULT_TIME_BLOCKS} time blocks` };
        }
        catch (error) {
            console.error('Error initializing time blocks:', error);
            return { success: false, message: 'Failed to initialize time blocks' };
        }
    }
    static async getTimeBlockBalance(userId, debateId) {
        try {
            const user = await User_1.default.findById(userId);
            if (!user)
                return null;
            const balance = user.timeBlockBalances.find(balance => balance.debateId === debateId);
            return balance ? {
                timeBlocksRemaining: balance.timeBlocksRemaining,
                timeBlocksUsed: balance.timeBlocksUsed
            } : null;
        }
        catch (error) {
            console.error('Error getting time block balance:', error);
            return null;
        }
    }
    static async useTimeBlock(userId, debateId, speakerId) {
        try {
            const user = await User_1.default.findById(userId);
            if (!user) {
                return { success: false, message: 'User not found' };
            }
            const balanceIndex = user.timeBlockBalances.findIndex(balance => balance.debateId === debateId);
            if (balanceIndex === -1) {
                const initResult = await this.initializeTimeBlocks(userId, debateId);
                if (!initResult.success) {
                    return initResult;
                }
                return this.useTimeBlock(userId, debateId, speakerId);
            }
            const balance = user.timeBlockBalances[balanceIndex];
            if (balance.timeBlocksRemaining <= 0) {
                return { success: false, message: 'No time blocks remaining' };
            }
            const allocateResult = await this.allocateTime(debateId, speakerId, this.TIME_BLOCK_DURATION, userId);
            if (!allocateResult.success) {
                return allocateResult;
            }
            balance.timeBlocksRemaining -= 1;
            balance.timeBlocksUsed += 1;
            balance.lastUpdated = new Date();
            await user.save();
            server_1.io.to(`debate-${debateId}`).emit('time-block-used', {
                debateId,
                userId,
                speakerId,
                timeBlocksRemaining: balance.timeBlocksRemaining,
                timeAllocated: this.TIME_BLOCK_DURATION
            });
            return { success: true, message: `Used 1 time block (${balance.timeBlocksRemaining} remaining)` };
        }
        catch (error) {
            console.error('Error using time block:', error);
            return { success: false, message: 'Failed to use time block' };
        }
    }
}
exports.VoiceDebateService = VoiceDebateService;
VoiceDebateService.speakerTimers = new Map();
VoiceDebateService.DEFAULT_TIME_BLOCKS = 5;
VoiceDebateService.TIME_BLOCK_DURATION = 60;
VoiceDebateService.timeTrackingIntervals = new Map();
//# sourceMappingURL=voiceDebateService.js.map