"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCleanupStats = exports.triggerCleanup = exports.reviewModerationItem = exports.getModerationStats = exports.getModerationQueue = void 0;
const ModerationQueue_1 = require("../models/ModerationQueue");
const messageCleanupService_1 = require("../services/messageCleanupService");
const getModerationQueue = async (req, res, next) => {
    try {
        const { status = 'pending', page = 1, limit = 20 } = req.query;
        const skip = (Number(page) - 1) * Number(limit);
        const items = await ModerationQueue_1.ModerationItem.find({ status })
            .sort({ reportedAt: -1 })
            .skip(skip)
            .limit(Number(limit));
        const total = await ModerationQueue_1.ModerationItem.countDocuments({ status });
        res.json({
            success: true,
            data: {
                items,
                pagination: {
                    page: Number(page),
                    limit: Number(limit),
                    total,
                    pages: Math.ceil(total / Number(limit))
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getModerationQueue = getModerationQueue;
const getModerationStats = async (req, res, next) => {
    try {
        const [pending, reviewed, resolved, dismissed] = await Promise.all([
            ModerationQueue_1.ModerationItem.countDocuments({ status: 'pending' }),
            ModerationQueue_1.ModerationItem.countDocuments({ status: 'reviewed' }),
            ModerationQueue_1.ModerationItem.countDocuments({ status: 'resolved' }),
            ModerationQueue_1.ModerationItem.countDocuments({ status: 'dismissed' })
        ]);
        const cleanupStats = await messageCleanupService_1.MessageCleanupService.getCleanupStats();
        res.json({
            success: true,
            data: {
                moderation: {
                    pending,
                    reviewed,
                    resolved,
                    dismissed,
                    total: pending + reviewed + resolved + dismissed
                },
                cleanup: cleanupStats
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getModerationStats = getModerationStats;
const reviewModerationItem = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { status, moderatorNotes, actionTaken } = req.body;
        const moderatorId = req.user.id;
        const item = await ModerationQueue_1.ModerationItem.findById(id);
        if (!item) {
            res.status(404).json({
                success: false,
                message: 'Moderation item not found'
            });
            return;
        }
        item.status = status;
        item.moderatorId = moderatorId;
        item.moderatorNotes = moderatorNotes;
        item.actionTaken = actionTaken;
        item.reviewedAt = new Date();
        await item.save();
        res.json({
            success: true,
            data: item,
            message: 'Moderation item updated successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.reviewModerationItem = reviewModerationItem;
const triggerCleanup = async (req, res, next) => {
    try {
        await messageCleanupService_1.MessageCleanupService.cleanupOldMessages();
        res.json({
            success: true,
            message: 'Cleanup completed successfully'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.triggerCleanup = triggerCleanup;
const getCleanupStats = async (req, res, next) => {
    try {
        const stats = await messageCleanupService_1.MessageCleanupService.getCleanupStats();
        res.json({
            success: true,
            data: stats
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getCleanupStats = getCleanupStats;
//# sourceMappingURL=moderationController.js.map